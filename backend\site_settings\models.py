from django.db import models
from django.core.exceptions import ValidationError
from django.core.files.images import get_image_dimensions
import os

def validate_image(image):
    """
    Validate that the uploaded file is a valid image.
    """
    if image:
        # Check file size (max 2MB)
        if image.size > 2 * 1024 * 1024:
            raise ValidationError("Image file too large (max 2MB)")

        # Check dimensions
        width, height = get_image_dimensions(image)
        if width > 1200 or height > 1200:
            raise ValidationError("Image dimensions too large (max 1200x1200)")

def logo_upload_path(instance, filename):
    """
    Generate the upload path for the logo.
    """
    # Get the file extension
    ext = filename.split('.')[-1]
    # Generate a new filename
    filename = f"site_logo.{ext}"
    return os.path.join('site', 'logo', filename)

def favicon_upload_path(instance, filename):
    """
    Generate the upload path for the favicon.
    """
    # Get the file extension
    ext = filename.split('.')[-1]
    # Generate a new filename
    filename = f"favicon.{ext}"
    return os.path.join('site', 'favicon', filename)

class SiteSettings(models.Model):
    """
    Model to store site-wide settings like logo and favicon.

    Only one instance of this model should exist.
    """
    site_logo = models.ImageField(
        upload_to=logo_upload_path,
        validators=[validate_image],
        blank=True,
        null=True,
        help_text="Site logo image (max 2MB, 1200x1200)"
    )
    favicon = models.ImageField(
        upload_to=favicon_upload_path,
        validators=[validate_image],
        blank=True,
        null=True,
        help_text="Site favicon (max 2MB, recommended 32x32 or 64x64)"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Site Settings"
        verbose_name_plural = "Site Settings"

    def __str__(self):
        return "Site Settings"

    def save(self, *args, **kwargs):
        """
        Ensure only one instance of SiteSettings exists.
        """
        if SiteSettings.objects.exists() and not self.pk:
            raise ValidationError("Only one instance of SiteSettings is allowed")
        return super().save(*args, **kwargs)

    def clean(self):
        """
        Additional validation for the model.
        """
        # If favicon exists, check if it's a valid favicon format
        if self.favicon:
            ext = self.favicon.name.split('.')[-1].lower()
            valid_favicon_formats = ['ico', 'png', 'jpg', 'jpeg', 'gif']
            if ext not in valid_favicon_formats:
                raise ValidationError({
                    'favicon': f"Invalid favicon format. Supported formats: {', '.join(valid_favicon_formats)}"
                })
