from rest_framework import serializers
from .models import SiteSettings

class SiteSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for the SiteSettings model.
    
    Provides serialization for site settings data with additional fields
    for the logo and favicon URLs.
    """
    logo_url = serializers.SerializerMethodField()
    favicon_url = serializers.SerializerMethodField()
    
    class Meta:
        model = SiteSettings
        fields = [
            'id', 'logo_url', 'favicon_url'
        ]
    
    def get_logo_url(self, obj):
        """Get the URL for the site logo."""
        if obj.site_logo:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.site_logo.url)
            return obj.site_logo.url
        return None
    
    def get_favicon_url(self, obj):
        """Get the URL for the favicon."""
        if obj.favicon:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.favicon.url)
            return obj.favicon.url
        return None
