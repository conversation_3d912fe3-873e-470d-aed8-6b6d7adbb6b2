#!/usr/bin/env python
"""
Authentication System Checker
=============================

Quick check of authentication system issues.
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from django.conf import settings


def check_auth_settings():
    """Check authentication settings"""
    print("🔧 Authentication Settings Check")
    print("=" * 40)
    
    print(f"DEBUG: {settings.DEBUG}")
    print(f"ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    
    # Check CORS settings
    if hasattr(settings, 'CORS_ALLOWED_ORIGINS'):
        print(f"CORS_ALLOWED_ORIGINS: {settings.CORS_ALLOWED_ORIGINS}")
    
    # Check REST Framework settings
    print(f"REST_FRAMEWORK AUTH: {settings.REST_FRAMEWORK.get('DEFAULT_AUTHENTICATION_CLASSES', [])}")
    
    # Check Djoser settings
    if hasattr(settings, 'DJOSER'):
        print(f"DJOSER LOGIN_FIELD: {settings.DJOSER.get('LOGIN_FIELD', 'username')}")
        print(f"DJOSER SEND_ACTIVATION_EMAIL: {settings.DJOSER.get('SEND_ACTIVATION_EMAIL', True)}")


def test_registration_endpoints():
    """Test all registration endpoints"""
    print("\n🧪 Testing Registration Endpoints")
    print("=" * 40)
    
    client = Client()
    
    test_data = {
        'username': 'test_reg_user',
        'email': '<EMAIL>',
        'password': 'TestPass123!',
        're_password': 'TestPass123!'
    }
    
    endpoints = [
        ('/api/accounts/register/', 'Custom Registration'),
        ('/api/auth/users/', 'Djoser Registration')
    ]
    
    for url, name in endpoints:
        print(f"\n{name} ({url}):")
        try:
            response = client.post(url, test_data, content_type='application/json')
            print(f"  Status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                print(f"  ✅ Success")
                # Clean up if user was created
                try:
                    User.objects.get(username='test_reg_user').delete()
                except User.DoesNotExist:
                    pass
            else:
                print(f"  ❌ Failed")
                print(f"  Response: {response.content.decode()[:200]}")
                
        except Exception as e:
            print(f"  ❌ Exception: {e}")


def check_signals():
    """Check if signals are working"""
    print("\n🔄 Testing User Creation Signals")
    print("=" * 40)
    
    # Create test user
    test_user = User.objects.create_user(
        username='signal_test',
        email='<EMAIL>',
        password='testpass123'
    )
    
    print(f"Created user: {test_user.username}")
    
    # Check wallet
    try:
        wallet = test_user.wallet
        print(f"✅ Wallet created: {wallet.balance} tokens")
        
        # Check signup bonus
        from wallet.models import WalletTransaction
        bonus = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='signup_bonus'
        ).first()
        
        if bonus:
            print(f"✅ Signup bonus: {bonus.amount} tokens")
        else:
            print(f"❌ No signup bonus found")
            
    except Exception as e:
        print(f"❌ Wallet error: {e}")
    
    # Check profile
    try:
        _ = test_user.profile
        print(f"✅ Profile created")
    except Exception as e:
        print(f"❌ Profile error: {e}")
    
    # Clean up
    test_user.delete()
    print(f"🗑️ Test user cleaned up")


def main():
    print("🔍 Authentication System Diagnosis")
    print("=" * 50)
    
    check_auth_settings()
    test_registration_endpoints()
    check_signals()
    
    print(f"\n📋 Quick Fixes:")
    print(f"1. If registration fails, check CORS settings")
    print(f"2. If no signup bonus, run: python production_diagnosis.py")
    print(f"3. If signals not working, check apps are in INSTALLED_APPS")


if __name__ == '__main__':
    main()
