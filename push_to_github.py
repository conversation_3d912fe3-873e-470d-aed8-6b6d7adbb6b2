#!/usr/bin/env python
"""
Push Complete Games to GitHub
============================

Push all Color Match and Memory Card game changes to both backend and frontend repositories
"""

import subprocess
import sys
import os

def run_command(command, description, cwd=None):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"Directory: {cwd or os.getcwd()}")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=cwd
        )
        
        if result.returncode == 0:
            print(f"✅ Success!")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
        else:
            print(f"❌ Failed!")
            if result.stderr.strip():
                print(f"Error: {result.stderr.strip()}")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def push_backend():
    """Push backend changes"""
    print("\n" + "="*50)
    print("🚀 PUSHING BACKEND CHANGES")
    print("="*50)
    
    backend_dir = r"C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion"
    
    # Check git status
    if not run_command("git status", "Checking backend git status", backend_dir):
        return False
    
    # Add all files
    if not run_command("git add .", "Adding all backend files", backend_dir):
        return False
    
    # Commit changes
    commit_message = "🎮 Complete Color Match and Memory Card games - Backend APIs, game logic, and database setup"
    if not run_command(f'git commit -m "{commit_message}"', "Committing backend changes", backend_dir):
        print("⚠️ No new changes to commit in backend")
    
    # Push to GitHub
    if not run_command("git push origin master", "Pushing backend to GitHub", backend_dir):
        return False
    
    print("\n✅ Backend pushed successfully!")
    return True

def push_frontend():
    """Push frontend changes"""
    print("\n" + "="*50)
    print("🚀 PUSHING FRONTEND CHANGES")
    print("="*50)
    
    frontend_dir = r"C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion\frontend"
    
    # Check git status
    if not run_command("git status", "Checking frontend git status", frontend_dir):
        return False
    
    # Add all files
    if not run_command("git add .", "Adding all frontend files", frontend_dir):
        return False
    
    # Commit changes
    commit_message = "🎮 Complete Color Match and Memory Card games - React components, services, and routing"
    if not run_command(f'git commit -m "{commit_message}"', "Committing frontend changes", frontend_dir):
        print("⚠️ No new changes to commit in frontend")
    
    # Push to GitHub
    if not run_command("git push origin master", "Pushing frontend to GitHub", frontend_dir):
        return False
    
    print("\n✅ Frontend pushed successfully!")
    return True

def main():
    """Main function"""
    print("🚀 PUSH COMPLETE GAMES TO GITHUB")
    print("=" * 60)
    print("This will push all Color Match and Memory Card game changes")
    print("to both backend and frontend GitHub repositories.")
    print("=" * 60)
    
    # Push backend
    backend_success = push_backend()
    
    # Push frontend
    frontend_success = push_frontend()
    
    # Final summary
    print("\n" + "="*60)
    print("📊 PUSH SUMMARY")
    print("="*60)
    
    if backend_success:
        print("✅ Backend: Successfully pushed to GitHub")
    else:
        print("❌ Backend: Failed to push")
    
    if frontend_success:
        print("✅ Frontend: Successfully pushed to GitHub")
    else:
        print("❌ Frontend: Failed to push")
    
    if backend_success and frontend_success:
        print("\n🎉 ALL CHANGES PUSHED SUCCESSFULLY!")
        print("=" * 40)
        
        print("\n📦 What was pushed:")
        print("\n🔧 Backend Repository:")
        print("   • color_match_api.py - Color Match API endpoints")
        print("   • memory_card_api.py - Memory Card API endpoints")
        print("   • process_game_transaction() - Token transaction function")
        print("   • Updated setup_games.py - All 5 games database setup")
        print("   • Updated game_logic.py - Complete game implementations")
        print("   • Updated ai_bot.py - AI logic for all games")
        print("   • Updated urls.py - New game routes")
        
        print("\n🎨 Frontend Repository:")
        print("   • ColorMatchGame.tsx - Complete Color Match component")
        print("   • MemoryCardGame.tsx - Complete Memory Card component")
        print("   • colorMatchService.ts - Color Match API service")
        print("   • memoryCardService.ts - Memory Card API service")
        print("   • Updated App.tsx - Game routes")
        print("   • Updated GameLobby.tsx - Fixed routing")
        
        print("\n🎮 Game Features:")
        print("   • Color Match: Stroop effect, 80% AI accuracy, 5 rounds")
        print("   • Memory Card: 4x4 grid, AI memory, turn-based matching")
        print("   • Token System: Win +5, Draw +2, Loss -1")
        print("   • Authentication: Django login required")
        print("   • Responsive: Works on all devices")
        
        print("\n🌐 Deployment:")
        print("   • Backend: https://pickmetrendofficial-render.onrender.com")
        print("   • Frontend: https://pickmetrend-frontend-render.onrender.com")
        print("   • Games: /gaming/color-match and /gaming/memory-card")
        
        print("\n💡 Next Steps:")
        print("1. Wait for Render to redeploy (2-3 minutes)")
        print("2. Run: python manage.py setup_games (on backend)")
        print("3. Test games on your website")
        print("4. Click '🤖 Play vs AI' buttons (not 'Find Opponent')")
        
    else:
        print("\n⚠️ SOME PUSHES FAILED")
        print("Please check the errors above and try again.")
    
    print("\n🎮 The complete interactive games are ready!")

if __name__ == '__main__':
    main()
