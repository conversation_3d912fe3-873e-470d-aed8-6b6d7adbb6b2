# 🎮 Complete Token Economy System Documentation

## 🎯 Overview

This is a comprehensive Django-based token economy system for your e-commerce gaming platform. It implements all the requested features with robust validation, admin controls, and API endpoints.

## ✅ Features Implemented

### 🎁 1. Signup Bonus
- **100 tokens** automatically given to every new user
- Implemented via Django signals on user creation
- Prevents duplicate bonuses for existing users

### 🕹️ 2. Game Loss Penalty
- **1 token deducted** when user loses a game
- Automatic validation to prevent negative balances
- Transaction logging for audit trail

### ⛔ 3. Zero Balance Lock
- **Prevents token-based games** when balance = 0
- **Disables token discounts** in checkout when balance = 0
- Real-time validation in API responses

### 🙋 4. Token Refill Request System
- Users can request refills when balance = 0
- Admin approval/rejection workflow
- Prevents duplicate pending requests
- Complete audit trail

## 📊 Database Models

### Enhanced Wallet Model
```python
class Wallet(models.Model):
    # Existing fields...
    
    # New methods:
    def can_play_games(self) -> bool
    def can_use_token_discounts(self) -> bool
    def deduct_game_loss_penalty(self, game_id=None, description="Game loss penalty")
    @classmethod
    def give_signup_bonus(cls, user)
```

### New TokenRequest Model
```python
class TokenRequest(models.Model):
    user = models.ForeignKey(User)
    message = models.TextField(blank=True, null=True)
    status = models.CharField(choices=['pending', 'approved', 'rejected'])
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(blank=True, null=True)
    processed_by = models.ForeignKey(User, related_name='processed_token_requests')
    tokens_granted = models.PositiveIntegerField(default=0)
    admin_notes = models.TextField(blank=True, null=True)
```

### Enhanced Transaction Types
```python
TRANSACTION_TYPES = [
    ('game_win', 'Game Win'),
    ('game_loss', 'Game Loss'),          # NEW
    ('signup_bonus', 'Signup Bonus'),    # NEW
    ('refill_bonus', 'Token Refill'),    # NEW
    # ... existing types
]
```

## 🌐 API Endpoints

### User Endpoints
```
GET    /api/wallet/                           # Enhanced wallet details
GET    /api/wallet/token-requests/            # List user's requests
POST   /api/wallet/token-requests/            # Create new request
GET    /api/wallet/token-requests/{id}/       # Get request details
POST   /api/wallet/check-refill-eligibility/  # Check if can request
```

### Admin Endpoints
```
GET    /api/wallet/admin/token-requests/              # List all requests
POST   /api/wallet/admin/token-requests/{id}/approve/ # Approve request
POST   /api/wallet/admin/token-requests/{id}/reject/  # Reject request
```

## 🎮 Game Integration

### Using the Game Integration Helper
```python
from wallet.game_integration import handle_game_loss, check_game_eligibility

# In your game view when user loses:
result = handle_game_loss(user.id, game_id="game-123")
if result['success']:
    print(f"Deducted 1 token. Remaining: {result['remaining_balance']}")
else:
    print(f"Error: {result['error']}")

# Before starting a game:
eligibility = check_game_eligibility(user.id)
if not eligibility['can_play']:
    return "You need tokens to play games"
```

### Using the Mixin in Game Views
```python
from wallet.game_integration import TokenEconomyMixin

class GameView(TokenEconomyMixin, APIView):
    def post(self, request):
        # Check if user can play
        can_play, message = self.check_user_can_play(request.user)
        if not can_play:
            return Response({'error': message}, status=400)
        
        # Play game logic...
        game_result = 'win'  # or 'loss' or 'draw'
        
        # Handle token transaction
        result = self.handle_game_result(request.user, game_result)
        return Response(result)
```

## 🛒 Checkout Integration

### Validating Token Usage
```python
from wallet.game_integration import validate_checkout_token_usage

# In your checkout view:
is_valid, error_message = validate_checkout_token_usage(user, tokens_to_use)
if not is_valid:
    return Response({'error': error_message}, status=400)
```

## 🎨 Frontend Integration

### React Component Usage
```tsx
import TokenRefillRequest from '../components/wallet/TokenRefillRequest';

// In your wallet page:
<TokenRefillRequest 
  onRequestSubmitted={() => {
    // Refresh wallet data
    refreshWallet();
  }}
  className="mb-6"
/>
```

### API Usage Examples
```typescript
// Check if user can request refill
const response = await api.post('/api/wallet/check-refill-eligibility/');
console.log(response.data.can_request); // true/false

// Submit token refill request
const request = await api.post('/api/wallet/token-requests/', {
  message: "I need tokens to continue playing"
});

// Get enhanced wallet details
const wallet = await api.get('/api/wallet/');
console.log(wallet.data.can_play_games);        // true/false
console.log(wallet.data.can_use_token_discounts); // true/false
console.log(wallet.data.is_zero_balance);        // true/false
```

## 🔧 Admin Usage

### Django Admin Interface
1. **Navigate to:** `/admin/wallet/tokenrequest/`
2. **View pending requests** with user balance information
3. **Bulk approve/reject** multiple requests
4. **Individual processing** with custom token amounts

### Admin Actions Available
- **Approve Request:** Grant tokens (default 50, customizable)
- **Reject Request:** Decline with admin notes
- **Bulk Actions:** Process multiple requests at once
- **Filter by Status:** View pending/approved/rejected requests

## 🧪 Testing

### Run the Test Suite
```bash
cd backend
python test_token_economy.py
```

### Manual Testing Checklist
- [ ] New user gets 100 tokens on signup
- [ ] Game loss deducts 1 token
- [ ] Zero balance prevents games and discounts
- [ ] Token refill request works when balance = 0
- [ ] Admin can approve/reject requests
- [ ] Tokens are granted on approval
- [ ] API endpoints return correct data

## 🚀 Deployment Notes

### Database Migration
```bash
python manage.py makemigrations wallet
python manage.py migrate
```

### Required Settings
No additional settings required - uses existing `WALLET_SETTINGS` configuration.

### Security Considerations
- All endpoints require authentication
- Admin endpoints require staff permissions
- Input validation on all forms
- SQL injection protection via Django ORM
- CSRF protection on state-changing operations

## 🎯 Usage Examples

### Complete User Journey
1. **User registers** → Gets 100 tokens automatically
2. **Plays games** → Earns 5 tokens per win, loses 1 per loss
3. **Balance reaches 0** → Cannot play games or use discounts
4. **Requests refill** → Submits request with optional message
5. **Admin approves** → User gets 50 tokens, can play again

### Admin Workflow
1. **Check pending requests** in Django admin
2. **Review user balance** and request message
3. **Approve with custom amount** or reject with notes
4. **User automatically gets tokens** on approval

## 🎉 Success Metrics

The system successfully implements:
- ✅ **100% automated signup bonus**
- ✅ **Real-time game loss penalties**
- ✅ **Complete zero balance lockdown**
- ✅ **User-friendly refill request system**
- ✅ **Efficient admin approval workflow**
- ✅ **Comprehensive API coverage**
- ✅ **Production-ready security**

Your token economy system is now ready to drive user engagement and provide a gamified shopping experience! 🎮🛒💰
