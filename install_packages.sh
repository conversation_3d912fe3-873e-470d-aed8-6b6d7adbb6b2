#!/bin/bash

# Custom Package Installation Script
# This script installs packages one by one to avoid pip auto-upgrade issues

echo "🔧 Starting custom package installation..."
echo "📁 Current directory: $(pwd)"

# Set environment variables to bypass SSL verification
export PYTHONHTTPSVERIFY=0
export SSL_VERIFY=false
export PIP_DISABLE_PIP_VERSION_CHECK=1
export CURL_CA_BUNDLE=""
export REQUESTS_CA_BUNDLE=""

echo "📦 Installing packages individually to avoid pip upgrade..."

# Define packages to install (from requirements.txt)
packages=(
    "Django==4.2.13"
    "djangorestframework==3.14.0"
    "djangorestframework-simplejwt==5.2.2"
    "djoser==2.2.0"
    "django-cors-headers==4.0.0"
    "django-filter==23.2"
    "Pillow==9.5.0"
    "psycopg2-binary==2.9.7"
    "gunicorn==20.1.0"
    "whitenoise==6.5.0"
    "python-dotenv==1.0.0"
    "dj-database-url==2.0.0"
    "cloudinary==1.36.0"
    "django-cloudinary-storage==0.3.0"
    "razorpay==1.4.1"
    "redis==4.5.5"
    "channels==4.0.0"
    "channels-redis==4.1.0"
    "django-templated-mail==1.1.1"
    "social-auth-app-django==5.4.3"
    "social-auth-core==4.6.1"
)

# Install each package individually
for package in "${packages[@]}"; do
    echo "📦 Installing: $package"
    
    # Try multiple installation methods
    pip install "$package" \
        --trusted-host pypi.org \
        --trusted-host pypi.python.org \
        --trusted-host files.pythonhosted.org \
        --no-cache-dir \
        --disable-pip-version-check \
        --no-deps || \
    python -m pip install "$package" \
        --trusted-host pypi.org \
        --trusted-host pypi.python.org \
        --trusted-host files.pythonhosted.org \
        --no-cache-dir \
        --disable-pip-version-check \
        --no-deps || \
    python -c "
import ssl
ssl._create_default_https_context = ssl._create_unverified_context
import subprocess
import sys
subprocess.check_call([sys.executable, '-m', 'pip', 'install', '$package', '--no-cache-dir', '--disable-pip-version-check', '--no-deps'])
"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Successfully installed: $package"
    else
        echo "   ❌ Failed to install: $package"
    fi
done

echo "📦 Installing dependencies for all packages..."
# Now install with dependencies
pip install -r requirements.txt \
    --trusted-host pypi.org \
    --trusted-host pypi.python.org \
    --trusted-host files.pythonhosted.org \
    --no-cache-dir \
    --disable-pip-version-check \
    --force-reinstall || \
python -c "
import ssl
ssl._create_default_https_context = ssl._create_unverified_context
import subprocess
import sys
subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt', '--no-cache-dir', '--disable-pip-version-check', '--force-reinstall'])
"

echo "✅ Package installation completed!"
echo "📋 Installed packages:"
pip list --format=freeze | head -20
