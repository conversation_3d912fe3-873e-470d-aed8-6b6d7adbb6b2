#!/usr/bin/env python
"""
Create sample products for testing the manual product system
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from products.models import Product, Category, ProductImage
from decimal import Decimal

def create_sample_products():
    print("🛍️ Creating Sample Products for PickMeTrend")
    print("=" * 50)
    
    # Create categories first
    categories_data = [
        {
            'name': 'T-Shirts',
            'slug': 't-shirts',
            'description': 'Comfortable cotton t-shirts for everyday wear',
            'is_active': True
        },
        {
            'name': 'Hoodies',
            'slug': 'hoodies',
            'description': 'Warm and cozy hoodies for all seasons',
            'is_active': True
        },
        {
            'name': 'Gaming Gear',
            'slug': 'gaming-gear',
            'description': 'Gaming-themed apparel and accessories',
            'is_active': True
        }
    ]
    
    print("📂 Creating Categories...")
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            slug=cat_data['slug'],
            defaults=cat_data
        )
        if created:
            print(f"  ✅ Created: {category.name}")
        else:
            print(f"  📁 Exists: {category.name}")
    
    # Create sample products
    products_data = [
        {
            'name': 'Cool Gaming T-Shirt',
            'slug': 'cool-gaming-t-shirt',
            'description': 'Awesome t-shirt perfect for gamers. Made from 100% cotton with a comfortable fit. Features a cool gaming design that shows your passion for gaming.',
            'price': Decimal('599.00'),
            'compare_price': Decimal('799.00'),
            'stock': 50,
            'is_active': True,
            'is_featured': True,
            'allow_token_discount': True,
            'token_discount_percentage': 30,
            'token_discount_max_amount': Decimal('100.00'),
            'categories': ['gaming-gear', 't-shirts']
        },
        {
            'name': 'Premium Cotton Hoodie',
            'slug': 'premium-cotton-hoodie',
            'description': 'Ultra-soft premium cotton hoodie with a modern fit. Perfect for casual wear or lounging. Available in multiple colors.',
            'price': Decimal('1299.00'),
            'compare_price': Decimal('1599.00'),
            'stock': 30,
            'is_active': True,
            'is_featured': True,
            'allow_token_discount': True,
            'token_discount_percentage': 25,
            'token_discount_max_amount': Decimal('200.00'),
            'categories': ['hoodies']
        },
        {
            'name': 'Classic White T-Shirt',
            'slug': 'classic-white-t-shirt',
            'description': 'Simple and elegant white t-shirt. A wardrobe essential that goes with everything. Made from high-quality cotton.',
            'price': Decimal('399.00'),
            'compare_price': Decimal('499.00'),
            'stock': 100,
            'is_active': True,
            'is_featured': False,
            'allow_token_discount': False,  # No token discount for this one
            'token_discount_percentage': 20,
            'token_discount_max_amount': None,
            'categories': ['t-shirts']
        },
        {
            'name': 'Gaming Champion Hoodie',
            'slug': 'gaming-champion-hoodie',
            'description': 'Show off your gaming skills with this champion hoodie. Features embroidered gaming logos and a comfortable hood.',
            'price': Decimal('1799.00'),
            'compare_price': Decimal('2199.00'),
            'stock': 25,
            'is_active': True,
            'is_featured': True,
            'allow_token_discount': True,
            'token_discount_percentage': 40,
            'token_discount_max_amount': Decimal('300.00'),
            'categories': ['gaming-gear', 'hoodies']
        },
        {
            'name': 'Retro Gaming T-Shirt',
            'slug': 'retro-gaming-t-shirt',
            'description': 'Nostalgic retro gaming design that brings back memories of classic arcade games. Perfect for retro gaming enthusiasts.',
            'price': Decimal('699.00'),
            'compare_price': Decimal('899.00'),
            'stock': 40,
            'is_active': True,
            'is_featured': False,
            'allow_token_discount': True,
            'token_discount_percentage': 35,
            'token_discount_max_amount': Decimal('150.00'),
            'categories': ['gaming-gear', 't-shirts']
        }
    ]
    
    print("\n🛍️ Creating Products...")
    for product_data in products_data:
        # Extract categories
        category_slugs = product_data.pop('categories')
        
        # Create or get product
        product, created = Product.objects.get_or_create(
            slug=product_data['slug'],
            defaults=product_data
        )
        
        if created:
            print(f"  ✅ Created: {product.name}")
            print(f"     Price: ₹{product.price}")
            if product.allow_token_discount:
                print(f"     Token Discount: {product.token_discount_percentage}% (max ₹{product.token_discount_max_amount})")
            else:
                print(f"     Token Discount: Not allowed")
            
            # Add categories
            for cat_slug in category_slugs:
                try:
                    category = Category.objects.get(slug=cat_slug)
                    product.categories.add(category)
                    print(f"     Category: {category.name}")
                except Category.DoesNotExist:
                    print(f"     ⚠️ Category not found: {cat_slug}")
            
            print()
        else:
            print(f"  📦 Exists: {product.name}")
    
    # Summary
    total_products = Product.objects.count()
    total_categories = Category.objects.count()
    token_eligible = Product.objects.filter(allow_token_discount=True).count()
    
    print("=" * 50)
    print("📊 Summary:")
    print(f"  📂 Categories: {total_categories}")
    print(f"  📦 Total Products: {total_products}")
    print(f"  🪙 Token Eligible: {token_eligible}")
    print(f"  💰 Regular Products: {total_products - token_eligible}")
    
    print("\n🎯 Next Steps:")
    print("1. Visit admin: http://localhost:8000/admin/products/product/")
    print("2. Add product images")
    print("3. Test the frontend")
    print("4. Configure more token discounts as needed")
    
    print("\n✅ Sample products created successfully!")

if __name__ == '__main__':
    create_sample_products()
