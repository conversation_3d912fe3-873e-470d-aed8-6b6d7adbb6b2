#!/bin/bash

echo "🚀 PUSHING BACKEND TO GITHUB"
echo "=============================="
echo

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if we're in the right directory
if [ ! -d "backend" ]; then
    echo -e "${RED}❌ ERROR: Backend folder not found!${NC}"
    echo "Please run this script from your project root directory"
    echo "Expected: /path/to/pickmetrend_gameversion"
    exit 1
fi

echo -e "${GREEN}✅ Found backend folder${NC}"
echo -e "${BLUE}📁 Navigating to backend...${NC}"
cd backend

echo -e "${BLUE}🔧 Initializing Git repository...${NC}"
git init

echo -e "${BLUE}🔗 Adding GitHub repository...${NC}"
git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git 2>/dev/null || {
    echo -e "${YELLOW}⚠️  Remote already exists, updating...${NC}"
    git remote set-url origin https://github.com/phinihas30/pickmetrendofficial-render.git
}

echo -e "${BLUE}📦 Adding all files...${NC}"
git add .

echo -e "${BLUE}💾 Creating commit...${NC}"
git commit -m "🎮 Gaming System Complete - Production Ready

✨ New Features:
- Tic Tac Toe game with hard mode AI
- Token economy: Win +5, Draw +2, Loss -1 tokens
- Real-time gaming with WebSocket support
- Wallet system with shopping integration
- Complete e-commerce API

🔧 Technical Stack:
- Django REST API with gaming endpoints
- Redis integration with Upstash
- Token transaction system
- Razorpay live payment integration
- Production-ready deployment config

🚀 Ready for Render deployment!"

echo -e "${BLUE}🌿 Setting main branch...${NC}"
git branch -M main

echo -e "${BLUE}🚀 Pushing to GitHub...${NC}"
echo
echo -e "${YELLOW}⚠️  AUTHENTICATION REQUIRED:${NC}"
echo "   Username: phinihas30"
echo "   Password: Use your Personal Access Token (NOT your GitHub password)"
echo
echo -e "${BLUE}🔑 Need a token? Go to: https://github.com/settings/tokens${NC}"
echo

if git push -u origin main; then
    echo
    echo -e "${GREEN}🎉 SUCCESS! Backend pushed to GitHub!${NC}"
    echo -e "${BLUE}📁 Check your repository: https://github.com/phinihas30/pickmetrendofficial-render${NC}"
    echo
    echo -e "${GREEN}🚀 Next Steps:${NC}"
    echo "   1. Verify files on GitHub"
    echo "   2. Deploy to Render"
    echo "   3. Push frontend code"
else
    echo
    echo -e "${RED}❌ Push failed. Common solutions:${NC}"
    echo "   1. Check your internet connection"
    echo "   2. Verify your GitHub credentials"
    echo "   3. Make sure you have repository access"
    echo
    echo -e "${BLUE}🔑 Create Personal Access Token:${NC}"
    echo "   - Go to https://github.com/settings/tokens"
    echo "   - Generate new token with 'repo' permissions"
    echo "   - Use token as password when prompted"
fi

echo
read -p "Press Enter to continue..."
