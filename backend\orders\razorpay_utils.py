import razorpay
from django.conf import settings
from decimal import Decimal
import logging
from razorpay_settings.utils import get_razorpay_keys, get_razorpay_mode, initialize_razorpay_client

# Set up logging
logger = logging.getLogger(__name__)

# Initialize Razorpay client using our utility function
client = initialize_razorpay_client()

def create_razorpay_order(order_id, amount_in_inr):
    """
    Create a Razorpay order

    Args:
        order_id: The ID of the order in our system
        amount_in_inr: The amount in INR (as Decimal)

    Returns:
        dict: The Razorpay order details
    """
    try:
        # Validate the client is properly initialized
        if not client.auth:
            print("Razorpay client not properly initialized. Check API keys.")
            raise ValueError("Razorpay client not properly initialized")

        # Ensure amount is a valid number
        try:
            amount_float = float(amount_in_inr)
            if amount_float <= 0:
                raise ValueError(f"Invalid amount: {amount_in_inr}. Amount must be positive.")
        except (ValueError, TypeError):
            print(f"Invalid amount format: {amount_in_inr}")
            raise ValueError(f"Invalid amount format: {amount_in_inr}")

        # Razorpay expects amount in paise (1 INR = 100 paise)
        amount_in_paise = int(amount_float * 100)

        print(f"Creating Razorpay order with amount: {amount_in_paise} paise ({amount_in_inr} INR)")
        print(f"Using Razorpay key ID: {client.auth[0]}")

        # Create Razorpay order
        razorpay_order = client.order.create({
            'amount': amount_in_paise,
            'currency': 'INR',
            'receipt': str(order_id),
            'payment_capture': 1,  # Auto-capture payment
            'notes': {
                'order_id': str(order_id)
            }
        })

        print(f"Razorpay order created successfully: {razorpay_order}")
        return razorpay_order
    except Exception as e:
        print(f"Error creating Razorpay order: {str(e)}")
        # Re-raise with more context
        raise Exception(f"Failed to create Razorpay order: {str(e)}")

def verify_payment_signature(payment_id, order_id, signature):
    """
    Verify the payment signature from Razorpay

    Args:
        payment_id: The Razorpay payment ID
        order_id: The Razorpay order ID
        signature: The signature received from Razorpay

    Returns:
        bool: True if signature is valid, False otherwise
    """
    try:
        # Validate inputs
        if not payment_id or not order_id or not signature:
            print("Missing required parameters for payment verification")
            return False

        # Validate the client is properly initialized
        if not client.auth:
            print("Razorpay client not properly initialized. Check API keys.")
            return False

        # Create the parameters dict
        params_dict = {
            'razorpay_payment_id': payment_id,
            'razorpay_order_id': order_id,
            'razorpay_signature': signature
        }

        # Log the verification attempt
        print(f"Verifying Razorpay payment: {params_dict}")
        print(f"Using Razorpay key ID: {client.auth[0]}")

        # Verify the signature
        result = client.utility.verify_payment_signature(params_dict)

        # If no exception is raised, the signature is valid
        # Note: verify_payment_signature returns None on success
        print(f"Razorpay verification result: {result}")
        return True
    except Exception as e:
        # Log the error
        print(f"Razorpay verification error: {str(e)}")

        # Try to get more details about the payment
        try:
            payment_details = client.payment.fetch(payment_id)
            print(f"Payment details: {payment_details}")
        except Exception as fetch_error:
            print(f"Error fetching payment details: {str(fetch_error)}")

        return False
