#!/usr/bin/env python3
"""
Production Readiness Check for PickMeTrend
Verifies all systems are ready for deployment
"""

import os
import sys
import django
from pathlib import Path

# Setup Django
sys.path.append(str(Path(__file__).parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

def check_environment_variables():
    """Check critical environment variables"""
    print("🔧 Checking Environment Variables...")
    
    critical_vars = {
        'SECRET_KEY': 'Django secret key',
        'DEBUG': 'Debug mode setting',
        'DATABASE_URL': 'Database connection',
        'SENDGRID_API_KEY': 'Email service',
        'CLOUDINARY_CLOUD_NAME': 'Media storage',
        'RAZORPAY_KEY_ID': 'Payment gateway',
        'REDIS_URL': 'Gaming system cache'
    }
    
    missing = []
    for var, description in critical_vars.items():
        if not os.environ.get(var):
            missing.append(f"   ❌ {var} - {description}")
        else:
            print(f"   ✅ {var}")
    
    if missing:
        print("\n⚠️ Missing environment variables:")
        for var in missing:
            print(var)
        return False
    
    return True

def check_database_connection():
    """Test database connectivity"""
    print("\n🗄️ Checking Database Connection...")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("   ✅ Database connection successful")
        return True
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
        return False

def check_gaming_system():
    """Verify gaming system components"""
    print("\n🎮 Checking Gaming System...")
    
    try:
        # Check gaming models
        from gaming.models import GameType, Battle
        from wallet.models import Wallet, WalletTransaction
        
        print("   ✅ Gaming models imported")
        
        # Check if game types exist
        game_types = GameType.objects.count()
        print(f"   ✅ Game types in database: {game_types}")
        
        # Check Tic Tac Toe API
        from gaming.tic_tac_toe_api import start_tic_tac_toe_game
        print("   ✅ Tic Tac Toe API available")
        
        return True
    except Exception as e:
        print(f"   ❌ Gaming system check failed: {e}")
        return False

def check_wallet_system():
    """Verify wallet and token system"""
    print("\n💰 Checking Wallet System...")
    
    try:
        from wallet.models import Wallet
        from wallet.game_integration import check_game_eligibility
        
        print("   ✅ Wallet models imported")
        print("   ✅ Game integration functions available")
        
        return True
    except Exception as e:
        print(f"   ❌ Wallet system check failed: {e}")
        return False

def check_redis_connection():
    """Test Redis connectivity for gaming (Upstash)"""
    print("\n🔴 Checking Upstash Redis Connection...")

    try:
        from django.conf import settings
        redis_url = settings.REDIS_URL

        # Use the same connection function as Django settings
        r = settings.get_redis_connection()
        r.ping()

        if 'upstash.io' in redis_url:
            print("   ✅ Upstash Redis connection successful")
        else:
            print("   ✅ Redis connection successful")

        # Test basic operations
        r.set('test_key', 'test_value', ex=10)
        value = r.get('test_key')
        if value == 'test_value':
            print("   ✅ Redis read/write operations working")

        return True
    except Exception as e:
        print(f"   ❌ Redis connection failed: {e}")
        print("   ⚠️ Gaming WebSocket features may not work")
        return False

def check_static_files():
    """Verify static file configuration"""
    print("\n📁 Checking Static Files...")
    
    try:
        from django.conf import settings
        from django.core.management import call_command
        
        print(f"   ✅ STATIC_URL: {settings.STATIC_URL}")
        print(f"   ✅ STATIC_ROOT: {settings.STATIC_ROOT}")
        
        # Test collectstatic (dry run)
        call_command('collectstatic', '--dry-run', '--noinput', verbosity=0)
        print("   ✅ Static files collection test passed")
        
        return True
    except Exception as e:
        print(f"   ❌ Static files check failed: {e}")
        return False

def check_email_configuration():
    """Test email configuration"""
    print("\n📧 Checking Email Configuration...")
    
    try:
        from django.core.mail import send_mail
        from django.conf import settings
        
        print(f"   ✅ Email backend: {settings.EMAIL_BACKEND}")
        print(f"   ✅ Default from email: {settings.DEFAULT_FROM_EMAIL}")
        
        if not settings.DEBUG:
            print("   ✅ Production email configuration active")
        else:
            print("   ⚠️ Development email configuration (console backend)")
        
        return True
    except Exception as e:
        print(f"   ❌ Email configuration check failed: {e}")
        return False

def check_security_settings():
    """Verify security configuration"""
    print("\n🔒 Checking Security Settings...")
    
    from django.conf import settings
    
    checks = []
    
    # Debug mode
    if not settings.DEBUG:
        checks.append("   ✅ DEBUG=False (production mode)")
    else:
        checks.append("   ⚠️ DEBUG=True (development mode)")
    
    # Secret key
    if settings.SECRET_KEY and len(settings.SECRET_KEY) > 20:
        checks.append("   ✅ Secret key configured")
    else:
        checks.append("   ❌ Secret key missing or too short")
    
    # Allowed hosts
    if settings.ALLOWED_HOSTS and '.onrender.com' in str(settings.ALLOWED_HOSTS):
        checks.append("   ✅ Allowed hosts configured for Render")
    else:
        checks.append("   ⚠️ Allowed hosts may need Render domains")
    
    for check in checks:
        print(check)
    
    return all("✅" in check for check in checks)

def main():
    """Run all production readiness checks"""
    print("🚀 PickMeTrend Production Readiness Check")
    print("=" * 50)
    
    checks = [
        check_environment_variables,
        check_database_connection,
        check_gaming_system,
        check_wallet_system,
        check_redis_connection,
        check_static_files,
        check_email_configuration,
        check_security_settings
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
        except Exception as e:
            print(f"   ❌ Check failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! Ready for production deployment!")
        print("\n🚀 Next steps:")
        print("1. Commit all changes to Git")
        print("2. Push to your repository")
        print("3. Deploy to Render using the blueprint")
        print("4. Monitor deployment logs")
        print("5. Test the live application")
    else:
        print(f"\n⚠️ {total - passed} checks failed. Please fix the issues above.")
        print("Review the PRODUCTION_DEPLOYMENT_GUIDE.md for help.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
