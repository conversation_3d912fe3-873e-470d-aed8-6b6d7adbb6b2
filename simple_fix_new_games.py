#!/usr/bin/env python
"""
Simple Fix for New Games
========================

This script creates the missing game types and tests AI battle creation.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType
from django.contrib.auth.models import User
from wallet.models import Wallet


def create_missing_game_types():
    """Create the missing Color Match and Memory Card game types"""
    print("🎮 Creating Missing Game Types")
    print("=" * 35)
    
    # Define the new games
    new_games = [
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember color sequences - Play vs AI or human opponents!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs - Play vs AI or human opponents!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼']
            }
        }
    ]
    
    created_count = 0
    
    for game_data in new_games:
        game_type, created = GameType.objects.get_or_create(
            name=game_data['name'],
            defaults={
                'display_name': game_data['display_name'],
                'description': game_data['description'],
                'rules': game_data['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created: {game_type.display_name}")
            created_count += 1
        else:
            print(f"✅ Already exists: {game_type.display_name}")
    
    print(f"\nCreated {created_count} new game types")
    return created_count


def test_simple_battle_creation():
    """Test simple battle creation without async"""
    print("\n🧪 Testing Simple Battle Creation")
    print("=" * 35)
    
    from gaming.models import Battle
    
    # Get a test user
    test_user = User.objects.first()
    if not test_user:
        print("❌ No users found")
        return False
    
    print(f"Testing with user: {test_user.username}")
    
    # Ensure user has tokens
    try:
        wallet = test_user.wallet
        if wallet.balance < 5:
            wallet.add_tokens(50, 'test', 'Test tokens')
            print(f"Added tokens, balance: {wallet.balance}")
    except Wallet.DoesNotExist:
        wallet = Wallet.objects.create(user=test_user)
        wallet.add_tokens(50, 'test', 'Test tokens')
        print(f"Created wallet with balance: {wallet.balance}")
    
    # Test creating battles for new games
    test_games = ['color_match', 'memory_card']
    
    for game_name in test_games:
        print(f"\n🎮 Testing {game_name}:")
        
        try:
            # Get game type
            game_type = GameType.objects.get(name=game_name)
            print(f"   ✅ Game type found: {game_type.display_name}")
            
            # Create battle
            battle = Battle.objects.create(
                game_type=game_type,
                player1=test_user,
                is_ai_battle=True,
                status='waiting'
            )
            
            print(f"   ✅ Battle created: {battle.id}")
            
            # Test game state initialization
            from gaming.game_logic import GameEngine
            initial_state = GameEngine.create_initial_state(game_name)
            battle.game_state = initial_state
            battle.save()
            
            print(f"   ✅ Game state initialized")
            
            # Clean up
            battle.delete()
            print(f"   🗑️ Cleaned up")
            
        except GameType.DoesNotExist:
            print(f"   ❌ Game type not found: {game_name}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return True


def test_api_call():
    """Test the actual API call"""
    print("\n🌐 Testing API Call")
    print("=" * 20)
    
    from django.test import Client
    import json
    
    client = Client()
    test_user = User.objects.first()
    
    if not test_user:
        print("❌ No users found")
        return False
    
    client.force_login(test_user)
    print(f"Logged in as: {test_user.username}")
    
    # Test new games
    test_games = ['color_match', 'memory_card']
    
    for game_name in test_games:
        print(f"\n🎮 Testing API for {game_name}:")
        
        try:
            response = client.post('/api/gaming/create-ai-battle/', 
                                 data=json.dumps({'game_type': game_name}),
                                 content_type='application/json')
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                response_data = json.loads(response.content.decode())
                print(f"   ✅ SUCCESS!")
                print(f"   Battle ID: {response_data.get('battle_id')}")
                
                # Clean up
                if 'battle_id' in response_data:
                    from gaming.models import Battle
                    try:
                        battle = Battle.objects.get(id=response_data['battle_id'])
                        battle.delete()
                        print(f"   🗑️ Cleaned up")
                    except:
                        pass
            else:
                print(f"   ❌ FAILED!")
                error_content = response.content.decode()
                print(f"   Error: {error_content}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    return True


def show_all_game_types():
    """Show all game types in database"""
    print("\n📋 All Game Types in Database")
    print("=" * 35)
    
    game_types = GameType.objects.all()
    print(f"Total game types: {game_types.count()}")
    
    for game in game_types:
        status = "✅" if game.is_active else "❌"
        print(f"   {status} {game.display_name} ({game.name})")


def main():
    """Main fix function"""
    print("🔧 Simple Fix for New Games")
    print("=" * 40)
    
    # Step 1: Create missing game types
    created = create_missing_game_types()
    
    # Step 2: Show all game types
    show_all_game_types()
    
    # Step 3: Test simple battle creation
    test_simple_battle_creation()
    
    # Step 4: Test API call
    test_api_call()
    
    print("\n🎉 Simple Fix Complete!")
    print("=" * 25)
    
    if created > 0:
        print(f"✅ Created {created} new game types")
        print("✅ New games should now work!")
    else:
        print("✅ All game types already existed")
        print("✅ Check test results above for issues")
    
    print("\n💡 Next steps:")
    print("1. If tests passed, new games should work")
    print("2. If tests failed, check error messages above")
    print("3. Try the games on your frontend")


if __name__ == '__main__':
    main()
