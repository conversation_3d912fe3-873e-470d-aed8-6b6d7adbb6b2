import os
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User

class Command(BaseCommand):
    help = 'Creates a superuser'

    def handle(self, *args, **options):
        username = os.environ.get('DJANGO_SUPERUSER_USERNAME', 'admin')
        email = os.environ.get('DJANGO_SUPERUSER_EMAIL', '<EMAIL>')
        password = os.environ.get('DJANGO_SUPERUSER_PASSWORD', 'admin123')

        self.stdout.write(f'DEBUG: Creating/updating user with username: {username}')
        self.stdout.write(f'DEBUG: Email: {email}')
        self.stdout.write(f'DEBUG: Password length: {len(password)}')

        # Delete any existing admin user first
        if User.objects.filter(username='admin').exists():
            User.objects.filter(username='admin').delete()
            self.stdout.write('DEBUG: Deleted existing admin user')

        # Check if user with this username exists
        if not User.objects.filter(username=username).exists():
            user = User.objects.create_superuser(
                username=username,
                email=email,
                password=password
            )
            user.is_staff = True
            user.is_superuser = True
            user.is_active = True
            user.save()
            self.stdout.write(self.style.SUCCESS(f'Superuser "{username}" created successfully'))
        else:
            # Update existing user to ensure they have correct permissions
            user = User.objects.get(username=username)
            user.is_staff = True
            user.is_superuser = True
            user.is_active = True
            user.set_password(password)
            user.save()
            self.stdout.write(self.style.SUCCESS(f'Superuser "{username}" already exists and updated'))

        # Verify the user was created/updated correctly
        try:
            user = User.objects.get(username=username)
            self.stdout.write(f'DEBUG: User found - is_staff: {user.is_staff}, is_superuser: {user.is_superuser}, is_active: {user.is_active}')
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'ERROR: User {username} was not found after creation!'))
