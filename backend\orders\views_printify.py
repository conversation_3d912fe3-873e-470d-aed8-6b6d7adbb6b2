from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from .models import Order, OrderItem
from utils.printify import place_printify_order
import json
import logging

logger = logging.getLogger(__name__)

@csrf_exempt
@require_POST
def handle_payment_success(request):
    """
    Handle successful payment and place order on Printify
    """
    try:
        # Parse the request data
        data = json.loads(request.body)
        order_id = data.get('order_id')

        # Get the order from the database
        order = Order.objects.get(id=order_id)

        # Check if payment is already processed
        if order.is_paid:
            return JsonResponse({
                'success': False,
                'error': 'Payment already processed'
            }, status=400)

        # Mark the order as paid
        order.is_paid = True
        order.save()

        # Check if shipping fields are populated, if not, use billing address
        if not order.shipping_first_name or not order.shipping_last_name:
            # Split full name into first and last name
            name_parts = order.full_name.split(' ', 1)
            first_name = name_parts[0]
            last_name = name_parts[1] if len(name_parts) > 1 else ''

            # Update order with shipping name
            order.shipping_first_name = first_name
            order.shipping_last_name = last_name

        if not order.shipping_address1:
            order.shipping_address1 = order.address

        if not order.shipping_city:
            order.shipping_city = order.city

        if not order.shipping_state:
            order.shipping_state = order.state

        if not order.shipping_zip:
            order.shipping_zip = order.zipcode

        if not order.shipping_country:
            order.shipping_country = order.country

        # Save the updated order
        order.save()

        # Get customer details from the order
        customer_details = {
            "first_name": order.shipping_first_name,
            "last_name": order.shipping_last_name,
            "email": order.email,
            "phone": order.phone,
            "country": order.shipping_country,
            "region": order.shipping_state,
            "address1": order.shipping_address1,
            "address2": order.shipping_address2 or "",
            "city": order.shipping_city,
            "zip": order.shipping_zip
        }

        # Log the customer details
        logger.info(f"Customer details for Printify order: {customer_details}")

        # Process each order item
        printify_orders = []
        for item in OrderItem.objects.filter(order=order):
            # Skip items that don't have Printify IDs
            if not hasattr(item.product, 'printify_id') or not item.product.printify_id or not item.variant_id:
                logger.warning(f"Skipping item {item.id} - missing Printify ID or variant ID")
                continue

            # Place the order on Printify
            printify_result = place_printify_order(
                order_id=f"{order.id}-{item.id}",
                customer_details=customer_details,
                printify_product_id=item.product.printify_id,
                variant_id=item.variant_id,
                quantity=item.quantity
            )

            # Store the result
            printify_orders.append(printify_result)

            # Update the order item with Printify order ID if successful
            if printify_result['success']:
                item.printify_order_id = printify_result['printify_order_id']
                item.save()

        # Check if all Printify orders were successful
        all_successful = all(order['success'] for order in printify_orders)

        return JsonResponse({
            'success': True,
            'order_id': order.id,
            'printify_orders': printify_orders,
            'all_printify_orders_successful': all_successful
        })

    except Order.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Order not found'
        }, status=404)

    except Exception as e:
        logger.exception(f"Error processing payment success: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
