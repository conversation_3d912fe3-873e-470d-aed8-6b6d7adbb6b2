#!/usr/bin/env python
"""
Test Registration Flow
======================

This script tests the complete registration flow to ensure users are created properly.
"""

import os
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from accounts.models import UserProfile
from wallet.models import Wallet, WalletTransaction
from gaming.models import PlayerStats


def test_registration_endpoint():
    """Test the registration endpoint with a new user"""
    print("🧪 Testing Registration Endpoint")
    print("=" * 40)
    
    client = Client()
    
    # Test user data
    test_username = "test_reg_flow_user"
    test_email = "<EMAIL>"
    test_password = "TestPass123!"
    
    # Clean up any existing test user
    User.objects.filter(username=test_username).delete()
    User.objects.filter(email=test_email).delete()
    
    registration_data = {
        'username': test_username,
        'email': test_email,
        'password': test_password,
        're_password': test_password,
        'first_name': 'Test',
        'last_name': 'User'
    }
    
    print(f"Registering user: {test_username}")
    print(f"Registration data: {registration_data}")
    
    try:
        # Test registration
        response = client.post('/api/auth/register/',
                             data=json.dumps(registration_data),
                             content_type='application/json')
        
        print(f"\nRegistration Response:")
        print(f"Status: {response.status_code}")
        print(f"Content: {response.content.decode()}")
        
        if response.status_code == 201:
            print("✅ Registration successful!")
            
            # Parse response
            try:
                response_data = json.loads(response.content.decode())
                print(f"Response data keys: {list(response_data.keys())}")
                
                if 'access' in response_data and 'refresh' in response_data:
                    print("✅ Tokens received in response")
                else:
                    print("❌ No tokens in response")
                
                if 'user' in response_data:
                    print("✅ User data in response")
                    print(f"User data: {response_data['user']}")
                else:
                    print("❌ No user data in response")
                    
            except json.JSONDecodeError:
                print("❌ Could not parse response as JSON")
            
            # Check if user was created in database
            try:
                user = User.objects.get(username=test_username)
                print(f"\n✅ User created in database: {user.username}")
                print(f"   - ID: {user.id}")
                print(f"   - Email: {user.email}")
                print(f"   - Active: {user.is_active}")
                print(f"   - Password set: {bool(user.password)}")
                
                # Check related objects
                check_user_related_objects(user)
                
                # Test login with created user
                test_login_with_created_user(test_username, test_password)
                
                # Clean up
                user.delete()
                print("✅ Test user cleaned up")
                
                return True
                
            except User.DoesNotExist:
                print("❌ User was not created in database despite successful response")
                return False
                
        else:
            print("❌ Registration failed")
            try:
                error_data = json.loads(response.content.decode())
                print(f"Error details: {error_data}")
            except:
                print(f"Raw error: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during registration: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_user_related_objects(user):
    """Check if all related objects were created for the user"""
    print(f"\n🔍 Checking related objects for {user.username}")
    
    # Check profile
    try:
        profile = user.profile
        print(f"✅ Profile: {profile}")
    except UserProfile.DoesNotExist:
        print("❌ Profile: Missing")
    
    # Check wallet
    try:
        wallet = user.wallet
        print(f"✅ Wallet: {wallet.balance} tokens")
        
        # Check signup bonus
        bonus_transactions = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='signup_bonus'
        )
        
        if bonus_transactions.exists():
            bonus = bonus_transactions.first()
            print(f"✅ Signup bonus: {bonus.amount} tokens")
        else:
            print("❌ Signup bonus: Missing")
            
    except Wallet.DoesNotExist:
        print("❌ Wallet: Missing")
    
    # Check gaming stats
    try:
        stats = user.gaming_stats
        print(f"✅ Gaming stats: {stats}")
    except PlayerStats.DoesNotExist:
        print("❌ Gaming stats: Missing")


def test_login_with_created_user(username, password):
    """Test login with the newly created user"""
    print(f"\n🔐 Testing login with created user: {username}")
    
    client = Client()
    
    # Test custom login
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = client.post('/api/auth/login/',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        print(f"Login response status: {response.status_code}")
        print(f"Login response: {response.content.decode()}")
        
        if response.status_code == 200:
            print("✅ Login successful with created user")
            return True
        else:
            print("❌ Login failed with created user")
            return False
            
    except Exception as e:
        print(f"❌ Exception during login test: {e}")
        return False


def test_djoser_registration():
    """Test Djoser registration endpoint"""
    print("\n🧪 Testing Djoser Registration Endpoint")
    print("=" * 40)
    
    client = Client()
    
    # Test user data
    test_username = "test_djoser_user"
    test_email = "<EMAIL>"
    test_password = "TestPass123!"
    
    # Clean up any existing test user
    User.objects.filter(username=test_username).delete()
    User.objects.filter(email=test_email).delete()
    
    registration_data = {
        'username': test_username,
        'email': test_email,
        'password': test_password,
        're_password': test_password
    }
    
    try:
        response = client.post('/api/auth/djoser/users/',
                             data=json.dumps(registration_data),
                             content_type='application/json')
        
        print(f"Djoser registration status: {response.status_code}")
        print(f"Djoser registration response: {response.content.decode()}")
        
        if response.status_code == 201:
            print("✅ Djoser registration successful")
            
            # Check if user was created
            try:
                user = User.objects.get(username=test_username)
                print(f"✅ User created via Djoser: {user.username}")
                print(f"   - Active: {user.is_active}")
                
                # Clean up
                user.delete()
                return True
                
            except User.DoesNotExist:
                print("❌ User not found after Djoser registration")
                return False
        else:
            print("❌ Djoser registration failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception during Djoser registration: {e}")
        return False


def main():
    """Main test function"""
    print("🔧 Registration Flow Test")
    print("=" * 50)
    
    # Test custom registration
    custom_reg_success = test_registration_endpoint()
    
    # Test Djoser registration
    djoser_reg_success = test_djoser_registration()
    
    print("\n📋 Test Summary")
    print("=" * 40)
    print(f"Custom registration: {'✅ PASSED' if custom_reg_success else '❌ FAILED'}")
    print(f"Djoser registration: {'✅ PASSED' if djoser_reg_success else '❌ FAILED'}")
    
    if custom_reg_success:
        print("\n✅ Custom registration is working correctly!")
        print("Users should be able to register and login successfully.")
    else:
        print("\n❌ Custom registration has issues!")
        print("Check Django logs for more details.")
    
    print("\n💡 Next Steps:")
    print("1. If registration works but login fails, check password handling")
    print("2. If registration fails, check serializer validation")
    print("3. If related objects are missing, check signals")
    print("4. Run debug_authentication.py to test login with existing users")


if __name__ == '__main__':
    main()
