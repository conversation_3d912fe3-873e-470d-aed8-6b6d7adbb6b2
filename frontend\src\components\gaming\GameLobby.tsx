import React, { useState, useEffect } from 'react';
import { api } from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';

interface GameType {
  id: number;
  name: string;
  display_name: string;
  description: string;
  rules: any;
  is_active: boolean;
}

interface GameLobbyProps {
  onBattleCreated: (battleId: string) => void;
  onGameSelected?: (gameType: string) => void;
}

const GameLobby: React.FC<GameLobbyProps> = ({ onBattleCreated, onGameSelected }) => {
  const { user } = useAuth();
  const { wallet, loading: walletLoading, error: walletError, fetchWallet } = useWallet();
  const [error, setError] = useState<string | null>(null);
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [matchmakingStatus, setMatchmakingStatus] = useState<'idle' | 'searching' | 'found'>('idle');

  console.log('Using HTTP-only mode for gaming');

  const handleFindMatch = (gameType: string) => {
    setError('Multiplayer matchmaking coming soon! Try playing vs AI for now.');
  };

  const handlePlayAI = async (gameType: string) => {
    console.log('handlePlayAI called with:', gameType);

    // Check if user has tokens
    if (!wallet || wallet.balance <= 0) {
      setError('You need tokens to play games! Your balance is 0. Request a token refill to continue playing.');
      return;
    }

    try {
      setSelectedGame(gameType);
      setError(null);
      setMatchmakingStatus('searching');

      // Create AI battle via HTTP API (for Rock Paper Scissors, Number Guessing, Tic Tac Toe)
      const response = await api.post('/api/gaming/create-ai-battle/', {
        game_type: gameType
      });

      if (response.data.battle_id) {
        console.log('AI battle created:', response.data.battle_id);
        onBattleCreated(response.data.battle_id);
      } else {
        setError('Failed to create AI battle');
        setMatchmakingStatus('idle');
      }
    } catch (err: any) {
      console.error('Failed to create AI battle:', err);
      if (err.response?.status === 401) {
        setError('Please log in to play games');
      } else if (err.response?.status === 400 && err.response?.data?.error?.includes('tokens')) {
        setError(err.response.data.error + ' Request a token refill to continue playing.');
      } else {
        setError('Failed to create AI battle. Please try again.');
      }
      setMatchmakingStatus('idle');
    }
  };

  const handleCancelMatchmaking = () => {
    setMatchmakingStatus('idle');
    setSelectedGame(null);
  };

  // No loading state needed since we're using hardcoded games

  return (
    <div className="max-w-6xl mx-auto">
      {/* Welcome Section */}
      <div className="text-center mb-12">
        <div className="relative inline-block">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🎯 Game Lobby</h1>
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
        </div>
        <p className="text-xl text-gray-600 mt-6 max-w-2xl mx-auto">
          Choose your battlefield and prove your skills! Compete with AI or challenge other players to earn tokens and climb the leaderboards.
        </p>
      </div>

      {/* Wallet Balance Display */}
      {wallet && (
        <div className="mb-8 flex justify-center">
          <div className="bg-gradient-to-r from-emerald-500 via-teal-600 to-cyan-700 rounded-2xl p-6 text-white shadow-2xl transform hover:scale-105 transition-all duration-300">
            <div className="flex items-center justify-center space-x-6">
              <div className="text-center">
                <div className="text-3xl font-bold mb-1">🪙 {wallet.balance}</div>
                <div className="text-sm text-emerald-100">Available Tokens</div>
              </div>
              <div className="w-px h-12 bg-white bg-opacity-30"></div>
              <div className="text-center">
                <div className="text-2xl font-semibold mb-1">₹{wallet.balance_in_inr}</div>
                <div className="text-sm text-emerald-100">Wallet Value</div>
              </div>
            </div>
            {wallet.balance === 0 && (
              <div className="mt-4 text-center">
                <div className="bg-red-500 bg-opacity-20 rounded-lg px-4 py-2 border border-red-300 border-opacity-30">
                  <span className="text-red-100 font-medium">⚠️ You need tokens to play games</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Status Indicator */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full border border-green-200">
          <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
          <span className="font-medium">System Online - Ready to Play!</span>
        </div>
      </div>

      {error && (
        <div className="mb-8 max-w-2xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-2xl p-6 shadow-lg">
            <div className="flex items-center">
              <div className="text-3xl mr-4">⚠️</div>
              <div>
                <h3 className="text-lg font-semibold text-red-800 mb-1">Oops! Something went wrong</h3>
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {matchmakingStatus === 'searching' && (
        <div className="mb-8 max-w-2xl mx-auto">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-6 text-white shadow-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="animate-spin rounded-full h-8 w-8 border-4 border-white border-t-transparent"></div>
                <div>
                  <h3 className="text-lg font-semibold mb-1">Finding Your Match</h3>
                  <p className="text-blue-100">Searching for a worthy opponent...</p>
                </div>
              </div>
              <button
                onClick={handleCancelMatchmaking}
                className="px-4 py-2 bg-white bg-opacity-20 backdrop-blur-sm text-white font-semibold rounded-lg hover:bg-opacity-30 transition-all duration-200 border border-white border-opacity-30"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Game Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {/* Tic Tac Toe - Standalone Game */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-purple-500 via-pink-600 to-red-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">⭕</span>
                  <h3 className="text-xl font-bold">Tic Tac Toe</h3>
                </div>
                <p className="text-sm opacity-90">Classic strategy game - Play vs AI or human opponents!</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => window.location.href = '/gaming/tic-tac-toe'}
                  disabled={!wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">🤖</span>
                  Play vs AI
                </button>

                <button
                  onClick={() => handleFindMatch('tic_tac_toe')}
                  disabled={matchmakingStatus !== 'idle' || !wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-xl hover:from-pink-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">👥</span>
                  Find Opponent
                </button>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-purple-700 font-medium mb-1">🏆 Standardized Rewards</div>
                  <div className="text-xs text-purple-600">Play: +2 • Win: +5 • Draw: +2 • Loss: -1 token</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Game Rules
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• Get 3 in a row to win</div>
                      <div>• Hard mode: AI plays optimally</div>
                      <div>• Win: +5 tokens, Draw: +2 tokens</div>
                      <div>• Loss: -1 token (challenging!)</div>
                      <div>• Play vs AI or find human opponent</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Rock Paper Scissors - Battle Game */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">✂️</span>
                  <h3 className="text-xl font-bold">Rock Paper Scissors</h3>
                </div>
                <p className="text-sm opacity-90">Classic battle game - Play vs AI or human opponents!</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => window.location.href = '/gaming/rock-paper-scissors'}
                  disabled={!wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">🤖</span>
                  Play vs AI
                </button>

                <button
                  onClick={() => handleFindMatch('rock_paper_scissors')}
                  disabled={matchmakingStatus !== 'idle' || !wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-blue-600 text-white font-semibold rounded-xl hover:from-indigo-600 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">👥</span>
                  Find Opponent
                </button>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-blue-700 font-medium mb-1">🏆 Standardized Rewards</div>
                  <div className="text-xs text-blue-600">Play: +2 • Win: +5 • Draw: +2 • Loss: -1 token</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Game Rules
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• Rock beats Scissors</div>
                      <div>• Paper beats Rock</div>
                      <div>• Scissors beats Paper</div>
                      <div>• Best of 3 rounds wins</div>
                      <div>• Play vs AI or find human opponent</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Number Guessing - Battle Game */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-green-500 via-teal-600 to-blue-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">🎲</span>
                  <h3 className="text-xl font-bold">Number Guessing</h3>
                </div>
                <p className="text-sm opacity-90">Guess the number - Play vs AI or human opponents!</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => window.location.href = '/gaming/number-guessing'}
                  disabled={!wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">🤖</span>
                  Play vs AI
                </button>

                <button
                  onClick={() => handleFindMatch('number_guessing')}
                  disabled={matchmakingStatus !== 'idle' || !wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-teal-500 to-green-600 text-white font-semibold rounded-xl hover:from-teal-600 hover:to-green-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">👥</span>
                  Find Opponent
                </button>

                <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-green-700 font-medium mb-1">🏆 Standardized Rewards</div>
                  <div className="text-xs text-green-600">Play: +2 • Win: +5 • Draw: +2 • Loss: -1 token</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Game Rules
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• Guess number between 1-100</div>
                      <div>• Closest guess wins the round</div>
                      <div>• 5 rounds per game</div>
                      <div>• Strategy and luck combined</div>
                      <div>• Play vs AI or find human opponent</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Color Match - Battle Game */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-pink-500 via-rose-600 to-red-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">🌈</span>
                  <h3 className="text-xl font-bold">Color Match</h3>
                </div>
                <p className="text-sm opacity-90">Remember color sequences - Play vs AI or human opponents!</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => window.location.href = '/gaming/color-match'}
                  disabled={!wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-pink-500 to-rose-600 text-white font-semibold rounded-xl hover:from-pink-600 hover:to-rose-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">🤖</span>
                  Play vs AI
                </button>

                <button
                  onClick={() => handleFindMatch('color_match')}
                  disabled={matchmakingStatus !== 'idle' || !wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-rose-500 to-pink-600 text-white font-semibold rounded-xl hover:from-rose-600 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">👥</span>
                  Find Opponent
                </button>

                <div className="bg-pink-50 border border-pink-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-pink-700 font-medium mb-1">🏆 Standardized Rewards</div>
                  <div className="text-xs text-pink-600">Play: +2 • Win: +5 • Draw: +2 • Loss: -1 token</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Game Rules
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• Watch the color sequence</div>
                      <div>• Repeat the sequence correctly</div>
                      <div>• Sequences get longer each round</div>
                      <div>• 5 rounds per game</div>
                      <div>• Play vs AI or find human opponent</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Memory Card Match - Battle Game */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-amber-500 via-orange-600 to-red-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">🃏</span>
                  <h3 className="text-xl font-bold">Memory Card Match</h3>
                </div>
                <p className="text-sm opacity-90">Find matching card pairs - Play vs AI or human opponents!</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => window.location.href = '/gaming/memory-card'}
                  disabled={!wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-amber-500 to-orange-600 text-white font-semibold rounded-xl hover:from-amber-600 hover:to-orange-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">🤖</span>
                  Play vs AI
                </button>

                <button
                  onClick={() => handleFindMatch('memory_card')}
                  disabled={matchmakingStatus !== 'idle' || !wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-orange-500 to-amber-600 text-white font-semibold rounded-xl hover:from-orange-600 hover:to-amber-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">👥</span>
                  Find Opponent
                </button>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-amber-700 font-medium mb-1">🏆 Standardized Rewards</div>
                  <div className="text-xs text-amber-600">Play: +2 • Win: +5 • Draw: +2 • Loss: -1 token</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Game Rules
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• Flip cards to reveal animals</div>
                      <div>• Find matching pairs</div>
                      <div>• Remember card positions</div>
                      <div>• 8 pairs to find (16 cards total)</div>
                      <div>• Play vs AI or find human opponent</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Ludo Game */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-yellow-500 via-orange-600 to-red-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">🎲</span>
                  <h3 className="text-xl font-bold">Ludo</h3>
                </div>
                <p className="text-sm opacity-90">5-minute timer-based Ludo! Make as many valid moves as possible to win.</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => window.location.href = '/gaming/ludo'}
                  disabled={!wallet || wallet.balance <= 0}
                  className="w-full px-4 py-3 bg-gradient-to-r from-yellow-500 to-orange-600 text-white font-semibold rounded-xl hover:from-yellow-600 hover:to-orange-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transform hover:scale-105 transition-all duration-200 shadow-lg"
                  title={!wallet || wallet.balance <= 0 ? "You need tokens to play games" : ""}
                >
                  <span className="mr-2">🤖</span>
                  Play vs AI
                </button>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-yellow-700 font-medium mb-1">⏰ 5-Minute Timer Game</div>
                  <div className="text-xs text-yellow-600">Entry: -2 • Win: +5 • Loss: -1 • Draw: Replay</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Game Rules
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• 5-minute timer-based gameplay</div>
                      <div>• Make as many valid moves as possible</div>
                      <div>• Choose 2 or 4 tokens per player</div>
                      <div>• Player with most moves wins</div>
                      <div>• Roll dice and move tokens strategically</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Spin Wheel - Daily Reward */}
        <div className="group">
          <div className="relative overflow-hidden rounded-2xl bg-white shadow-2xl transform hover:scale-105 transition-all duration-300 border border-gray-100">
            {/* Gradient Header */}
            <div className="bg-gradient-to-br from-purple-500 via-pink-600 to-indigo-700 p-6 text-white relative overflow-hidden">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
              <div className="absolute bottom-0 left-0 -mb-6 -ml-6 w-24 h-24 bg-white bg-opacity-5 rounded-full"></div>

              <div className="relative z-10">
                <div className="flex items-center mb-3">
                  <span className="text-3xl mr-3">🎡</span>
                  <h3 className="text-xl font-bold">Daily Spin Wheel</h3>
                </div>
                <p className="text-sm opacity-90">Spin once per day for amazing rewards! Tokens, scratch cards & discounts!</p>
              </div>
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="space-y-3">
                <button
                  onClick={() => onGameSelected && onGameSelected('spin-wheel')}
                  className="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white font-semibold rounded-xl hover:from-purple-600 hover:to-pink-700 transform hover:scale-105 transition-all duration-200 shadow-lg"
                >
                  <span className="mr-2">🎯</span>
                  Spin Now!
                </button>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
                  <div className="text-sm text-purple-700 font-medium mb-1">🎁 Daily Rewards</div>
                  <div className="text-xs text-purple-600">Free spin • Tokens • Scratch cards • Discounts</div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center">
                    <span className="mr-2">📋</span>
                    Rewards Info
                    <span className="ml-auto transform group-open:rotate-180 transition-transform duration-200">▼</span>
                  </summary>
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-xs text-gray-600 space-y-1">
                      <div>• Free daily spin (24-hour cooldown)</div>
                      <div>• Win 2-25 tokens instantly</div>
                      <div>• Scratch cards with hidden prizes</div>
                      <div>• 5-10% shopping discounts</div>
                      <div>• No tokens required to play</div>
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        {/* Database games removed - using hardcoded games above for better styling and control */}
      </div>


    </div>
  );
};

export default GameLobby;
