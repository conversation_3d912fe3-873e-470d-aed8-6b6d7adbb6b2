from django.db import models
from django.contrib.auth.models import User
from products.models import Product
import uuid


class Cart(models.Model):
    """
    Shopping cart model
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.user:
            return f"Cart for {self.user.username}"
        return f"Cart {self.id}"

    @property
    def total_price(self):
        return sum(item.total_price for item in self.items.all())

    @property
    def total_items(self):
        return sum(item.quantity for item in self.items.all())

    @property
    def has_token_eligible_items(self):
        """Check if cart has any items that allow token discounts"""
        return self.items.filter(product__allow_token_discount=True, product__is_active=True).exists()

    def calculate_token_discount_info(self, user_wallet_balance=0):
        """
        Calculate token discount information for the cart

        Args:
            user_wallet_balance: User's current token balance

        Returns:
            dict: Token discount calculation details
        """
        from django.conf import settings
        from decimal import Decimal

        # Check if cart has token-eligible items
        if not self.has_token_eligible_items:
            return {
                'eligible': False,
                'reason': 'No token-eligible items in cart',
                'max_tokens_usable': 0,
                'max_inr_discount': 0,
                'eligible_items': []
            }

        # Check if user has tokens
        if user_wallet_balance <= 0:
            return {
                'eligible': False,
                'reason': 'No tokens available in wallet',
                'max_tokens_usable': 0,
                'max_inr_discount': 0,
                'eligible_items': []
            }

        token_rate = Decimal(str(settings.WALLET_SETTINGS.get('TOKEN_TO_INR_RATE', 0.1)))
        total_discount = Decimal('0')
        total_tokens_needed = 0
        eligible_items = []

        # Calculate discount for each eligible item
        for item in self.items.filter(product__allow_token_discount=True, product__is_active=True):
            item_discount = item.product.calculate_max_token_discount(item.quantity)

            if item_discount['max_inr_discount'] > 0:
                total_discount += Decimal(str(item_discount['max_inr_discount']))
                total_tokens_needed += item_discount['max_tokens']

                eligible_items.append({
                    'product_name': item.product.name,
                    'quantity': item.quantity,
                    'max_tokens': item_discount['max_tokens'],
                    'max_inr_discount': item_discount['max_inr_discount'],
                    'discount_percentage': item_discount['discount_percentage']
                })

        # Limit by user's wallet balance
        max_tokens_usable = min(total_tokens_needed, user_wallet_balance)
        max_inr_discount = min(total_discount, Decimal(str(max_tokens_usable)) * token_rate)

        # Calculate final amount
        cart_total = self.total_price
        final_amount = max(Decimal('0'), cart_total - max_inr_discount)

        return {
            'eligible': True,
            'max_tokens_usable': max_tokens_usable,
            'max_inr_discount': max_inr_discount,
            'final_amount': final_amount,
            'eligible_items': eligible_items,
            'cart_total': cart_total
        }


class CartItem(models.Model):
    """
    Shopping cart item model
    """
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    variant_id = models.CharField(max_length=100, blank=True, null=True, help_text="Printify variant ID")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['cart', 'product', 'variant_id']

    def __str__(self):
        return f"{self.quantity} x {self.product.name} in cart {self.cart.id}"

    @property
    def total_price(self):
        """Calculate total price using variant price if available, otherwise product price"""
        price = self.get_variant_price()
        return price * self.quantity

    def get_variant_price(self):
        """Get the price for this cart item's variant, or fallback to product price"""
        if self.variant_id:
            try:
                from products.models import ProductVariant
                variant = ProductVariant.objects.get(
                    product=self.product,
                    variant_id=self.variant_id
                )
                return variant.price
            except ProductVariant.DoesNotExist:
                pass
        return self.product.price

    def get_variant_details(self):
        """Get variant details (color, size, title) if available"""
        if self.variant_id:
            try:
                from products.models import ProductVariant
                variant = ProductVariant.objects.get(
                    product=self.product,
                    variant_id=self.variant_id
                )
                return {
                    'id': variant.variant_id,
                    'title': variant.title,
                    'color': variant.color,
                    'size': variant.size,
                    'price': variant.price
                }
            except ProductVariant.DoesNotExist:
                pass
        return None


class Order(models.Model):
    """
    Order model for storing order information
    """
    ORDER_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    )

    PAYMENT_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')
    full_name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)

    # Billing address
    address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    zipcode = models.CharField(max_length=20)
    country = models.CharField(max_length=100)

    # Shipping address (for Printify)
    shipping_first_name = models.CharField(max_length=100, blank=True, null=True)
    shipping_last_name = models.CharField(max_length=100, blank=True, null=True)
    shipping_address1 = models.CharField(max_length=255, blank=True, null=True)
    shipping_address2 = models.CharField(max_length=255, blank=True, null=True)
    shipping_city = models.CharField(max_length=100, blank=True, null=True)
    shipping_state = models.CharField(max_length=100, blank=True, null=True)
    shipping_zip = models.CharField(max_length=20, blank=True, null=True)
    shipping_country = models.CharField(max_length=100, blank=True, null=True)
    total = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=ORDER_STATUS_CHOICES, default='pending')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20)
    payment_id = models.CharField(max_length=100, blank=True, null=True)
    razorpay_order_id = models.CharField(max_length=100, blank=True, null=True)
    razorpay_payment_id = models.CharField(max_length=100, blank=True, null=True)
    razorpay_signature = models.CharField(max_length=255, blank=True, null=True)
    is_paid = models.BooleanField(default=False, help_text="Whether payment has been received")
    notes = models.TextField(blank=True, null=True)
    tracking_number = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Order {self.id} by {self.user.username}"


class OrderItem(models.Model):
    """
    Order item model for storing ordered items
    """
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    quantity = models.PositiveIntegerField(default=1)
    variant_id = models.CharField(max_length=100, blank=True, null=True, help_text="Printify variant ID")
    printify_order_id = models.CharField(max_length=100, blank=True, null=True, help_text="Printify order ID")

    def __str__(self):
        return f"{self.quantity} x {self.product.name} in {self.order.id}"

    @property
    def total_price(self):
        return self.price * self.quantity
