import logging
from django.core.management.base import BaseCommand
from printify.api_client import PrintifyAPIClient
from printify.models import PrintifyProduct
from products.models import Product, ProductImage

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fetch and update images for Printify products'

    def add_arguments(self, parser):
        parser.add_argument('--shop_id', type=str, required=True, help='Printify shop ID')
        parser.add_argument('--product_id', type=str, help='Specific Printify product ID to update')
        parser.add_argument('--all', action='store_true', help='Update all Printify products')
        parser.add_argument('--force', action='store_true', help='Force update even if images exist')

    def handle(self, *args, **options):
        shop_id = options['shop_id']
        product_id = options.get('product_id')
        update_all = options.get('all', False)
        force = options.get('force', False)
        
        if not product_id and not update_all:
            self.stdout.write(self.style.ERROR('Either --product_id or --all must be specified'))
            return
        
        self.stdout.write(self.style.SUCCESS(f'Starting Printify image fetch for shop {shop_id}'))
        
        try:
            # Initialize API client
            client = PrintifyAPIClient()
            
            # Get products to update
            if product_id:
                printify_products = PrintifyProduct.objects.filter(printify_id=product_id)
                self.stdout.write(f'Fetching images for specific product: {product_id}')
            else:
                printify_products = PrintifyProduct.objects.all()
                self.stdout.write(f'Fetching images for all {printify_products.count()} products')
            
            # Track statistics
            stats = {
                'total': printify_products.count(),
                'updated': 0,
                'skipped': 0,
                'errors': 0,
                'images_added': 0
            }
            
            # Process each product
            for i, printify_product in enumerate(printify_products):
                try:
                    self.stdout.write(f'Processing product {i+1}/{stats["total"]}: {printify_product.printify_id}')
                    
                    # Check if product already has images and we're not forcing an update
                    if not force and printify_product.images_json and len(printify_product.images_json) > 0:
                        self.stdout.write(f'Product already has {len(printify_product.images_json)} images. Skipping...')
                        stats['skipped'] += 1
                        continue
                    
                    # Get detailed product information from Printify API
                    detailed_product = client.get_product(shop_id, printify_product.printify_id)
                    
                    # Extract images
                    images_json = detailed_product.get('images', [])
                    
                    if not images_json:
                        self.stdout.write(self.style.WARNING(f'No images found for product {printify_product.printify_id}'))
                        stats['skipped'] += 1
                        continue
                    
                    # Update Printify product with images
                    printify_product.images_json = images_json
                    printify_product.save()
                    
                    self.stdout.write(self.style.SUCCESS(f'Updated Printify product with {len(images_json)} images'))
                    
                    # Find corresponding Django product
                    try:
                        product = Product.objects.get(printify_id=printify_product.printify_id)
                        
                        # Delete existing product images
                        deleted_count = ProductImage.objects.filter(product=product).delete()
                        self.stdout.write(f'Deleted {deleted_count[0]} existing product images')
                        
                        # Create new product images
                        for i, image_data in enumerate(images_json):
                            image_url = image_data.get('src', '')
                            if image_url:
                                ProductImage.objects.create(
                                    product=product,
                                    image=image_url,
                                    image_url=image_url,
                                    is_primary=(i == 0),
                                    alt_text=f"{product.name} - Image {i+1}"
                                )
                                stats['images_added'] += 1
                                self.stdout.write(f'Added image {i+1}: {image_url}')
                        
                        self.stdout.write(self.style.SUCCESS(f'Updated Django product with {len(images_json)} images'))
                    except Product.DoesNotExist:
                        self.stdout.write(self.style.WARNING(f'No Django product found for Printify ID: {printify_product.printify_id}'))
                    
                    stats['updated'] += 1
                    
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error processing product {printify_product.printify_id}: {str(e)}'))
                    stats['errors'] += 1
            
            # Print summary
            self.stdout.write(self.style.SUCCESS(
                f'Image fetch completed. Total: {stats["total"]}, Updated: {stats["updated"]}, '
                f'Skipped: {stats["skipped"]}, Errors: {stats["errors"]}, Images added: {stats["images_added"]}'
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error fetching Printify images: {str(e)}'))
