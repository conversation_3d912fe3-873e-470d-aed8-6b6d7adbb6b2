from django.core.management.base import BaseCommand
from gaming.spin_wheel_models import Spin<PERSON>heelReward, SpinWheelSettings


class Command(BaseCommand):
    help = 'Set up spin wheel rewards and settings'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Spin Wheel system...'))
        
        # Create default settings
        settings, created = SpinWheelSettings.objects.get_or_create(
            id='00000000-0000-0000-0000-000000000001',
            defaults={
                'cooldown_hours': 24,
                'wheel_segments': 8,
                'animation_duration': 3000,
                'min_token_reward': 1,
                'max_token_reward': 50,
                'scratch_card_probability': 0.2,
                'is_active': True,
                'maintenance_mode': False,
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✅ Created default spin wheel settings'))
        else:
            self.stdout.write(self.style.WARNING('⚠️ Spin wheel settings already exist'))
        
        # Create default rewards (8 segments)
        default_rewards = [
            {
                'name': '5 Tokens',
                'reward_type': 'tokens',
                'value': 5,
                'probability': 0.25,  # 25% chance
                'extra_data': {'color': '#FFD700', 'icon': '🪙'}
            },
            {
                'name': '10 Tokens',
                'reward_type': 'tokens',
                'value': 10,
                'probability': 0.20,  # 20% chance
                'extra_data': {'color': '#FF6B6B', 'icon': '💰'}
            },
            {
                'name': '15 Tokens',
                'reward_type': 'tokens',
                'value': 15,
                'probability': 0.15,  # 15% chance
                'extra_data': {'color': '#4ECDC4', 'icon': '💎'}
            },
            {
                'name': '25 Tokens',
                'reward_type': 'tokens',
                'value': 25,
                'probability': 0.10,  # 10% chance
                'extra_data': {'color': '#45B7D1', 'icon': '🎁'}
            },
            {
                'name': 'Scratch Card',
                'reward_type': 'scratch_card',
                'value': 1,
                'probability': 0.15,  # 15% chance
                'extra_data': {'color': '#96CEB4', 'icon': '🎫'}
            },
            {
                'name': '5% Discount',
                'reward_type': 'discount',
                'value': 5,
                'probability': 0.08,  # 8% chance
                'extra_data': {'color': '#FFEAA7', 'icon': '🏷️'}
            },
            {
                'name': '10% Discount',
                'reward_type': 'discount',
                'value': 10,
                'probability': 0.05,  # 5% chance
                'extra_data': {'color': '#DDA0DD', 'icon': '🎟️'}
            },
            {
                'name': '2 Tokens',
                'reward_type': 'tokens',
                'value': 2,
                'probability': 0.02,  # 2% chance (consolation prize)
                'extra_data': {'color': '#F0F0F0', 'icon': '🥉'}
            }
        ]
        
        created_count = 0
        for reward_data in default_rewards:
            reward, created = SpinWheelReward.objects.get_or_create(
                name=reward_data['name'],
                reward_type=reward_data['reward_type'],
                defaults={
                    'value': reward_data['value'],
                    'probability': reward_data['probability'],
                    'extra_data': reward_data['extra_data'],
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created reward: {reward.name}')
                )
        
        if created_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f'✅ Created {created_count} new spin wheel rewards')
            )
        else:
            self.stdout.write(
                self.style.WARNING('⚠️ All spin wheel rewards already exist')
            )
        
        # Verify probabilities sum to 1.0
        total_probability = sum(
            reward.probability for reward in SpinWheelReward.objects.filter(is_active=True)
        )
        
        self.stdout.write(f'📊 Total probability: {total_probability:.2f}')
        
        if abs(total_probability - 1.0) > 0.01:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️ Warning: Total probability ({total_probability:.2f}) should be close to 1.0'
                )
            )
        
        # Display summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('🎡 SPIN WHEEL SETUP COMPLETE'))
        self.stdout.write('='*50)
        
        active_rewards = SpinWheelReward.objects.filter(is_active=True).order_by('-probability')
        
        self.stdout.write('\n📋 Active Rewards:')
        for reward in active_rewards:
            icon = reward.extra_data.get('icon', '🎁')
            self.stdout.write(
                f'  {icon} {reward.name} ({reward.reward_type}) - '
                f'{reward.probability*100:.1f}% chance'
            )
        
        self.stdout.write(f'\n⚙️ Settings:')
        self.stdout.write(f'  • Cooldown: {settings.cooldown_hours} hours')
        self.stdout.write(f'  • Wheel segments: {settings.wheel_segments}')
        self.stdout.write(f'  • Animation duration: {settings.animation_duration}ms')
        self.stdout.write(f'  • Token range: {settings.min_token_reward}-{settings.max_token_reward}')
        self.stdout.write(f'  • Scratch card probability: {settings.scratch_card_probability*100:.1f}%')
        self.stdout.write(f'  • Active: {settings.is_active}')
        
        self.stdout.write('\n🚀 Ready to spin!')
        self.stdout.write(
            self.style.SUCCESS(
                '\nRun migrations if needed: python manage.py makemigrations && python manage.py migrate'
            )
        )
