{"version": 3, "file": "react-confetti.umd.js", "sources": ["../src/utils.ts", "../src/Particle.ts", "../src/ParticleGenerator.ts", "../src/Confetti.ts", "../src/ReactConfetti.tsx"], "sourcesContent": ["import { ICircle } from './Circle'\nimport { IPoint } from './Point'\nimport { IRect } from './Rect'\n\nexport function norm(value: number, min: number, max: number) {\n  return (value - min) / (max - min)\n}\n\nexport function lerp(lnorm: number, min: number, max: number) {\n  return (max - min) * lnorm + min\n}\n\nexport function map(\n  value: number,\n  sourceMin: number,\n  sourceMax: number,\n  destMin: number,\n  destMax: number,\n) {\n  return lerp(norm(value, sourceMin, sourceMax), destMin, destMax)\n}\n\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, Math.min(min, max)), Math.max(min, max))\n}\n\nexport function distance(p0: IPoint, p1: IPoint) {\n  const dx = p1.x - p0.x\n  const dy = p1.y - p0.y\n  return Math.sqrt(dx * dx + dy * dy)\n}\n\nexport function distanceXY(x0: number, y0: number, x1: number, y1: number) {\n  const dx = x1 - x0\n  const dy = y1 - y0\n  return Math.sqrt(dx * dx + dy * dy)\n}\n\nexport function circleCollision(c0: ICircle, c1: ICircle) {\n  return distance(c0, c1) <= c0.radius + c1.radius\n}\n\nexport function circlePointCollision(x: number, y: number, circle: ICircle) {\n  return distanceXY(x, y, circle.x, circle.y) < circle.radius\n}\n\nexport function inRange(value: number, min: number, max: number) {\n  return value >= Math.min(min, max) && value <= Math.max(min, max)\n}\n\nexport function pointInRect(p: IPoint, rect: IRect) {\n  return (\n    inRange(p.x, rect.x, rect.x + rect.w) &&\n    inRange(p.y, rect.y, rect.y + rect.h)\n  )\n}\n\nexport function rangeIntersect(\n  min0: number,\n  max0: number,\n  min1: number,\n  max1: number,\n) {\n  return (\n    Math.max(min0, max0) >= Math.min(min1, max1) &&\n    Math.min(min0, max0) <= Math.max(min1, max1)\n  )\n}\n\nexport function rectIntersect(r0: IRect, r1: IRect) {\n  return (\n    rangeIntersect(r0.x, r0.x + r0.w, r1.x, r1.x + r1.w) &&\n    rangeIntersect(r0.y, r0.y + r0.h, r1.y, r1.y + r1.h)\n  )\n}\n\nexport function degreesToRads(degrees: number) {\n  return (degrees * Math.PI) / 180\n}\n\nexport function radsToDegrees(radians: number) {\n  return (radians * 180) / Math.PI\n}\n\nexport function randomRange(min: number, max: number) {\n  return min + Math.random() * (max - min)\n}\n\nexport function randomInt(min: number, max: number) {\n  return Math.floor(min + Math.random() * (max - min + 1))\n}\n", "import { IConfettiOptions } from './Confetti'\nimport { degreesToRads, randomInt, randomRange } from './utils'\n\nexport enum ParticleShape {\n  Circle = 0,\n  Square = 1,\n  Strip = 2,\n}\n\nenum RotationDirection {\n  Positive = 1,\n  Negative = -1,\n}\n\nconst DEFAULT_FRAME_TIME = 1000 / 60\n\nexport default class Particle {\n  constructor(\n    context: CanvasRenderingContext2D,\n    getOptions: () => IConfettiOptions,\n    x: number,\n    y: number,\n  ) {\n    this.getOptions = getOptions\n    const { colors, initialVelocityX, initialVelocityY } = this.getOptions()\n    this.context = context\n    this.x = x\n    this.y = y\n    this.w = randomRange(5, 20)\n    this.h = randomRange(5, 20)\n    this.radius = randomRange(5, 10)\n    this.vx =\n      typeof initialVelocityX === 'number'\n        ? randomRange(-initialVelocityX, initialVelocityX)\n        : randomRange(initialVelocityX.min, initialVelocityX.max)\n    this.vy =\n      typeof initialVelocityY === 'number'\n        ? randomRange(-initialVelocityY, 0)\n        : randomRange(initialVelocityY.min, initialVelocityY.max)\n    this.shape = randomInt(0, 2)\n    this.angle = degreesToRads(randomRange(0, 360))\n    this.angularSpin = randomRange(-0.2, 0.2)\n    this.color = colors[Math.floor(Math.random() * colors.length)]\n    this.rotateY = randomRange(0, 1)\n    this.rotationDirection = randomRange(0, 1)\n      ? RotationDirection.Positive\n      : RotationDirection.Negative\n  }\n\n  context: CanvasRenderingContext2D\n\n  radius: number\n\n  x: number\n\n  y: number\n\n  w: number\n\n  h: number\n\n  vx: number\n\n  vy: number\n\n  shape: ParticleShape\n\n  angle: number\n\n  angularSpin: number\n\n  color: string\n\n  // Actually used as scaleY to simulate rotation cheaply\n  rotateY: number\n\n  rotationDirection: RotationDirection\n\n  getOptions: () => IConfettiOptions\n\n  update(elapsed: number) {\n    const { gravity, wind, friction, opacity, drawShape } = this.getOptions()\n    const frameTimeMultiplier = elapsed / DEFAULT_FRAME_TIME\n    this.x += this.vx * frameTimeMultiplier\n    this.y += this.vy * frameTimeMultiplier\n    this.vy += gravity * frameTimeMultiplier\n    this.vx += wind * frameTimeMultiplier\n    this.vx *= friction ** frameTimeMultiplier\n    this.vy *= friction ** frameTimeMultiplier\n    if (\n      this.rotateY >= 1 &&\n      this.rotationDirection === RotationDirection.Positive\n    ) {\n      this.rotationDirection = RotationDirection.Negative\n    } else if (\n      this.rotateY <= -1 &&\n      this.rotationDirection === RotationDirection.Negative\n    ) {\n      this.rotationDirection = RotationDirection.Positive\n    }\n\n    const rotateDelta = 0.1 * this.rotationDirection * frameTimeMultiplier\n\n    this.rotateY += rotateDelta\n    this.angle += this.angularSpin\n    this.context.save()\n    this.context.translate(this.x, this.y)\n    this.context.rotate(this.angle)\n    this.context.scale(1, this.rotateY)\n    this.context.rotate(this.angle)\n    this.context.beginPath()\n    this.context.fillStyle = this.color\n    this.context.strokeStyle = this.color\n    this.context.globalAlpha = opacity\n    this.context.lineCap = 'round'\n    this.context.lineWidth = 2\n    if (drawShape && typeof drawShape === 'function') {\n      drawShape.call(this, this.context)\n    } else {\n      switch (this.shape) {\n        case ParticleShape.Circle: {\n          this.context.beginPath()\n          this.context.arc(0, 0, this.radius, 0, 2 * Math.PI)\n          this.context.fill()\n          break\n        }\n        case ParticleShape.Square: {\n          this.context.fillRect(-this.w / 2, -this.h / 2, this.w, this.h)\n          break\n        }\n        case ParticleShape.Strip: {\n          this.context.fillRect(-this.w / 6, -this.h / 2, this.w / 3, this.h)\n          break\n        }\n      }\n    }\n    this.context.closePath()\n    this.context.restore()\n  }\n}\n", "import { IConfettiOptions } from './Confetti'\nimport Particle from './Particle'\nimport { IRect } from './Rect'\nimport { randomRange } from './utils'\n\nexport interface IParticleGenerator extends IRect {\n  removeParticleAt: (index: number) => void\n  getParticle: () => void\n  animate: (elapsed: number) => boolean\n  particles: Particle[]\n  particlesGenerated: number\n}\n\nexport default class ParticleGenerator implements IParticleGenerator {\n  constructor(canvas: HTMLCanvasElement, getOptions: () => IConfettiOptions) {\n    this.canvas = canvas\n    const ctx = this.canvas.getContext('2d')\n    if (!ctx) {\n      throw new Error('Could not get canvas context')\n    }\n    this.context = ctx\n    this.getOptions = getOptions\n  }\n\n  canvas: HTMLCanvasElement\n\n  context: CanvasRenderingContext2D\n\n  getOptions: () => IConfettiOptions\n\n  x = 0\n\n  y = 0\n\n  w = 0\n\n  h = 0\n\n  lastNumberOfPieces = 0\n\n  tweenProgress = 0\n\n  tweenFrom = 0\n\n  particles: Particle[] = []\n\n  particlesGenerated = 0\n\n  removeParticleAt = (i: number) => {\n    this.particles.splice(i, 1)\n  }\n\n  getParticle = () => {\n    const newParticleX = randomRange(this.x, this.w + this.x)\n    const newParticleY = randomRange(this.y, this.h + this.y)\n    return new Particle(\n      this.context,\n      this.getOptions,\n      newParticleX,\n      newParticleY,\n    )\n  }\n\n  animate = (elapsed: number): boolean => {\n    const { canvas, context, particlesGenerated, lastNumberOfPieces } = this\n    const {\n      run,\n      recycle,\n      numberOfPieces,\n      debug,\n      tweenFunction,\n      tweenDuration,\n    } = this.getOptions()\n    if (!run) {\n      return false\n    }\n\n    const nP = this.particles.length\n    const activeCount = recycle ? nP : particlesGenerated\n\n    // Initial population\n    if (activeCount < numberOfPieces) {\n      // Use the numberOfPieces prop as a key to reset the easing timing\n      if (lastNumberOfPieces !== numberOfPieces) {\n        this.tweenProgress = 0\n        this.tweenFrom = activeCount\n        this.lastNumberOfPieces = numberOfPieces\n      }\n\n      // Clamp tweenProgress between 0 and tweenDuration\n      this.tweenProgress = Math.min(\n        tweenDuration,\n        Math.max(0, this.tweenProgress + elapsed),\n      )\n      const tweenedVal = tweenFunction(\n        this.tweenProgress,\n        this.tweenFrom,\n        numberOfPieces,\n        tweenDuration,\n      )\n      const numToAdd = Math.round(tweenedVal - activeCount)\n      for (let i = 0; i < numToAdd; i++) {\n        this.particles.push(this.getParticle())\n      }\n      this.particlesGenerated += numToAdd\n    }\n    if (debug) {\n      // Draw debug text\n      context.font = '12px sans-serif'\n      context.fillStyle = '#333'\n      context.textAlign = 'right'\n      context.fillText(\n        `Particles: ${nP}`,\n        canvas.width - 10,\n        canvas.height - 20,\n      )\n    }\n\n    // Maintain the population, iterating backwards to prevent issues when removing particles\n    for (let i = this.particles.length - 1; i >= 0; i--) {\n      const p = this.particles[i]\n      // Update each particle's position\n      p.update(elapsed)\n      // Prune the off-canvas particles\n      if (\n        p.y > canvas.height ||\n        p.y < -100 ||\n        p.x > canvas.width + 100 ||\n        p.x < -100\n      ) {\n        if (recycle && activeCount <= numberOfPieces) {\n          // Replace the particle with a brand new one\n          this.particles[i] = this.getParticle()\n        } else {\n          this.removeParticleAt(i)\n        }\n      }\n    }\n    return nP > 0 || activeCount < numberOfPieces\n  }\n}\n", "import * as tweens from 'tween-functions'\nimport ParticleGenerator from './ParticleGenerator'\nimport { IRect } from './Rect'\n\nexport interface IConfettiOptions {\n  /**\n   * Width of the component\n   * @default window.width\n   */\n  width: number\n  /**\n   * Height of the component\n   * @default window.height\n   */\n  height: number\n  /**\n   * Max number of confetti pieces to render.\n   * @default 200\n   */\n  numberOfPieces: number\n  /**\n   * Slows movement of pieces. (lower number = slower confetti)\n   * @default 0.99\n   */\n  friction: number\n  /**\n   * Blows confetti along the X axis.\n   * @default 0\n   */\n  wind: number\n  /**\n   * How fast it falls (pixels per frame)\n   * @default 0.1\n   */\n  gravity: number\n  /**\n   * How fast the confetti is emitted horizontally\n   * @default 4\n   */\n  initialVelocityX: { min: number; max: number } | number\n  /**\n   * How fast the confetti is emitted vertically\n   * @default 10\n   */\n  initialVelocityY: { min: number; max: number } | number\n  /**\n   * Array of colors to choose from.\n   */\n  colors: string[]\n  /**\n   * Opacity of the confetti.\n   * @default 1\n   */\n  opacity: number\n  /**\n   * If false, only numberOfPieces will be emitted and then stops. If true, when a confetto goes offscreen, a new one will be emitted.\n   * @default true\n   */\n  recycle: boolean\n  /**\n   * If false, stops the requestAnimationFrame loop.\n   * @default true\n   */\n  run: boolean\n  /**\n   * The frame rate of the animation. If set, the animation will be throttled to that frame rate.\n   * @default undefined\n   */\n  frameRate?: number\n  /**\n   * Renders some debug text on the canvas.\n   * @default false\n   */\n  debug: boolean\n  /**\n   * A Rect defining the area where the confetti will spawn.\n   * @default {\n   *   x: 0,\n   *   y: 0,\n   *   w: canvas.width,\n   *   h: 0\n   * }\n   */\n  confettiSource: IRect\n  /**\n   * Controls the rate at which confetti is spawned.\n   * @default easeInOutQuad\n   */\n  tweenFunction: (\n    currentTime: number,\n    currentValue: number,\n    targetValue: number,\n    duration: number,\n    s?: number,\n  ) => number\n  /**\n   * Number of milliseconds it should take to spawn numberOfPieces.\n   * @default 5000\n   */\n  tweenDuration: number\n  /**\n   * Function to draw your own confetti shapes.\n   */\n  drawShape?: (context: CanvasRenderingContext2D) => void\n  /**\n   * Function called when all confetti has fallen off-canvas.\n   */\n  onConfettiComplete?: (confettiInstance?: Confetti) => void\n}\n\nexport const confettiDefaults: Pick<\n  IConfettiOptions,\n  Exclude<keyof IConfettiOptions, 'confettiSource'>\n> = {\n  width: typeof window !== 'undefined' ? window.innerWidth : 300,\n  height: typeof window !== 'undefined' ? window.innerHeight : 200,\n  numberOfPieces: 200,\n  friction: 0.99,\n  wind: 0,\n  gravity: 0.1,\n  initialVelocityX: 4,\n  initialVelocityY: 10,\n  colors: [\n    '#f44336',\n    '#e91e63',\n    '#9c27b0',\n    '#673ab7',\n    '#3f51b5',\n    '#2196f3',\n    '#03a9f4',\n    '#00bcd4',\n    '#009688',\n    '#4CAF50',\n    '#8BC34A',\n    '#CDDC39',\n    '#FFEB3B',\n    '#FFC107',\n    '#FF9800',\n    '#FF5722',\n    '#795548',\n  ],\n  opacity: 1.0,\n  debug: false,\n  tweenFunction: tweens.easeInOutQuad,\n  tweenDuration: 5000,\n  recycle: true,\n  run: true,\n}\n\nexport class Confetti {\n  constructor(canvas: HTMLCanvasElement, opts: Partial<IConfettiOptions>) {\n    this.canvas = canvas\n    const ctx = this.canvas.getContext('2d')\n    if (!ctx) {\n      throw new Error('Could not get canvas context')\n    }\n    this.context = ctx\n\n    this.generator = new ParticleGenerator(\n      this.canvas,\n      () => this.options as IConfettiOptions,\n    )\n    this.options = opts\n    this.update()\n  }\n\n  canvas: HTMLCanvasElement\n\n  context: CanvasRenderingContext2D\n\n  _options!: IConfettiOptions\n\n  generator: ParticleGenerator\n\n  rafId?: number\n\n  lastFrameTime = 0\n\n  get options(): Partial<IConfettiOptions> {\n    return this._options\n  }\n\n  set options(opts: Partial<IConfettiOptions>) {\n    const lastRunState = this._options?.run\n    const lastRecycleState = this._options?.recycle\n    this.setOptionsWithDefaults(opts)\n    if (this.generator) {\n      Object.assign(this.generator, this.options.confettiSource)\n      if (\n        typeof opts.recycle === 'boolean' &&\n        opts.recycle &&\n        lastRecycleState === false\n      ) {\n        this.generator.lastNumberOfPieces = this.generator.particles.length\n      }\n    }\n    if (typeof opts.run === 'boolean' && opts.run && lastRunState === false) {\n      this.update()\n    }\n  }\n\n  setOptionsWithDefaults = (opts: Partial<IConfettiOptions>) => {\n    const computedConfettiDefaults = {\n      confettiSource: {\n        x: 0,\n        y: 0,\n        w: this.canvas.width,\n        h: 0,\n      },\n    }\n    this._options = {\n      ...computedConfettiDefaults,\n      ...confettiDefaults,\n      ...opts,\n    }\n    Object.assign(this, opts.confettiSource)\n  }\n\n  update = (timestamp = 0) => {\n    const {\n      options: { run, onConfettiComplete, frameRate },\n      canvas,\n      context,\n    } = this\n    // Cap elapsed time to 50ms to prevent large time steps\n    const elapsed = Math.min(timestamp - this.lastFrameTime, 50)\n    // Throttle the frame rate if set\n    if (frameRate && elapsed < 1000 / frameRate) {\n      this.rafId = requestAnimationFrame(this.update)\n      return\n    }\n\n    this.lastFrameTime = timestamp - (frameRate ? elapsed % frameRate : 0)\n\n    if (run) {\n      context.fillStyle = 'white'\n      context.clearRect(0, 0, canvas.width, canvas.height)\n    }\n    if (this.generator.animate(elapsed)) {\n      this.rafId = requestAnimationFrame(this.update)\n    } else {\n      if (\n        onConfettiComplete &&\n        typeof onConfettiComplete === 'function' &&\n        this.generator.particlesGenerated > 0\n      ) {\n        onConfettiComplete.call(this, this)\n      }\n      this._options.run = false\n    }\n  }\n\n  reset = () => {\n    if (this.generator && this.generator.particlesGenerated > 0) {\n      this.generator.particlesGenerated = 0\n      this.generator.particles = []\n      this.generator.lastNumberOfPieces = 0\n    }\n  }\n\n  stop = () => {\n    this.options = { run: false }\n    if (this.rafId) {\n      cancelAnimationFrame(this.rafId)\n      this.rafId = undefined\n    }\n  }\n}\n\nexport default Confetti\n", "import React from 'react'\nimport Confetti, { IConfettiOptions, confettiDefaults } from './Confetti'\n\nexport type { IConfettiOptions } from './Confetti'\n\nconst ref = React.createRef<HTMLCanvasElement>()\n\nexport type Props = Partial<IConfettiOptions> &\n  React.CanvasHTMLAttributes<HTMLCanvasElement> & {\n    canvasRef?: React.Ref<HTMLCanvasElement>\n  }\n\nclass ReactConfettiInternal extends React.Component<Props> {\n  static readonly defaultProps = {\n    ...confettiDefaults,\n  }\n\n  static readonly displayName = 'ReactConfetti'\n\n  constructor(props: Props) {\n    super(props)\n    this.canvas = (props.canvasRef as React.RefObject<HTMLCanvasElement>) || ref\n  }\n\n  canvas: React.RefObject<HTMLCanvasElement | null> = React.createRef()\n\n  confetti?: Confetti\n\n  componentDidMount() {\n    if (this.canvas.current) {\n      const opts = extractCanvasProps(this.props)[0]\n      this.confetti = new Confetti(this.canvas.current, opts)\n    }\n  }\n\n  componentDidUpdate() {\n    const confettiOptions = extractCanvasProps(this.props)[0]\n    if (this.confetti) {\n      this.confetti.options = confettiOptions as IConfettiOptions\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.confetti) {\n      this.confetti.stop()\n    }\n    this.confetti = undefined\n  }\n\n  render() {\n    const [confettiOptions, passedProps] = extractCanvasProps(this.props)\n    const canvasStyles = {\n      zIndex: 2,\n      position: 'absolute' as const,\n      pointerEvents: 'none' as const,\n      top: 0,\n      left: 0,\n      bottom: 0,\n      right: 0,\n      ...passedProps.style,\n    }\n    return (\n      <canvas\n        width={confettiOptions.width}\n        height={confettiOptions.height}\n        ref={this.canvas}\n        {...passedProps}\n        style={canvasStyles}\n      />\n    )\n  }\n}\n\ninterface Refs {\n  [key: string]: React.Ref<HTMLElement>\n}\nfunction extractCanvasProps(\n  props: Partial<IConfettiOptions> | any,\n): [\n  Partial<IConfettiOptions>,\n  Partial<React.CanvasHTMLAttributes<HTMLCanvasElement>>,\n  Refs,\n] {\n  const confettiOptions: Partial<IConfettiOptions> = {}\n  const refs: Refs = {}\n  const rest: any = {}\n  const confettiOptionKeys = [\n    ...Object.keys(confettiDefaults),\n    'confettiSource',\n    'drawShape',\n    'onConfettiComplete',\n    'frameRate',\n  ]\n  const refProps = ['canvasRef']\n  for (const prop in props) {\n    const val = props[prop as string]\n    if (confettiOptionKeys.includes(prop)) {\n      confettiOptions[prop as keyof IConfettiOptions] = val\n    } else if (refProps.includes(prop)) {\n      refProps[prop as any] = val\n    } else {\n      rest[prop] = val\n    }\n  }\n  return [confettiOptions, rest, refs]\n}\n\nexport const ReactConfetti = React.forwardRef<HTMLCanvasElement, Props>(\n  (props, ref) => <ReactConfettiInternal canvasRef={ref} {...props} />,\n)\n\nexport default ReactConfetti\n"], "names": ["tweens", "_jsx"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IA4EM,SAAU,aAAa,CAAC,OAAe,EAAA;QAC3C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,IAAI,GAAG;IAClC;IAMgB,SAAA,WAAW,CAAC,GAAW,EAAE,GAAW,EAAA;IAClD,IAAA,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC;IAC1C;IAEgB,SAAA,SAAS,CAAC,GAAW,EAAE,GAAW,EAAA;IAChD,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC1D;;ICvFA,IAAY,aAIX;IAJD,CAAA,UAAY,aAAa,EAAA;IACvB,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;IACV,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;IACV,IAAA,aAAA,CAAA,aAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;IACX,CAAC,EAJW,aAAa,KAAb,aAAa,GAIxB,EAAA,CAAA,CAAA;IAED,IAAK,iBAGJ;IAHD,CAAA,UAAK,iBAAiB,EAAA;IACpB,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;IACZ,IAAA,iBAAA,CAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAa;IACf,CAAC,EAHI,iBAAiB,KAAjB,iBAAiB,GAGrB,EAAA,CAAA,CAAA;IAED,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAE;IAEtB,MAAO,QAAQ,CAAA;IAC3B,IAAA,WAAA,CACE,OAAiC,EACjC,UAAkC,EAClC,CAAS,EACT,CAAS,EAAA;IAET,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;IAC5B,QAAA,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE;IACxE,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO;IACtB,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC;IACV,QAAA,IAAI,CAAC,CAAC,GAAG,CAAC;YACV,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC;IAChC,QAAA,IAAI,CAAC,EAAE;gBACL,OAAO,gBAAgB,KAAK;IAC1B,kBAAE,WAAW,CAAC,CAAC,gBAAgB,EAAE,gBAAgB;sBAC/C,WAAW,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC;IAC7D,QAAA,IAAI,CAAC,EAAE;gBACL,OAAO,gBAAgB,KAAK;IAC1B,kBAAE,WAAW,CAAC,CAAC,gBAAgB,EAAE,CAAC;sBAChC,WAAW,CAAC,gBAAgB,CAAC,GAAG,EAAE,gBAAgB,CAAC,GAAG,CAAC;YAC7D,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B,QAAA,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC/C,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC;IACzC,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9D,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC;kBACrC,iBAAiB,CAAC;IACpB,cAAE,iBAAiB,CAAC,QAAQ;;IAkChC,IAAA,MAAM,CAAC,OAAe,EAAA;IACpB,QAAA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE;IACzE,QAAA,MAAM,mBAAmB,GAAG,OAAO,GAAG,kBAAkB;YACxD,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,mBAAmB;YACvC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,GAAG,mBAAmB;IACvC,QAAA,IAAI,CAAC,EAAE,IAAI,OAAO,GAAG,mBAAmB;IACxC,QAAA,IAAI,CAAC,EAAE,IAAI,IAAI,GAAG,mBAAmB;IACrC,QAAA,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,mBAAmB;IAC1C,QAAA,IAAI,CAAC,EAAE,IAAI,QAAQ,IAAI,mBAAmB;IAC1C,QAAA,IACE,IAAI,CAAC,OAAO,IAAI,CAAC;IACjB,YAAA,IAAI,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,EACrD;IACA,YAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ;;IAC9C,aAAA,IACL,IAAI,CAAC,OAAO,IAAI,EAAE;IAClB,YAAA,IAAI,CAAC,iBAAiB,KAAK,iBAAiB,CAAC,QAAQ,EACrD;IACA,YAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ;;YAGrD,MAAM,WAAW,GAAG,GAAG,GAAG,IAAI,CAAC,iBAAiB,GAAG,mBAAmB;IAEtE,QAAA,IAAI,CAAC,OAAO,IAAI,WAAW;IAC3B,QAAA,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW;IAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;IACnB,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC/B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;IAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK;YACnC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK;IACrC,QAAA,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,OAAO;IAClC,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO;IAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC;IAC1B,QAAA,IAAI,SAAS,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;gBAChD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC;;iBAC7B;IACL,YAAA,QAAQ,IAAI,CAAC,KAAK;IAChB,gBAAA,KAAK,aAAa,CAAC,MAAM,EAAE;IACzB,oBAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;wBACxB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;IACnD,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;wBACnB;;IAEF,gBAAA,KAAK,aAAa,CAAC,MAAM,EAAE;wBACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;wBAC/D;;IAEF,gBAAA,KAAK,aAAa,CAAC,KAAK,EAAE;IACxB,oBAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;wBACnE;;;;IAIN,QAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;IACxB,QAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;;IAEzB;;IC9Ha,MAAO,iBAAiB,CAAA;QACpC,WAAY,CAAA,MAAyB,EAAE,UAAkC,EAAA;YAgBzE,IAAC,CAAA,CAAA,GAAG,CAAC;YAEL,IAAC,CAAA,CAAA,GAAG,CAAC;YAEL,IAAC,CAAA,CAAA,GAAG,CAAC;YAEL,IAAC,CAAA,CAAA,GAAG,CAAC;YAEL,IAAkB,CAAA,kBAAA,GAAG,CAAC;YAEtB,IAAa,CAAA,aAAA,GAAG,CAAC;YAEjB,IAAS,CAAA,SAAA,GAAG,CAAC;YAEb,IAAS,CAAA,SAAA,GAAe,EAAE;YAE1B,IAAkB,CAAA,kBAAA,GAAG,CAAC;IAEtB,QAAA,IAAA,CAAA,gBAAgB,GAAG,CAAC,CAAS,KAAI;gBAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7B,SAAC;YAED,IAAW,CAAA,WAAA,GAAG,MAAK;IACjB,YAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACzD,YAAA,MAAM,YAAY,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;IACzD,YAAA,OAAO,IAAI,QAAQ,CACjB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,UAAU,EACf,YAAY,EACZ,YAAY,CACb;IACH,SAAC;IAED,QAAA,IAAA,CAAA,OAAO,GAAG,CAAC,OAAe,KAAa;gBACrC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,IAAI;IACxE,YAAA,MAAM,EACJ,GAAG,EACH,OAAO,EACP,cAAc,EACd,KAAK,EACL,aAAa,EACb,aAAa,GACd,GAAG,IAAI,CAAC,UAAU,EAAE;gBACrB,IAAI,CAAC,GAAG,EAAE;IACR,gBAAA,OAAO,KAAK;;IAGd,YAAA,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;gBAChC,MAAM,WAAW,GAAG,OAAO,GAAG,EAAE,GAAG,kBAAkB;;IAGrD,YAAA,IAAI,WAAW,GAAG,cAAc,EAAE;;IAEhC,gBAAA,IAAI,kBAAkB,KAAK,cAAc,EAAE;IACzC,oBAAA,IAAI,CAAC,aAAa,GAAG,CAAC;IACtB,oBAAA,IAAI,CAAC,SAAS,GAAG,WAAW;IAC5B,oBAAA,IAAI,CAAC,kBAAkB,GAAG,cAAc;;;oBAI1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAC3B,aAAa,EACb,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,CAC1C;IACD,gBAAA,MAAM,UAAU,GAAG,aAAa,CAC9B,IAAI,CAAC,aAAa,EAClB,IAAI,CAAC,SAAS,EACd,cAAc,EACd,aAAa,CACd;oBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC;IACrD,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;wBACjC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;;IAEzC,gBAAA,IAAI,CAAC,kBAAkB,IAAI,QAAQ;;gBAErC,IAAI,KAAK,EAAE;;IAET,gBAAA,OAAO,CAAC,IAAI,GAAG,iBAAiB;IAChC,gBAAA,OAAO,CAAC,SAAS,GAAG,MAAM;IAC1B,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO;IAC3B,gBAAA,OAAO,CAAC,QAAQ,CACd,cAAc,EAAE,CAAA,CAAE,EAClB,MAAM,CAAC,KAAK,GAAG,EAAE,EACjB,MAAM,CAAC,MAAM,GAAG,EAAE,CACnB;;;IAIH,YAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;oBACnD,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;IAE3B,gBAAA,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;;IAEjB,gBAAA,IACE,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM;IACnB,oBAAA,CAAC,CAAC,CAAC,GAAG,IAAI;IACV,oBAAA,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG;IACxB,oBAAA,CAAC,CAAC,CAAC,GAAG,IAAI,EACV;IACA,oBAAA,IAAI,OAAO,IAAI,WAAW,IAAI,cAAc,EAAE;;4BAE5C,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE;;6BACjC;IACL,wBAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;;;IAI9B,YAAA,OAAO,EAAE,GAAG,CAAC,IAAI,WAAW,GAAG,cAAc;IAC/C,SAAC;IA5HC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,GAAG,EAAE;IACR,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;;IAEjD,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG;IAClB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;;IAuH/B;;IC9BM,MAAM,gBAAgB,GAGzB;IACF,IAAA,KAAK,EAAE,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,UAAU,GAAG,GAAG;IAC9D,IAAA,MAAM,EAAE,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,CAAC,WAAW,GAAG,GAAG;IAChE,IAAA,cAAc,EAAE,GAAG;IACnB,IAAA,QAAQ,EAAE,IAAI;IACd,IAAA,IAAI,EAAE,CAAC;IACP,IAAA,OAAO,EAAE,GAAG;IACZ,IAAA,gBAAgB,EAAE,CAAC;IACnB,IAAA,gBAAgB,EAAE,EAAE;IACpB,IAAA,MAAM,EAAE;YACN,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;IACV,KAAA;IACD,IAAA,OAAO,EAAE,GAAG;IACZ,IAAA,KAAK,EAAE,KAAK;QACZ,aAAa,EAAEA,iBAAM,CAAC,aAAa;IACnC,IAAA,aAAa,EAAE,IAAI;IACnB,IAAA,OAAO,EAAE,IAAI;IACb,IAAA,GAAG,EAAE,IAAI;KACV;UAEY,QAAQ,CAAA;QACnB,WAAY,CAAA,MAAyB,EAAE,IAA+B,EAAA;YA0BtE,IAAa,CAAA,aAAA,GAAG,CAAC;IAyBjB,QAAA,IAAA,CAAA,sBAAsB,GAAG,CAAC,IAA+B,KAAI;IAC3D,YAAA,MAAM,wBAAwB,GAAG;IAC/B,gBAAA,cAAc,EAAE;IACd,oBAAA,CAAC,EAAE,CAAC;IACJ,oBAAA,CAAC,EAAE,CAAC;IACJ,oBAAA,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;IACpB,oBAAA,CAAC,EAAE,CAAC;IACL,iBAAA;iBACF;gBACD,IAAI,CAAC,QAAQ,GAAG;IACd,gBAAA,GAAG,wBAAwB;IAC3B,gBAAA,GAAG,gBAAgB;IACnB,gBAAA,GAAG,IAAI;iBACR;gBACD,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC;IAC1C,SAAC;IAED,QAAA,IAAA,CAAA,MAAM,GAAG,CAAC,SAAS,GAAG,CAAC,KAAI;IACzB,YAAA,MAAM,EACJ,OAAO,EAAE,EAAE,GAAG,EAAE,kBAAkB,EAAE,SAAS,EAAE,EAC/C,MAAM,EACN,OAAO,GACR,GAAG,IAAI;;IAER,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;;gBAE5D,IAAI,SAAS,IAAI,OAAO,GAAG,IAAI,GAAG,SAAS,EAAE;oBAC3C,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;oBAC/C;;IAGF,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,IAAI,SAAS,GAAG,OAAO,GAAG,SAAS,GAAG,CAAC,CAAC;gBAEtE,IAAI,GAAG,EAAE;IACP,gBAAA,OAAO,CAAC,SAAS,GAAG,OAAO;IAC3B,gBAAA,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC;;gBAEtD,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBACnC,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;;qBAC1C;IACL,gBAAA,IACE,kBAAkB;wBAClB,OAAO,kBAAkB,KAAK,UAAU;IACxC,oBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC,EACrC;IACA,oBAAA,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;;IAErC,gBAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,KAAK;;IAE7B,SAAC;YAED,IAAK,CAAA,KAAA,GAAG,MAAK;IACX,YAAA,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC,EAAE;IAC3D,gBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC;IACrC,gBAAA,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE;IAC7B,gBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,CAAC;;IAEzC,SAAC;YAED,IAAI,CAAA,IAAA,GAAG,MAAK;gBACV,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;IAC7B,YAAA,IAAI,IAAI,CAAC,KAAK,EAAE;IACd,gBAAA,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC;IAChC,gBAAA,IAAI,CAAC,KAAK,GAAG,SAAS;;IAE1B,SAAC;IAnHC,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM;YACpB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;YACxC,IAAI,CAAC,GAAG,EAAE;IACR,YAAA,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;;IAEjD,QAAA,IAAI,CAAC,OAAO,GAAG,GAAG;IAElB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,iBAAiB,CACpC,IAAI,CAAC,MAAM,EACX,MAAM,IAAI,CAAC,OAA2B,CACvC;IACD,QAAA,IAAI,CAAC,OAAO,GAAG,IAAI;YACnB,IAAI,CAAC,MAAM,EAAE;;IAef,IAAA,IAAI,OAAO,GAAA;YACT,OAAO,IAAI,CAAC,QAAQ;;QAGtB,IAAI,OAAO,CAAC,IAA+B,EAAA;IACzC,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG;IACvC,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,QAAQ,EAAE,OAAO;IAC/C,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;IACjC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;IAClB,YAAA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;IAC1D,YAAA,IACE,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS;IACjC,gBAAA,IAAI,CAAC,OAAO;oBACZ,gBAAgB,KAAK,KAAK,EAC1B;IACA,gBAAA,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM;;;IAGvE,QAAA,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,IAAI,YAAY,KAAK,KAAK,EAAE;gBACvE,IAAI,CAAC,MAAM,EAAE;;;IAsElB;;ICtQD,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,EAAqB;IAOhD,MAAM,qBAAsB,SAAQ,KAAK,CAAC,SAAgB,CAAA;IAOxD,IAAA,WAAA,CAAY,KAAY,EAAA;YACtB,KAAK,CAAC,KAAK,CAAC;IAId,QAAA,IAAA,CAAA,MAAM,GAA8C,KAAK,CAAC,SAAS,EAAE;YAHnE,IAAI,CAAC,MAAM,GAAI,KAAK,CAAC,SAAgD,IAAI,GAAG;;QAO9E,iBAAiB,GAAA;IACf,QAAA,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;gBACvB,MAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9C,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;;;QAI3D,kBAAkB,GAAA;YAChB,MAAM,eAAe,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;IACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,eAAmC;;;QAI/D,oBAAoB,GAAA;IAClB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;IACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;;IAEtB,QAAA,IAAI,CAAC,QAAQ,GAAG,SAAS;;QAG3B,MAAM,GAAA;IACJ,QAAA,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC;IACrE,QAAA,MAAM,YAAY,GAAG;IACnB,YAAA,MAAM,EAAE,CAAC;IACT,YAAA,QAAQ,EAAE,UAAmB;IAC7B,YAAA,aAAa,EAAE,MAAe;IAC9B,YAAA,GAAG,EAAE,CAAC;IACN,YAAA,IAAI,EAAE,CAAC;IACP,YAAA,MAAM,EAAE,CAAC;IACT,YAAA,KAAK,EAAE,CAAC;gBACR,GAAG,WAAW,CAAC,KAAK;aACrB;YACD,QACEC,cACE,CAAA,QAAA,EAAA,EAAA,KAAK,EAAE,eAAe,CAAC,KAAK,EAC5B,MAAM,EAAE,eAAe,CAAC,MAAM,EAC9B,GAAG,EAAE,IAAI,CAAC,MAAM,EACZ,GAAA,WAAW,EACf,KAAK,EAAE,YAAY,EACnB,CAAA;;;IAvDU,qBAAA,CAAA,YAAY,GAAG;IAC7B,IAAA,GAAG,gBAAgB;IACpB,CAF2B;IAIZ,qBAAW,CAAA,WAAA,GAAG,eAAH;IA2D7B,SAAS,kBAAkB,CACzB,KAAsC,EAAA;QAMtC,MAAM,eAAe,GAA8B,EAAE;QACrD,MAAM,IAAI,GAAS,EAAE;QACrB,MAAM,IAAI,GAAQ,EAAE;IACpB,IAAA,MAAM,kBAAkB,GAAG;IACzB,QAAA,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;YAChC,gBAAgB;YAChB,WAAW;YACX,oBAAoB;YACpB,WAAW;SACZ;IACD,IAAA,MAAM,QAAQ,GAAG,CAAC,WAAW,CAAC;IAC9B,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;IACxB,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAc,CAAC;IACjC,QAAA,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACrC,YAAA,eAAe,CAAC,IAA8B,CAAC,GAAG,GAAG;;IAChD,aAAA,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAClC,YAAA,QAAQ,CAAC,IAAW,CAAC,GAAG,GAAG;;iBACtB;IACL,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;;;IAGpB,IAAA,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC;IACtC;AAEa,UAAA,aAAa,GAAG,KAAK,CAAC,UAAU,CAC3C,CAAC,KAAK,EAAE,GAAG,KAAKA,cAAC,CAAA,qBAAqB,EAAC,EAAA,SAAS,EAAE,GAAG,EAAM,GAAA,KAAK,EAAI,CAAA;;;;;;;;"}