#!/usr/bin/env python
"""
Fix Production Issues Script
============================

This script fixes common production issues:
1. Missing signup bonuses for existing users
2. Missing token discount settings on products
3. Missing game types in database
4. Wallet creation for users without wallets

Run this script after deployment to ensure everything works correctly.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from wallet.models import Wallet, WalletTransaction
from products.models import Product
from gaming.models import GameType


def fix_signup_bonuses():
    """Fix missing signup bonuses for existing users"""
    print("\n🪙 Fixing Signup Bonuses")
    print("=" * 30)
    
    users_fixed = 0
    users_already_have_bonus = 0
    
    for user in User.objects.all():
        # Ensure user has a wallet
        wallet, wallet_created = Wallet.objects.get_or_create(user=user)
        
        if wallet_created:
            print(f"✅ Created wallet for {user.username}")
        
        # Check if user already has signup bonus
        has_signup_bonus = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='signup_bonus'
        ).exists()
        
        if has_signup_bonus:
            users_already_have_bonus += 1
            continue
        
        # Give signup bonus
        wallet.add_tokens(
            amount=100,
            transaction_type='signup_bonus',
            description='Welcome bonus (retroactive fix)'
        )
        print(f"✅ Gave 100 token signup bonus to {user.username}")
        users_fixed += 1
    
    print(f"\n📊 Signup Bonus Summary:")
    print(f"   Users already had bonus: {users_already_have_bonus}")
    print(f"   Users fixed: {users_fixed}")


def fix_token_discounts():
    """Enable token discounts on products"""
    print("\n💰 Fixing Token Discounts")
    print("=" * 30)
    
    products_updated = 0
    products_already_enabled = 0
    
    for product in Product.objects.filter(is_active=True):
        if product.token_discount_available:
            products_already_enabled += 1
            continue
        
        # Enable token discount
        product.token_discount_available = True
        product.token_discount_percentage = 20  # 20% discount
        product.token_discount_max_amount = 100.0  # Max ₹100 discount
        product.save()
        
        print(f"✅ Enabled 20% token discount (max ₹100) for: {product.name}")
        products_updated += 1
    
    print(f"\n📊 Token Discount Summary:")
    print(f"   Products already enabled: {products_already_enabled}")
    print(f"   Products updated: {products_updated}")


def fix_game_types():
    """Ensure game types exist in database"""
    print("\n🎮 Fixing Game Types")
    print("=" * 30)
    
    game_types = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic rock, paper, scissors game. Best of 3 rounds wins!',
            'rules': {
                'max_rounds': 3,
                'choices': ['rock', 'paper', 'scissors'],
                'winning_combinations': {
                    'rock': 'scissors',
                    'paper': 'rock',
                    'scissors': 'paper'
                }
            }
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number between 1-100. Closest guess wins the round!',
            'rules': {
                'max_rounds': 5,
                'range': [1, 100],
                'scoring': 'closest_wins'
            }
        },
        {
            'name': 'tic_tac_toe',
            'display_name': 'Tic Tac Toe',
            'description': 'Classic strategy game against AI. Get 3 in a row to win!',
            'rules': {
                'board_size': 3,
                'win_condition': '3_in_a_row',
                'ai_difficulty': 'hard'
            }
        }
    ]
    
    games_created = 0
    games_already_exist = 0
    
    for game_data in game_types:
        game_type, created = GameType.objects.get_or_create(
            name=game_data['name'],
            defaults={
                'display_name': game_data['display_name'],
                'description': game_data['description'],
                'rules': game_data['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created game type: {game_type.display_name}")
            games_created += 1
        else:
            games_already_exist += 1
    
    print(f"\n📊 Game Types Summary:")
    print(f"   Games already existed: {games_already_exist}")
    print(f"   Games created: {games_created}")


def verify_fixes():
    """Verify that all fixes were applied correctly"""
    print("\n🔍 Verifying Fixes")
    print("=" * 30)
    
    # Check users with wallets and signup bonuses
    total_users = User.objects.count()
    users_with_wallets = User.objects.filter(wallet__isnull=False).count()
    users_with_signup_bonus = User.objects.filter(
        wallet__transactions__transaction_type='signup_bonus'
    ).distinct().count()
    
    print(f"👥 Users:")
    print(f"   Total users: {total_users}")
    print(f"   Users with wallets: {users_with_wallets}")
    print(f"   Users with signup bonus: {users_with_signup_bonus}")
    
    # Check products with token discounts
    total_products = Product.objects.filter(is_active=True).count()
    products_with_token_discount = Product.objects.filter(
        is_active=True,
        token_discount_available=True
    ).count()
    
    print(f"\n🛍️ Products:")
    print(f"   Total active products: {total_products}")
    print(f"   Products with token discount: {products_with_token_discount}")
    
    # Check game types
    total_game_types = GameType.objects.filter(is_active=True).count()
    
    print(f"\n🎮 Games:")
    print(f"   Active game types: {total_game_types}")
    for game in GameType.objects.filter(is_active=True):
        print(f"   - {game.display_name}")
    
    # Overall status
    all_good = (
        users_with_wallets == total_users and
        users_with_signup_bonus == total_users and
        products_with_token_discount == total_products and
        total_game_types >= 3
    )
    
    if all_good:
        print(f"\n🎉 All fixes applied successfully!")
        print(f"✅ Your production site should now have:")
        print(f"   - All users have wallets with 100 token signup bonus")
        print(f"   - All products have token discount enabled")
        print(f"   - All 3 games are available (Tic Tac Toe, Rock Paper Scissors, Number Guessing)")
        print(f"   - Token discount system working in checkout")
    else:
        print(f"\n⚠️ Some issues may still exist. Check the details above.")


def main():
    """Main function to run all fixes"""
    print("🔧 PickMeTrend Production Issues Fix")
    print("=" * 40)
    print("This script will fix common production issues:")
    print("1. Missing signup bonuses")
    print("2. Missing token discount settings")
    print("3. Missing game types")
    print("4. Verification of all fixes")
    
    try:
        # Run all fixes
        fix_signup_bonuses()
        fix_token_discounts()
        fix_game_types()
        verify_fixes()
        
        print(f"\n🎉 Production fixes completed successfully!")
        print(f"🚀 Your gaming e-commerce platform is now fully functional!")
        
    except Exception as e:
        print(f"\n❌ Error during fixes: {e}")
        print(f"Please check your Django configuration and try again.")
        sys.exit(1)


if __name__ == '__main__':
    main()
