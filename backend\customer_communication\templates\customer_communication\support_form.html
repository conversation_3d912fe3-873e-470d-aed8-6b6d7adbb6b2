{% extends "base.html" %}
{% load static %}
{% load phone_filters %}

{% block title %}{{ title }} | PickMeTrend{% endblock %}

{% block extra_css %}
<style>
    .form-group {
        margin-bottom: 1.5rem;
    }
    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
    }
    .form-control:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
    .btn-primary {
        background-color: #4f46e5;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    .btn-primary:hover {
        background-color: #4338ca;
    }
    .alert {
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1.5rem;
    }
    .alert-success {
        background-color: #ecfdf5;
        color: #065f46;
        border: 1px solid #10b981;
    }
    .alert-danger {
        background-color: #fef2f2;
        color: #b91c1c;
        border: 1px solid #ef4444;
    }
    .hidden {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-12">
    <div class="max-w-3xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">{{ title }}</h1>
        <p class="text-lg text-gray-600 mb-8">{{ description }}</p>

        <div class="bg-white rounded-lg shadow-md p-8">
            <div id="success-message" class="alert alert-success hidden">
                Thank you for contacting us! Your support ticket has been submitted successfully. We'll get back to you as soon as possible.
            </div>

            <div id="error-message" class="alert alert-danger hidden">
                There was an error submitting your support ticket. Please try again or contact us directly.
            </div>

            <form id="support-form" class="space-y-6">
                <div class="form-group">
                    <label for="name" class="form-label">Your Name</label>
                    <input type="text" id="name" name="name" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input type="email" id="email" name="email" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="subject" class="form-label">Subject</label>
                    <input type="text" id="subject" name="subject" class="form-control" required>
                </div>

                <div class="form-group">
                    <label for="message" class="form-label">Message</label>
                    <textarea id="message" name="message" rows="6" class="form-control" required></textarea>
                </div>

                <div class="text-right">
                    <button type="submit" class="btn-primary">Submit</button>
                </div>
            </form>
        </div>

        <div class="mt-12 bg-gray-50 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Contact Information</h2>
            <p class="mb-4">If you prefer to contact us directly:</p>
            <ul class="space-y-2">
                <li>Email: <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></li>
                <li>Phone: <a href="tel:{% company_phone 'link' %}" class="text-blue-600 hover:underline">{% company_phone 'display' %}</a></li>
                <li>Hours: Monday–Friday, 10 AM to 6 PM (IST)</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('support-form');
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Hide any previous messages
            successMessage.classList.add('hidden');
            errorMessage.classList.add('hidden');

            // Get form data
            const formData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                subject: document.getElementById('subject').value,
                message: document.getElementById('message').value
            };

            // Send the data to the API
            fetch('/customer/api/support-form/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Show success message
                successMessage.classList.remove('hidden');
                // Reset form
                form.reset();
            })
            .catch(error => {
                // Show error message
                errorMessage.classList.remove('hidden');
                console.error('Error:', error);
            });
        });

        // Function to get CSRF token from cookies
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
