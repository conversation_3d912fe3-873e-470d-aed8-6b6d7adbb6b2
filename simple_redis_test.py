#!/usr/bin/env python3
"""
Simple Redis Connection Test
"""

try:
    import redis
    print("✅ Redis package is available")
    
    # Your Upstash Redis URL
    redis_url = "redis://default:<EMAIL>:6379"
    
    print("🔄 Testing Upstash Redis connection...")
    
    # Create Redis connection with TLS support
    r = redis.Redis.from_url(
        redis_url,
        ssl_cert_reqs=None,  # Don't verify SSL certificates
        decode_responses=True
    )
    
    # Test connection
    response = r.ping()
    print(f"✅ Connection successful! Ping response: {response}")
    
    # Test basic operations
    r.set('test_key', 'PickMeTrend Production Ready!', ex=30)
    value = r.get('test_key')
    print(f"✅ Read/Write test successful: {value}")
    
    # Clean up
    r.delete('test_key')
    print("✅ Cleanup successful")
    
    print("\n🎉 Upstash Redis is ready for production!")
    
except ImportError:
    print("❌ Redis package not installed")
    print("Run: pip install redis")
    
except Exception as e:
    print(f"❌ Redis connection failed: {e}")
    print("Check your Redis URL and network connectivity")
