from django.http import JsonResponse
from django.db import connection
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
import requests

@csrf_exempt
def debug_printify_api(request):
    """Debug view to test Printify API integration"""
    try:
        result = {
            'status': 'success',
            'checks': []
        }

        # Check 1: Environment Variables
        api_token = getattr(settings, 'PRINTIFY_API_TOKEN', None)
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)

        result['checks'].append({
            'test': 'Environment Variables',
            'status': 'success' if api_token and shop_id else 'error',
            'message': f'API Token: {"✓ Set" if api_token else "✗ Missing"}, Shop ID: {shop_id if shop_id else "✗ Missing"}',
            'api_token_length': len(api_token) if api_token else 0,
            'shop_id': shop_id
        })

        if not api_token or not shop_id:
            return JsonResponse(result)

        # Check 2: API Authentication Test
        try:
            headers = {
                'Authorization': f'Bearer {api_token}',
                'Content-Type': 'application/json'
            }

            # Test basic API connectivity
            auth_response = requests.get(
                'https://api.printify.com/v1/shops.json',
                headers=headers,
                timeout=10
            )

            if auth_response.status_code == 200:
                shops_data = auth_response.json()
                result['checks'].append({
                    'test': 'API Authentication',
                    'status': 'success',
                    'message': f'Successfully authenticated. Found {len(shops_data)} shops.',
                    'shops': shops_data
                })
            else:
                result['checks'].append({
                    'test': 'API Authentication',
                    'status': 'error',
                    'message': f'Authentication failed. Status: {auth_response.status_code}',
                    'response': auth_response.text[:500]
                })

        except Exception as e:
            result['checks'].append({
                'test': 'API Authentication',
                'status': 'error',
                'message': f'API request failed: {str(e)}'
            })

        # Check 3: Shop-specific API Test
        try:
            products_response = requests.get(
                f'https://api.printify.com/v1/shops/{shop_id}/products.json',
                headers=headers,
                timeout=15
            )

            if products_response.status_code == 200:
                products_data = products_response.json()
                product_count = len(products_data.get('data', []))
                result['checks'].append({
                    'test': 'Products API',
                    'status': 'success',
                    'message': f'Successfully retrieved {product_count} products from shop {shop_id}',
                    'product_count': product_count,
                    'first_product': products_data.get('data', [{}])[0] if product_count > 0 else None
                })
            else:
                result['checks'].append({
                    'test': 'Products API',
                    'status': 'error',
                    'message': f'Failed to get products. Status: {products_response.status_code}',
                    'response': products_response.text[:500]
                })

        except Exception as e:
            result['checks'].append({
                'test': 'Products API',
                'status': 'error',
                'message': f'Products API request failed: {str(e)}'
            })

        # Check 4: Database Connection for Sync
        try:
            from products.models import Product, ProductImage

            existing_products = Product.objects.filter(printify_id__isnull=False).count()
            existing_images = ProductImage.objects.count()

            result['checks'].append({
                'test': 'Database Readiness',
                'status': 'success',
                'message': f'Database ready. Existing products with Printify ID: {existing_products}, Images: {existing_images}',
                'existing_products': existing_products,
                'existing_images': existing_images
            })

        except Exception as e:
            result['checks'].append({
                'test': 'Database Readiness',
                'status': 'error',
                'message': f'Database check failed: {str(e)}'
            })

        return JsonResponse(result)

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Critical error: {str(e)}',
            'checks': []
        })

@csrf_exempt
def quick_sync_test(request):
    """Quick sync test - sync only 1 product to avoid timeouts"""
    try:
        from products.admin import ProductAdmin
        from products.models import Product, ProductImage, ProductVariant
        from django.contrib.admin.sites import site
        from django.conf import settings
        from printify.api_client import PrintifyAPIClient
        import time

        start_time = time.time()

        # Get current state before sync
        before_products = Product.objects.count()
        before_printify_products = Product.objects.filter(printify_id__isnull=False).count()
        before_images = ProductImage.objects.count()
        before_variants = ProductVariant.objects.count()

        # Initialize API client
        client = PrintifyAPIClient()
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)

        if not shop_id:
            return JsonResponse({
                'status': 'error',
                'message': 'Shop ID not configured'
            })

        # Get just the first product to test
        try:
            printify_products = client.get_products(shop_id)
            if isinstance(printify_products, dict) and 'data' in printify_products:
                printify_products = printify_products.get('data', [])

            if not printify_products:
                return JsonResponse({
                    'status': 'error',
                    'message': 'No products found in Printify'
                })

            # Test with just the first product
            first_product = printify_products[0]
            printify_id = str(first_product.get('id', ''))

            if not printify_id:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid product ID'
                })

            # Get detailed product info
            printify_product = client.get_product(shop_id, printify_id)

            # Create a ProductAdmin instance and sync this one product
            product_admin = ProductAdmin(Product, site)

            # Manually sync just this one product
            from django.utils.text import slugify
            import decimal

            title = printify_product.get('title', '')
            description = printify_product.get('description', '')
            slug = slugify(title)
            images = printify_product.get('images', [])
            variants = printify_product.get('variants', [])

            # Check if product exists
            try:
                existing_product = Product.objects.get(printify_id=printify_id)
                # Update existing
                existing_product.name = title
                existing_product.description = description
                existing_product.slug = slug
                existing_product.save()
                action = 'updated'
                product = existing_product
            except Product.DoesNotExist:
                # Create new
                product = Product.objects.create(
                    name=title,
                    description=description,
                    slug=slug,
                    printify_id=printify_id,
                    price=decimal.Decimal('10.00'),  # Default price
                    stock=100
                )
                action = 'created'

            # Sync images for this product
            if images:
                # Clear existing images
                ProductImage.objects.filter(product=product).delete()

                # Add new images
                for i, image_data in enumerate(images[:3]):  # Limit to 3 images
                    image_url = image_data.get('src')
                    if image_url:
                        ProductImage.objects.create(
                            product=product,
                            image_url=image_url,
                            alt_text=f"{product.name} - Image {i+1}",
                            is_primary=(i == 0)
                        )

            end_time = time.time()
            duration = end_time - start_time

            # Get state after sync
            after_products = Product.objects.count()
            after_printify_products = Product.objects.filter(printify_id__isnull=False).count()
            after_images = ProductImage.objects.count()
            after_variants = ProductVariant.objects.count()

            return JsonResponse({
                'status': 'success',
                'message': f'Successfully {action} 1 product: {title}',
                'duration_seconds': round(duration, 2),
                'product_action': action,
                'product_name': title,
                'product_id': printify_id,
                'images_synced': len(images),
                'before_sync': {
                    'total_products': before_products,
                    'printify_products': before_printify_products,
                    'images': before_images,
                    'variants': before_variants
                },
                'after_sync': {
                    'total_products': after_products,
                    'printify_products': after_printify_products,
                    'images': after_images,
                    'variants': after_variants
                },
                'changes': {
                    'products_added': after_products - before_products,
                    'images_added': after_images - before_images,
                    'variants_added': after_variants - before_variants
                }
            })

        except Exception as api_error:
            return JsonResponse({
                'status': 'error',
                'message': f'API error: {str(api_error)}',
                'error_type': type(api_error).__name__
            })

    except Exception as e:
        import traceback
        return JsonResponse({
            'status': 'error',
            'message': f'Critical error: {str(e)}',
            'error_type': type(e).__name__,
            'traceback': traceback.format_exc()
        })

@csrf_exempt
def test_sync_products(request):
    """Test endpoint to sync products without admin authentication - LIMITED TO AVOID TIMEOUTS"""
    try:
        from products.admin import ProductAdmin
        from products.models import Product, ProductImage, ProductVariant
        from django.contrib.admin.sites import site
        import time

        start_time = time.time()

        # Get current state before sync
        before_products = Product.objects.count()
        before_printify_products = Product.objects.filter(printify_id__isnull=False).count()
        before_images = ProductImage.objects.count()
        before_variants = ProductVariant.objects.count()

        # Create a ProductAdmin instance
        product_admin = ProductAdmin(Product, site)

        # Test the sync functionality with timeout protection
        try:
            # Set a timeout to prevent 502 errors
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("Sync operation timed out")

            # Set a 45-second timeout
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(45)

            try:
                synced_count = product_admin.sync_products_from_printify()
                signal.alarm(0)  # Cancel the alarm
            except TimeoutError:
                signal.alarm(0)  # Cancel the alarm
                return JsonResponse({
                    'status': 'timeout',
                    'message': 'Sync operation timed out after 45 seconds. This is normal for large syncs.',
                    'suggestion': 'Use the admin interface or try the quick sync endpoint instead.'
                })

            end_time = time.time()
            duration = end_time - start_time

            # Get state after sync
            after_products = Product.objects.count()
            after_printify_products = Product.objects.filter(printify_id__isnull=False).count()
            after_images = ProductImage.objects.count()
            after_variants = ProductVariant.objects.count()

            return JsonResponse({
                'status': 'success',
                'message': f'Successfully synced {synced_count} products from Printify',
                'duration_seconds': round(duration, 2),
                'synced_count': synced_count,
                'before_sync': {
                    'total_products': before_products,
                    'printify_products': before_printify_products,
                    'images': before_images,
                    'variants': before_variants
                },
                'after_sync': {
                    'total_products': after_products,
                    'printify_products': after_printify_products,
                    'images': after_images,
                    'variants': after_variants
                },
                'changes': {
                    'products_added': after_products - before_products,
                    'images_added': after_images - before_images,
                    'variants_added': after_variants - before_variants
                }
            })

        except Exception as sync_error:
            import traceback
            return JsonResponse({
                'status': 'error',
                'message': f'Sync failed: {str(sync_error)}',
                'error_type': type(sync_error).__name__,
                'traceback': traceback.format_exc()
            })

    except Exception as e:
        import traceback
        return JsonResponse({
            'status': 'error',
            'message': f'Critical error: {str(e)}',
            'error_type': type(e).__name__,
            'traceback': traceback.format_exc()
        })

@csrf_exempt
def test_clean_description(request):
    """Test endpoint to test description cleaning functionality"""
    try:
        from products.models import Product
        from products.utils import clean_description, clean_description_with_ai

        # Get a product with description to test
        product = Product.objects.filter(description__isnull=False).exclude(description='').first()

        if not product:
            return JsonResponse({
                'status': 'error',
                'message': 'No products with descriptions found to test'
            })

        # Get the original description
        original_description = product.description

        # Test basic cleaning
        cleaned_basic = clean_description(original_description)

        # Test AI cleaning (will fall back to basic if no API key)
        cleaned_ai = clean_description_with_ai(original_description)

        return JsonResponse({
            'status': 'success',
            'product_name': product.name,
            'product_id': str(product.id),
            'original_description': original_description,
            'cleaned_basic': cleaned_basic,
            'cleaned_ai': cleaned_ai,
            'original_length': len(original_description),
            'cleaned_basic_length': len(cleaned_basic),
            'cleaned_ai_length': len(cleaned_ai),
            'improvements': {
                'basic_reduction': len(original_description) - len(cleaned_basic),
                'ai_reduction': len(original_description) - len(cleaned_ai)
            }
        })

    except Exception as e:
        import traceback
        return JsonResponse({
            'status': 'error',
            'message': f'Error testing clean description: {str(e)}',
            'traceback': traceback.format_exc()
        })

@csrf_exempt
def sync_status(request):
    """Get the current sync status from cache"""
    try:
        from django.core.cache import cache

        sync_status = cache.get('printify_sync_status')

        if not sync_status:
            return JsonResponse({
                'status': 'none',
                'message': 'No sync operation in progress'
            })

        return JsonResponse(sync_status)

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error getting sync status: {str(e)}'
        })

@csrf_exempt
def debug_productimage(request):
    """Debug view to check ProductImage model status"""
    try:
        from products.models import ProductImage, Product

        result = {
            'status': 'success',
            'checks': []
        }

        # Check 1: Can we import the model?
        try:
            result['checks'].append({
                'test': 'Model Import',
                'status': 'success',
                'message': 'ProductImage model imported successfully'
            })
        except Exception as e:
            result['checks'].append({
                'test': 'Model Import',
                'status': 'error',
                'message': f'Failed to import ProductImage: {str(e)}'
            })
            return JsonResponse(result)

        # Check 2: Can we query the model?
        try:
            count = ProductImage.objects.count()
            result['checks'].append({
                'test': 'Model Query',
                'status': 'success',
                'message': f'Found {count} ProductImage records'
            })
        except Exception as e:
            result['checks'].append({
                'test': 'Model Query',
                'status': 'error',
                'message': f'Failed to query ProductImage: {str(e)}'
            })

        # Check 3: Can we access image_url field?
        try:
            test_query = ProductImage.objects.filter(image_url__isnull=False)[:1]
            list(test_query)  # Force evaluation
            result['checks'].append({
                'test': 'image_url Field',
                'status': 'success',
                'message': 'image_url field is accessible'
            })
        except Exception as e:
            result['checks'].append({
                'test': 'image_url Field',
                'status': 'error',
                'message': f'image_url field error: {str(e)}'
            })

        # Check 4: Database table structure
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns
                    WHERE table_name='products_productimage'
                    ORDER BY ordinal_position;
                """)
                columns = cursor.fetchall()

                result['checks'].append({
                    'test': 'Table Structure',
                    'status': 'success',
                    'message': f'Table has {len(columns)} columns',
                    'columns': [{'name': col[0], 'type': col[1], 'nullable': col[2]} for col in columns]
                })
        except Exception as e:
            result['checks'].append({
                'test': 'Table Structure',
                'status': 'error',
                'message': f'Failed to check table structure: {str(e)}'
            })

        # Check 5: Can we create a ProductImage?
        try:
            # Get or create a test product
            test_product, created = Product.objects.get_or_create(
                name='Debug Test Product',
                defaults={
                    'slug': 'debug-test-product',
                    'description': 'Debug test',
                    'price': 1.00,
                    'stock': 1
                }
            )

            # Try to create a test image
            test_image = ProductImage(
                product=test_product,
                image_url='https://example.com/debug.jpg',
                alt_text='Debug test image',
                is_primary=False
            )
            test_image.save()

            result['checks'].append({
                'test': 'Create ProductImage',
                'status': 'success',
                'message': f'Successfully created ProductImage with ID {test_image.id}'
            })

            # Clean up
            test_image.delete()
            if created:
                test_product.delete()

        except Exception as e:
            result['checks'].append({
                'test': 'Create ProductImage',
                'status': 'error',
                'message': f'Failed to create ProductImage: {str(e)}'
            })

        return JsonResponse(result)

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Critical error: {str(e)}',
            'checks': []
        })
