"""
Tic Tac Toe game API endpoints for token integration
"""
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from wallet.models import Wallet
from wallet.game_integration import check_game_eligibility
import uuid


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_tic_tac_toe_game(request):
    """
    Start a new Tic Tac Toe game
    """
    try:
        user = request.user
        difficulty = 'hard'  # Always hard mode

        # Check if user can play games
        eligibility = check_game_eligibility(user.id)
        if not eligibility['can_play']:
            return Response({
                'error': eligibility['message'],
                'can_play': False,
                'balance': eligibility['balance']
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate game ID
        game_id = str(uuid.uuid4())

        return Response({
            'success': True,
            'game_id': game_id,
            'difficulty': difficulty,
            'can_play': True,
            'balance': eligibility['balance'],
            'message': 'Game started successfully'
        })
        
    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_tic_tac_toe_game(request):
    """
    Complete a Tic Tac Toe game and handle token transactions
    """
    try:
        user = request.user
        game_id = request.data.get('game_id')
        game_result = request.data.get('result')  # 'win', 'loss', 'draw'
        difficulty = 'hard'  # Always hard mode
        
        if not game_id or not game_result:
            return Response({
                'error': 'Missing game_id or result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if game_result not in ['win', 'loss', 'draw']:
            return Response({
                'error': 'Invalid game result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get or create wallet
        wallet, _ = Wallet.objects.get_or_create(user=user)
        
        # Tic Tac Toe specific reward structure
        tokens_earned = 0
        transaction_type = ''
        description = ''

        if game_result == 'win':
            # Win: 5 tokens
            tokens_earned = 5
            transaction_type = 'game_win'
            description = f'Tic Tac Toe win (difficulty: {difficulty})'

            # Add tokens to wallet
            wallet.add_tokens(
                amount=tokens_earned,
                transaction_type=transaction_type,
                description=description
            )

        elif game_result == 'draw':
            # Draw: 2 tokens
            tokens_earned = 2
            transaction_type = 'game_draw'
            description = f'Tic Tac Toe draw (difficulty: {difficulty})'

            # Add tokens to wallet
            wallet.add_tokens(
                amount=tokens_earned,
                transaction_type=transaction_type,
                description=description
            )

        elif game_result == 'loss':
            # Loss: -1 token (deduct from wallet)
            transaction_type = 'game_loss'
            description = f'Tic Tac Toe loss (difficulty: {difficulty})'

            # Check if user has enough tokens to deduct
            if wallet.balance >= 1:
                tokens_earned = -1
                wallet.spend_tokens(
                    amount=1,
                    transaction_type=transaction_type,
                    description=description
                )
            else:
                # If no tokens to deduct, give participation reward instead
                tokens_earned = 2
                transaction_type = 'game_participation'
                description = f'Tic Tac Toe participation (difficulty: {difficulty}) - No tokens to deduct'
                wallet.add_tokens(
                    amount=tokens_earned,
                    transaction_type=transaction_type,
                    description=description
                )
        
        # Get updated wallet balance
        wallet.refresh_from_db()
        
        return Response({
            'success': True,
            'tokens_earned': tokens_earned,
            'new_balance': wallet.balance,
            'balance_in_inr': float(wallet.balance_in_inr),
            'transaction_type': transaction_type,
            'description': description,
            'can_play_more': wallet.can_play_games()
        })
        
    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_tic_tac_toe_stats(request):
    """
    Get user's Tic Tac Toe game statistics
    """
    try:
        user = request.user
        wallet, _ = Wallet.objects.get_or_create(user=user)
        
        # Get Tic Tac Toe related transactions
        tic_tac_toe_transactions = wallet.transactions.filter(
            description__icontains='Tic Tac Toe'
        ).order_by('-created_at')
        
        # Calculate stats
        total_games = tic_tac_toe_transactions.count()
        wins = tic_tac_toe_transactions.filter(description__icontains='win').count()
        draws = tic_tac_toe_transactions.filter(description__icontains='draw').count()
        losses = total_games - wins - draws
        
        total_tokens_earned = sum(
            transaction.amount for transaction in tic_tac_toe_transactions
            if transaction.amount > 0
        )
        
        return Response({
            'success': True,
            'stats': {
                'total_games': total_games,
                'wins': wins,
                'losses': losses,
                'draws': draws,
                'total_tokens_earned': total_tokens_earned,
                'current_balance': wallet.balance,
                'balance_in_inr': float(wallet.balance_in_inr)
            },
            'recent_games': [
                {
                    'date': transaction.created_at.isoformat(),
                    'result': 'win' if 'win' in transaction.description else 
                             'draw' if 'draw' in transaction.description else 'loss',
                    'tokens_earned': transaction.amount,
                    'description': transaction.description
                }
                for transaction in tic_tac_toe_transactions[:10]  # Last 10 games
            ]
        })
        
    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
