# 🚀 Separate Repository Deployment Guide

Instructions for pushing frontend and backend to separate GitHub repositories and deploying them independently.

## 📁 **Repository Structure**

### **Backend Repository**: `pickmetrendofficial-render`
- Django REST API
- Gaming system
- Wallet/Token economy
- E-commerce API
- Admin panel
- **Repository**: https://github.com/phinihas30/pickmetrendofficial-render.git

### **Frontend Repository**: `pickmetrend-frontend` (to be created)
- React TypeScript app
- Gaming interface
- E-commerce UI
- Wallet integration
- Payment flow

## 🔧 **Step 1: Prepare Backend Repository**

### **Create Backend Repository**
```bash
# Navigate to backend directory
cd backend

# Initialize git repository
git init

# Add all backend files
git add .

# Create initial commit
git commit -m "Initial backend setup with gaming system and e-commerce API"

# Add your GitHub repository as remote
git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### **Backend Files to Include**
- ✅ All Django apps and models
- ✅ `requirements.txt` with all dependencies
- ✅ `render.yaml` for deployment
- ✅ Gaming system (Tic Tac Toe API)
- ✅ Wallet system
- ✅ Production readiness scripts
- ✅ README.md with deployment instructions

## 🎨 **Step 2: Prepare Frontend Repository**

### **Create Frontend Repository**
```bash
# Navigate to frontend directory
cd ../frontend

# Initialize git repository
git init

# Add all frontend files
git add .

# Create initial commit
git commit -m "Initial frontend setup with gaming interface and e-commerce UI"

# Add your GitHub repository as remote
git remote add origin https://github.com/yourusername/pickmetrend-frontend.git

# Push to GitHub
git branch -M main
git push -u origin main
```

### **Frontend Files to Include**
- ✅ All React components and pages
- ✅ `package.json` with dependencies
- ✅ Gaming components (Tic Tac Toe)
- ✅ E-commerce interface
- ✅ Wallet integration
- ✅ Tailwind CSS configuration
- ✅ README.md with deployment instructions

## 🚀 **Step 3: Deploy Backend to Render**

### **Backend Deployment**
1. **Login to Render**: https://render.com
2. **Create Blueprint**:
   - Click "New" → "Blueprint"
   - Connect your `pickmetrendofficial-render` repository
   - Select repository root
   - Render detects `render.yaml`
3. **Review Configuration**:
   - **Database**: PostgreSQL (auto-created)
   - **Redis**: Upstash Redis URL configured
   - **Environment Variables**: All set in render.yaml
4. **Deploy**: Click "Apply"

### **Backend Environment Variables**
```yaml
# Automatically configured in render.yaml
DATABASE_URL: [auto-generated]
REDIS_URL: redis://default:<EMAIL>:6379
SECRET_KEY: [auto-generated]
SENDGRID_API_KEY: [your key]
CLOUDINARY_*: [your credentials]
RAZORPAY_KEY_ID: rzp_live_1IrGH0WEYdtFdh
```

## 🎨 **Step 4: Deploy Frontend to Render**

### **Frontend Deployment**
1. **Create Static Site**:
   - Click "New" → "Static Site"
   - Connect your `pickmetrend-frontend` repository
   - Set **Root Directory**: Leave empty (repository root)
2. **Build Configuration**:
   ```
   Build Command: npm install && npm run build
   Publish Directory: build
   ```
3. **Environment Variables**:
   ```
   REACT_APP_API_URL=https://your-backend.onrender.com
   REACT_APP_RAZORPAY_KEY_ID=rzp_live_1IrGH0WEYdtFdh
   ```

## 🔗 **Step 5: Connect Frontend to Backend**

### **Update API URL**
After backend deployment, update frontend environment:
```bash
# In your frontend repository
# Update .env.production or Render environment variables
REACT_APP_API_URL=https://pickmetrend-backend.onrender.com
```

### **Update CORS Settings**
In backend `settings.py`, add your frontend domain:
```python
CORS_ALLOWED_ORIGINS = [
    "https://pickmetrend-frontend.onrender.com",
    "https://your-custom-domain.com",
]
```

## 📋 **Deployment Checklist**

### **Backend Repository**
- [ ] All Django files committed
- [ ] `requirements.txt` updated
- [ ] `render.yaml` configured
- [ ] Environment variables set
- [ ] Gaming system included
- [ ] Wallet system included
- [ ] Production settings configured

### **Frontend Repository**
- [ ] All React files committed
- [ ] `package.json` updated
- [ ] Gaming components included
- [ ] API integration configured
- [ ] Environment variables set
- [ ] Build configuration ready

### **Deployment Verification**
- [ ] Backend API accessible
- [ ] Frontend loads correctly
- [ ] Gaming system works
- [ ] Token transactions work
- [ ] Payment integration works
- [ ] CORS configured properly

## 🎮 **Gaming System Verification**

### **Test Gaming Features**
1. **Backend API**:
   ```bash
   # Test Tic Tac Toe endpoints
   curl -X POST https://your-backend.onrender.com/api/gaming/tic-tac-toe/start/
   ```

2. **Frontend Gaming**:
   - Login to your frontend
   - Navigate to gaming section
   - Play Tic Tac Toe game
   - Verify token rewards
   - Check wallet balance

## 🛒 **E-commerce Verification**

### **Test Shopping Features**
1. **Product Catalog**: Browse products
2. **Shopping Cart**: Add items with variants
3. **Token Discounts**: Apply gaming tokens
4. **Checkout**: Complete payment flow
5. **Order Tracking**: Verify order creation

## 🔧 **Maintenance & Updates**

### **Backend Updates**
```bash
cd pickmetrend-backend
git add .
git commit -m "Update: description of changes"
git push origin main
# Render auto-deploys on push
```

### **Frontend Updates**
```bash
cd pickmetrend-frontend
git add .
git commit -m "Update: description of changes"
git push origin main
# Render auto-deploys on push
```

## 📞 **Support & Troubleshooting**

### **Common Issues**
1. **CORS Errors**: Update backend CORS settings
2. **API Connection**: Check frontend API URL
3. **Gaming Issues**: Verify Redis connection
4. **Payment Errors**: Check Razorpay keys

### **Monitoring**
- **Backend**: Check Render logs and admin panel
- **Frontend**: Check browser console and network tab
- **Gaming**: Monitor token transactions
- **Payments**: Verify Razorpay dashboard

## 🎉 **Success!**

Your PickMeTrend platform is now deployed with:
- ✅ **Separate Repositories**: Clean organization
- ✅ **Independent Deployment**: Scalable architecture
- ✅ **Gaming System**: Challenging Tic Tac Toe
- ✅ **Token Economy**: Real rewards system
- ✅ **E-commerce**: Complete shopping platform
- ✅ **Production Ready**: Secure and scalable

**Your gaming e-commerce empire is live!** 🎮🛒🚀
