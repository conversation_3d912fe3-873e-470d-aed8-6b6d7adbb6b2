from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from . import views

# Create a router for the API
router = DefaultRouter()
router.register(r'tickets', views.SupportTicketViewSet, basename='support-ticket')
router.register(r'feedback', views.CustomerFeedbackViewSet, basename='customer-feedback')

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    path('api/support-form/', views.SupportFormView.as_view(), name='support-form-api'),
    
    # Frontend views
    path('support/', views.support_form_view, name='support-form'),
    path('feedback/<uuid:order_id>/', views.feedback_form_view, name='feedback-form'),
]
