from django.db import models

class URLOrFileField(models.ImageField):
    """
    A simplified field that can handle both file uploads and URLs.
    URLs are stored in the image_url field, files are handled normally.
    No downloading to avoid admin crashes.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def pre_save(self, model_instance, add):
        """
        Handle URL conversion before saving - simplified to avoid admin crashes
        """
        file = getattr(model_instance, self.attname)

        # If file is None, just return None
        if file is None:
            return None

        # If it's a string (URL), just store it in image_url and return None for the file field
        if isinstance(file, str) and file.startswith(('http://', 'https://')):
            # Store the URL in the image_url field if it exists
            if hasattr(model_instance, 'image_url'):
                setattr(model_instance, 'image_url', file)
            # Return None to avoid trying to save a URL as a file
            return None

        # For regular files, just return them
        return super().pre_save(model_instance, add)

    def value_from_object(self, obj):
        """
        Override to safely handle missing files
        """
        try:
            value = super().value_from_object(obj)

            # If we have a file value, check if it actually exists
            if value and hasattr(value, 'path'):
                import os
                if not os.path.exists(value.path):
                    # File doesn't exist, return None to avoid errors
                    return None

            return value
        except Exception:
            # If any error occurs, return None safely
            return None
