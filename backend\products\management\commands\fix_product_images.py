import re
import requests
from urllib.parse import urlparse
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from products.models import ProductImage

class Command(BaseCommand):
    help = 'Fix product images with URL paths'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting to fix product images...'))

        # Get all product images
        images = ProductImage.objects.all()
        fixed_count = 0

        for img in images:
            # Case 1: Image field is a string URL
            if isinstance(img.image, str) and img.image.startswith(('http://', 'https://')):
                self.stdout.write(f"Found URL in image field: {img.image}")

                # Store URL in image_url field
                img.image_url = img.image
                # Clear image field to avoid path errors
                img.image = None
                img.save()

                self.stdout.write(self.style.SUCCESS(f"Moved URL to image_url field"))
                fixed_count += 1
                continue

            # Case 2: Image name contains a URL
            if hasattr(img.image, 'name') and img.image.name and ('http://' in img.image.name or 'https://' in img.image.name):
                self.stdout.write(f"Found problematic image: {img.image.name}")

                # Extract the URL
                url_match = re.search(r'(https?://[^\s]+)', img.image.name)
                if url_match:
                    url = url_match.group(1)
                    self.stdout.write(f"Extracted URL: {url}")

                    # Store URL in image_url field
                    img.image_url = url

                    # Try to download the image
                    try:
                        response = requests.get(url, stream=True)
                        if response.status_code == 200:
                            # Get the filename from the URL
                            parsed_url = urlparse(url)
                            filename = parsed_url.path.split('/')[-1]
                            if not filename or '.' not in filename:
                                filename = f"image_{img.id}.jpg"

                            # Create a ContentFile from the response content
                            content_file = ContentFile(response.content, name=filename)

                            # Update the image field
                            img.image = content_file
                            img.save()

                            self.stdout.write(self.style.SUCCESS(f"Fixed image: {img.image.name}"))
                            fixed_count += 1
                        else:
                            # If download fails, just clear the image field
                            self.stdout.write(self.style.WARNING(f"Failed to download image from {url}: HTTP {response.status_code}"))
                            img.image = None
                            img.save()
                            fixed_count += 1
                    except Exception as e:
                        # If download fails, just clear the image field
                        self.stdout.write(self.style.WARNING(f"Error downloading image from {url}: {str(e)}"))
                        img.image = None
                        img.save()
                        fixed_count += 1

        self.stdout.write(self.style.SUCCESS(f"Fixed {fixed_count} images."))
