#!/usr/bin/env python
"""
Authentication System Fix
=========================

This script diagnoses and fixes authentication issues including:
- Missing user profiles
- Missing wallets
- Missing gaming stats
- User activation issues
- Signal problems
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from accounts.models import UserProfile
from wallet.models import Wallet, WalletTransaction
from gaming.models import PlayerStats
from django.db import transaction


def fix_user_profiles():
    """Ensure all users have profiles"""
    print("\n👤 Fixing User Profiles")
    print("=" * 40)
    
    users_without_profile = []
    
    for user in User.objects.all():
        if not hasattr(user, 'profile'):
            users_without_profile.append(user)
    
    print(f"Found {len(users_without_profile)} users without profiles")
    
    for user in users_without_profile:
        try:
            UserProfile.objects.create(user=user)
            print(f"✅ Created profile for {user.username}")
        except Exception as e:
            print(f"❌ Error creating profile for {user.username}: {e}")
    
    return len(users_without_profile)


def fix_user_wallets():
    """Ensure all users have wallets with signup bonus"""
    print("\n💰 Fixing User Wallets")
    print("=" * 40)
    
    users_without_wallet = []
    users_without_bonus = []
    
    for user in User.objects.all():
        # Check wallet
        try:
            wallet = user.wallet
            # Check signup bonus
            has_bonus = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            ).exists()
            
            if not has_bonus:
                users_without_bonus.append(user)
                
        except Wallet.DoesNotExist:
            users_without_wallet.append(user)
    
    print(f"Found {len(users_without_wallet)} users without wallets")
    print(f"Found {len(users_without_bonus)} users without signup bonus")
    
    # Create wallets
    for user in users_without_wallet:
        try:
            Wallet.give_signup_bonus(user)
            print(f"✅ Created wallet and gave 100 tokens to {user.username}")
        except Exception as e:
            print(f"❌ Error creating wallet for {user.username}: {e}")
    
    # Give signup bonus
    for user in users_without_bonus:
        try:
            wallet = user.wallet
            wallet.add_tokens(
                amount=100,
                transaction_type='signup_bonus',
                description='Welcome bonus for existing user'
            )
            print(f"✅ Gave 100 token signup bonus to {user.username}")
        except Exception as e:
            print(f"❌ Error giving bonus to {user.username}: {e}")
    
    return len(users_without_wallet) + len(users_without_bonus)


def fix_gaming_stats():
    """Ensure all users have gaming stats"""
    print("\n🎮 Fixing Gaming Stats")
    print("=" * 40)
    
    users_without_stats = []
    
    for user in User.objects.all():
        if not hasattr(user, 'gaming_stats'):
            users_without_stats.append(user)
    
    print(f"Found {len(users_without_stats)} users without gaming stats")
    
    for user in users_without_stats:
        try:
            PlayerStats.objects.create(user=user)
            print(f"✅ Created gaming stats for {user.username}")
        except Exception as e:
            print(f"❌ Error creating gaming stats for {user.username}: {e}")
    
    return len(users_without_stats)


def activate_all_users():
    """Ensure all users are active"""
    print("\n🔓 Activating All Users")
    print("=" * 40)
    
    inactive_users = User.objects.filter(is_active=False)
    print(f"Found {inactive_users.count()} inactive users")
    
    for user in inactive_users:
        user.is_active = True
        user.save()
        print(f"✅ Activated user: {user.username}")
    
    return inactive_users.count()


def test_authentication():
    """Test authentication system"""
    print("\n🔐 Testing Authentication System")
    print("=" * 40)
    
    # Test user creation
    test_username = f"test_auth_user"
    
    # Delete test user if exists
    User.objects.filter(username=test_username).delete()
    
    try:
        # Create test user
        user = User.objects.create_user(
            username=test_username,
            email=f'{test_username}@test.com',
            password='testpass123'
        )
        user.is_active = True
        user.save()
        
        print(f"✅ Created test user: {user.username}")
        
        # Check profile
        if hasattr(user, 'profile'):
            print(f"✅ Profile exists: {user.profile}")
        else:
            print(f"❌ No profile found")
        
        # Check wallet
        if hasattr(user, 'wallet'):
            print(f"✅ Wallet exists: {user.wallet.balance} tokens")
        else:
            print(f"❌ No wallet found")
        
        # Check gaming stats
        if hasattr(user, 'gaming_stats'):
            print(f"✅ Gaming stats exist: {user.gaming_stats}")
        else:
            print(f"❌ No gaming stats found")
        
        # Test password check
        if user.check_password('testpass123'):
            print(f"✅ Password check works")
        else:
            print(f"❌ Password check failed")
        
        # Clean up
        user.delete()
        print(f"✅ Test user cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False


def main():
    """Main function to fix all authentication issues"""
    print("🔧 Authentication System Fix")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # Fix all issues
            profiles_fixed = fix_user_profiles()
            wallets_fixed = fix_user_wallets()
            stats_fixed = fix_gaming_stats()
            users_activated = activate_all_users()
            
            # Test system
            auth_test_passed = test_authentication()
            
            print("\n📊 Summary")
            print("=" * 40)
            print(f"User profiles fixed: {profiles_fixed}")
            print(f"Wallets/bonuses fixed: {wallets_fixed}")
            print(f"Gaming stats fixed: {stats_fixed}")
            print(f"Users activated: {users_activated}")
            print(f"Authentication test: {'✅ PASSED' if auth_test_passed else '❌ FAILED'}")
            
            if profiles_fixed + wallets_fixed + stats_fixed + users_activated == 0:
                print("\n🎉 All users are properly configured!")
            else:
                print(f"\n✅ Fixed {profiles_fixed + wallets_fixed + stats_fixed + users_activated} issues")
            
            print("\n📋 Next Steps:")
            print("1. Test user registration on frontend")
            print("2. Test user login on frontend")
            print("3. Check that new users get 100 tokens")
            print("4. Verify gaming system works")
            
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
