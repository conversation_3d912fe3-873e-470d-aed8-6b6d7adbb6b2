#!/usr/bin/env python
"""
Simple test script to verify Spin Wheel functionality locally
This bypasses database issues and tests the core logic
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings_local')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {e}")
    sys.exit(1)

def test_spin_wheel_imports():
    """Test if Spin Wheel components can be imported"""
    print("\n🧪 Testing Spin Wheel imports...")
    
    try:
        from gaming.models import SpinWheelReward, SpinWheelSettings, SpinWheelHistory
        print("✅ Models imported successfully")
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        return False
    
    try:
        from gaming.spin_wheel_api import spin_wheel_status, spin_wheel
        print("✅ API functions imported successfully")
    except Exception as e:
        print(f"❌ API import failed: {e}")
        return False
    
    try:
        from gaming.urls import urlpatterns
        spin_wheel_urls = [url for url in urlpatterns if 'spin-wheel' in str(url.pattern)]
        print(f"✅ Found {len(spin_wheel_urls)} Spin Wheel URLs")
    except Exception as e:
        print(f"❌ URL import failed: {e}")
        return False
    
    return True

def test_spin_wheel_logic():
    """Test Spin Wheel logic without database"""
    print("\n🎡 Testing Spin Wheel logic...")
    
    try:
        from gaming.spin_wheel_api import _select_random_reward, _calculate_wheel_position
        print("✅ Helper functions imported")
        
        # Test wheel position calculation
        class MockReward:
            def __init__(self):
                self.id = "test-reward-123"
        
        mock_reward = MockReward()
        position = _calculate_wheel_position(mock_reward, 8)
        print(f"✅ Wheel position calculation: {position}°")
        
        return True
    except Exception as e:
        print(f"❌ Logic test failed: {e}")
        return False

def test_api_structure():
    """Test API endpoint structure"""
    print("\n🔗 Testing API structure...")
    
    try:
        from django.test import RequestFactory
        from django.contrib.auth.models import User
        from rest_framework.test import force_authenticate
        
        factory = RequestFactory()
        request = factory.get('/api/gaming/spin-wheel/status/')
        
        # Create mock user
        user = User(username='testuser', email='<EMAIL>')
        force_authenticate(request, user=user)
        
        print("✅ Request factory and authentication setup successful")
        return True
    except Exception as e:
        print(f"❌ API structure test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎡 SPIN WHEEL LOCAL TEST")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_spin_wheel_imports),
        ("Logic Test", test_spin_wheel_logic),
        ("API Structure Test", test_api_structure),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("The Spin Wheel code is working correctly.")
        print("The issue is likely database configuration in production.")
        print("\n💡 Next steps:")
        print("1. Run the production database setup commands")
        print("2. Test the Spin Wheel in your frontend")
    else:
        print("\n⚠️ SOME TESTS FAILED!")
        print("There may be code issues that need to be fixed.")
    
    return passed == len(results)

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
