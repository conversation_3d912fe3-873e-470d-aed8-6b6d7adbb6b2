@echo off
echo ========================================
echo PUSHING ALL GAME CHANGES TO GITHUB
echo ========================================

echo.
echo 🔧 BACKEND REPOSITORY
echo ========================================
cd /d "C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion"
echo Current directory: %cd%

echo Adding all backend files...
git add .

echo Committing backend changes...
git commit -m "🎮 Complete Color Match and Memory Card games - Backend APIs, game logic, and database setup"

echo Pushing backend to GitHub...
git push origin master

echo.
echo 🎨 FRONTEND REPOSITORY  
echo ========================================
cd /d "C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion\frontend"
echo Current directory: %cd%

echo Adding all frontend files...
git add .

echo Committing frontend changes...
git commit -m "🎮 Complete Color Match and Memory Card games - React components, services, and routing"

echo Pushing frontend to GitHub...
git push origin master

echo.
echo ========================================
echo ✅ ALL CHANGES PUSHED TO GITHUB!
echo ========================================
echo.
echo 📦 What was pushed:
echo.
echo 🔧 Backend:
echo    • color_match_api.py - Color Match API endpoints
echo    • memory_card_api.py - Memory Card API endpoints  
echo    • process_game_transaction() - Token transaction function
echo    • Updated setup_games.py - All 5 games database setup
echo    • Updated GameLobby.tsx - Fixed routing
echo.
echo 🎨 Frontend:
echo    • ColorMatchGame.tsx - Complete Color Match component
echo    • MemoryCardGame.tsx - Complete Memory Card component
echo    • colorMatchService.ts - Color Match API service
echo    • memoryCardService.ts - Memory Card API service
echo    • Updated App.tsx - Game routes
echo.
echo 🎮 Game Features:
echo    • Color Match: Stroop effect, 80%% AI accuracy, 5 rounds
echo    • Memory Card: 4x4 grid, AI memory, turn-based matching
echo    • Token System: Win +5, Draw +2, Loss -1
echo    • Authentication: Django login required
echo    • Responsive: Works on all devices
echo.
echo 🌐 Next Steps:
echo    1. Wait for Render to redeploy (2-3 minutes)
echo    2. Run: python manage.py setup_games (on backend)
echo    3. Test games on your website
echo    4. Click '🤖 Play vs AI' buttons (not 'Find Opponent')
echo.
echo 🎮 The complete interactive games are ready!
echo.
pause
