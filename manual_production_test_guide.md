# 🧪 Manual Production Games Testing Guide

## 🎯 Quick Test Checklist

Test all 5 games in production by following these steps:

### 🔐 **Step 1: Login & Setup**
1. Go to: https://pickmetrend-frontend-render.onrender.com/
2. Login to your account
3. Check your token balance (should have some tokens for testing)
4. Navigate to Gaming Dashboard: `/gaming`

### 🎮 **Step 2: Test Each Game**

#### **✂️ Rock Paper Scissors**
- [ ] Click "🤖 Play vs AI" button
- [ ] Should redirect to battle arena
- [ ] Game should load without errors
- [ ] Can make moves (rock/paper/scissors)
- [ ] AI responds appropriately
- [ ] Tokens are awarded/deducted correctly

#### **🔢 Number Guessing**
- [ ] Click "🤖 Play vs AI" button  
- [ ] Should redirect to battle arena
- [ ] Game should load without errors
- [ ] Can enter number guesses
- [ ] AI responds with hints
- [ ] Tokens are awarded/deducted correctly

#### **⭕ Tic Tac Toe**
- [ ] Click "🤖 Play vs AI" button
- [ ] Should redirect to Tic Tac Toe game
- [ ] Game board loads correctly
- [ ] Can click on grid squares
- [ ] AI makes moves automatically
- [ ] Win/loss detection works
- [ ] Tokens are awarded/deducted correctly

#### **🎨 Color Match**
- [ ] Click "🤖 Play vs AI" button
- [ ] Should show success message (game started)
- [ ] No "Failed to create AI battle" error
- [ ] Check browser console for any errors

#### **🃏 Memory Card Match**
- [ ] Click "🤖 Play vs AI" button
- [ ] Should show success message (game started)
- [ ] No "Failed to create AI battle" error
- [ ] Check browser console for any errors

### 📊 **Step 3: Check Results**

#### **✅ All Working (Expected Result):**
- Rock Paper Scissors: Full game works
- Number Guessing: Full game works  
- Tic Tac Toe: Full game works
- Color Match: Shows "Game started!" message
- Memory Card: Shows "Game started!" message
- No error messages in console
- Token balance updates correctly

#### **❌ Issues to Look For:**
- "Failed to create AI battle" errors
- "Oops! Something went wrong" messages
- 404 or 500 errors in browser console
- Games not loading or freezing
- Token balance not updating

### 🔍 **Step 4: Browser Console Check**

1. Open browser Developer Tools (F12)
2. Go to Console tab
3. Try each game and watch for errors
4. Look for these specific errors:
   - `Failed to fetch`
   - `404 Not Found`
   - `500 Internal Server Error`
   - `ImportError` or `ModuleNotFoundError`

### 📱 **Step 5: Test on Different Devices**

- [ ] Desktop Chrome
- [ ] Desktop Firefox
- [ ] Mobile Chrome
- [ ] Mobile Safari

### 🎯 **Expected Status After Fixes:**

| Game | Status | Functionality |
|------|--------|---------------|
| Rock Paper Scissors | ✅ Full Game | Complete battle system |
| Number Guessing | ✅ Full Game | Complete battle system |
| Tic Tac Toe | ✅ Full Game | Complete game with AI |
| Color Match | ✅ API Ready | Shows success, needs UI |
| Memory Card | ✅ API Ready | Shows success, needs UI |

### 🚨 **Common Issues & Solutions:**

#### **"Failed to create AI battle"**
- **Cause:** API endpoint not working
- **Solution:** Check backend deployment and database

#### **"Game started! Implementation coming soon"**
- **Cause:** Normal for Color Match & Memory Card
- **Solution:** This is expected - APIs work, need game components

#### **Token balance not updating**
- **Cause:** Wallet integration issue
- **Solution:** Check wallet API and transaction processing

#### **Games freeze or don't load**
- **Cause:** Frontend JavaScript errors
- **Solution:** Check browser console for errors

### 📞 **Quick Test Commands (Browser Console)**

```javascript
// Test Color Match API
fetch('/api/gaming/color-match/start/', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({difficulty: 'medium'})
}).then(r => r.json()).then(console.log);

// Test Memory Card API  
fetch('/api/gaming/memory-card/start/', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({difficulty: 'medium'})
}).then(r => r.json()).then(console.log);

// Check wallet balance
fetch('/api/wallet/balance/').then(r => r.json()).then(console.log);
```

### 🎉 **Success Criteria:**

**✅ Production is working if:**
- All 5 games can be clicked without errors
- Rock Paper Scissors, Number Guessing, Tic Tac Toe play fully
- Color Match & Memory Card show success messages
- Token balance updates correctly
- No console errors

**❌ Production needs fixing if:**
- Any game shows "Failed to create AI battle"
- Console shows 404/500 errors
- Games freeze or don't respond
- Token balance doesn't update

---

## 🚀 **Quick 2-Minute Test:**

1. **Login** → Gaming Dashboard
2. **Click each game** → Check for errors
3. **Play one full game** → Verify tokens update
4. **Check console** → Look for red errors

**If all games click without "Failed to create AI battle" errors, the fix worked!** 🎉
