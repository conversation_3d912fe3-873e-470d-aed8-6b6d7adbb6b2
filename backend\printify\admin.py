from django.contrib import admin
from .models import PrintifyConfig, PrintifyProduct, PrintifyOrder

@admin.register(PrintifyConfig)
class PrintifyConfigAdmin(admin.ModelAdmin):
    list_display = ('id', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active',)
    readonly_fields = ('created_at', 'updated_at')

@admin.register(PrintifyProduct)
class PrintifyProductAdmin(admin.ModelAdmin):
    list_display = ('id', 'printify_id', 'title', 'blueprint_id', 'print_provider_id', 'created_at')
    search_fields = ('title', 'printify_id', 'blueprint_id')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(PrintifyOrder)
class PrintifyOrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'printify_id', 'user', 'status', 'total_price', 'created_at')
    list_filter = ('status',)
    search_fields = ('printify_id', 'user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')
