#!/usr/bin/env python
"""
Check and Fix Games Database
============================

This script checks if all games are properly stored in the database
and fixes any missing game types.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType, Battle
from django.contrib.auth.models import User
from wallet.models import Wallet
from django.db import transaction
import json


def check_database_games():
    """Check what games exist in the database"""
    print("🔍 Checking Games in Database")
    print("=" * 40)
    
    all_games = GameType.objects.all()
    active_games = GameType.objects.filter(is_active=True)
    
    print(f"Total games in database: {all_games.count()}")
    print(f"Active games: {active_games.count()}")
    
    if all_games.exists():
        print("\n📋 Games in Database:")
        for game in all_games:
            status = "✅ Active" if game.is_active else "❌ Inactive"
            print(f"   {status} {game.display_name} ({game.name}) - ID: {game.id}")
            print(f"      Created: {game.created_at}")
            print(f"      Rules: {json.dumps(game.rules, indent=6)}")
    else:
        print("❌ No games found in database!")
    
    return all_games.count(), active_games.count()


def check_required_games():
    """Check if all required games exist"""
    print("\n🎯 Checking Required Games")
    print("=" * 30)
    
    required_games = [
        'rock_paper_scissors',
        'number_guessing', 
        'tic_tac_toe',
        'color_match',
        'memory_card'
    ]
    
    missing_games = []
    existing_games = []
    
    for game_name in required_games:
        try:
            game = GameType.objects.get(name=game_name)
            if game.is_active:
                existing_games.append(game_name)
                print(f"✅ {game_name}: Found and active")
            else:
                print(f"⚠️ {game_name}: Found but inactive")
                missing_games.append(game_name)
        except GameType.DoesNotExist:
            missing_games.append(game_name)
            print(f"❌ {game_name}: Missing from database")
    
    print(f"\nSummary:")
    print(f"   Existing: {len(existing_games)} games")
    print(f"   Missing: {len(missing_games)} games")
    
    if missing_games:
        print(f"   Missing games: {missing_games}")
    
    return missing_games, existing_games


def create_missing_games():
    """Create all missing games"""
    print("\n🛠️ Creating Missing Games")
    print("=" * 30)
    
    # Complete game definitions
    all_games_data = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic battle game - Play vs AI or human opponents!',
            'rules': {
                'choices': ['rock', 'paper', 'scissors'],
                'win_conditions': {
                    'rock': 'scissors',
                    'paper': 'rock', 
                    'scissors': 'paper'
                },
                'max_rounds': 3,
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number - Play vs AI or human opponents!',
            'rules': {
                'min_number': 1,
                'max_number': 100,
                'max_attempts': 5,
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        },
        {
            'name': 'tic_tac_toe',
            'display_name': 'Tic Tac Toe',
            'description': 'Classic strategy game against AI. Get 3 in a row to win!',
            'rules': {
                'board_size': 3,
                'win_condition': '3_in_a_row',
                'ai_difficulty': 'medium',
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        },
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember color sequences - Play vs AI or human opponents!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True,
                'difficulty_levels': ['easy', 'medium', 'hard'],
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs - Play vs AI or human opponents!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
                'difficulty_levels': ['easy', 'medium', 'hard'],
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        },
        {
            'name': 'ludo',
            'display_name': 'Ludo',
            'description': '5-minute timer-based Ludo! Make as many valid moves as possible to win.',
            'rules': {
                'game_duration': 300,
                'scoring_method': 'move_count',
                'ai_difficulty': 'medium',
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1,
                'configurable_tokens': [2, 4],
                'game_modes': ['vs_bot', 'vs_user']
            }
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    with transaction.atomic():
        for game_data in all_games_data:
            try:
                game_type, created = GameType.objects.get_or_create(
                    name=game_data['name'],
                    defaults={
                        'display_name': game_data['display_name'],
                        'description': game_data['description'],
                        'rules': game_data['rules'],
                        'is_active': True
                    }
                )
                
                if created:
                    print(f"✅ Created: {game_type.display_name}")
                    created_count += 1
                else:
                    # Update existing game to ensure it's properly configured
                    game_type.display_name = game_data['display_name']
                    game_type.description = game_data['description']
                    game_type.rules = game_data['rules']
                    game_type.is_active = True
                    game_type.save()
                    print(f"🔄 Updated: {game_type.display_name}")
                    updated_count += 1
                    
            except Exception as e:
                print(f"❌ Error with {game_data['name']}: {e}")
    
    print(f"\nResults:")
    print(f"   Created: {created_count} games")
    print(f"   Updated: {updated_count} games")
    
    return created_count, updated_count


def test_game_engine_support():
    """Test if GameEngine supports all games"""
    print("\n🔧 Testing GameEngine Support")
    print("=" * 35)
    
    try:
        from gaming.game_logic import GameEngine
        
        supported_games = list(GameEngine.GAME_CLASSES.keys())
        print(f"GameEngine supports: {supported_games}")
        
        database_games = list(GameType.objects.filter(is_active=True).values_list('name', flat=True))
        print(f"Database has: {database_games}")
        
        # Check each database game
        for game_name in database_games:
            if game_name in supported_games:
                print(f"✅ {game_name}: Supported in GameEngine")
                
                # Test initial state creation
                try:
                    initial_state = GameEngine.create_initial_state(game_name)
                    print(f"   ✅ Initial state created: {list(initial_state.keys())}")
                except Exception as e:
                    print(f"   ❌ Initial state error: {e}")
            else:
                print(f"❌ {game_name}: NOT supported in GameEngine")
        
        return len([g for g in database_games if g in supported_games]) == len(database_games)
        
    except Exception as e:
        print(f"❌ GameEngine error: {e}")
        return False


def test_ai_battle_creation():
    """Test AI battle creation for all games"""
    print("\n🤖 Testing AI Battle Creation")
    print("=" * 35)
    
    # Get a test user
    test_user = User.objects.first()
    if not test_user:
        print("❌ No users found for testing")
        return False
    
    print(f"Testing with user: {test_user.username}")
    
    # Ensure user has tokens
    try:
        wallet = test_user.wallet
        if wallet.balance < 10:
            wallet.add_tokens(50, 'test', 'Test tokens for battle creation')
            print(f"Added tokens, balance: {wallet.balance}")
    except Wallet.DoesNotExist:
        wallet = Wallet.objects.create(user=test_user)
        wallet.add_tokens(50, 'test', 'Test tokens for battle creation')
        print(f"Created wallet with balance: {wallet.balance}")
    
    # Test each active game
    success_count = 0
    total_games = 0
    
    for game_type in GameType.objects.filter(is_active=True):
        total_games += 1
        print(f"\n🎮 Testing {game_type.display_name}:")
        
        try:
            # Test battle creation
            battle = Battle.objects.create(
                game_type=game_type,
                player1=test_user,
                is_ai_battle=True,
                status='waiting'
            )
            
            print(f"   ✅ Battle created: {battle.id}")
            
            # Test game state initialization
            from gaming.game_logic import GameEngine
            initial_state = GameEngine.create_initial_state(game_type.name)
            battle.game_state = initial_state
            battle.save()
            
            print(f"   ✅ Game state initialized")
            success_count += 1
            
            # Clean up
            battle.delete()
            print(f"   🗑️ Cleaned up")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\nBattle Creation Results:")
    print(f"   Successful: {success_count}/{total_games}")
    
    return success_count == total_games


def test_api_endpoints():
    """Test the API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    from django.test import Client
    import json
    
    client = Client()
    test_user = User.objects.first()
    
    if not test_user:
        print("❌ No users found")
        return False
    
    client.force_login(test_user)
    print(f"Testing with user: {test_user.username}")
    
    success_count = 0
    total_tests = 0
    
    for game_type in GameType.objects.filter(is_active=True):
        total_tests += 1
        print(f"\n🎮 Testing API for {game_type.display_name}:")
        
        try:
            response = client.post('/api/gaming/create-ai-battle/', 
                                 data=json.dumps({'game_type': game_type.name}),
                                 content_type='application/json')
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                response_data = json.loads(response.content.decode())
                print(f"   ✅ SUCCESS! Battle ID: {response_data.get('battle_id')}")
                success_count += 1
                
                # Clean up
                if 'battle_id' in response_data:
                    try:
                        battle = Battle.objects.get(id=response_data['battle_id'])
                        battle.delete()
                        print(f"   🗑️ Cleaned up")
                    except:
                        pass
            else:
                error_content = response.content.decode()
                print(f"   ❌ FAILED: {error_content}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print(f"\nAPI Test Results:")
    print(f"   Successful: {success_count}/{total_tests}")
    
    return success_count == total_tests


def main():
    """Main diagnostic and fix function"""
    print("🔧 GAMES DATABASE DIAGNOSTIC & FIX")
    print("=" * 50)
    
    # Step 1: Check current database state
    total_games, active_games = check_database_games()
    
    # Step 2: Check required games
    missing_games, existing_games = check_required_games()
    
    # Step 3: Create missing games if needed
    if missing_games:
        print(f"\n⚠️ Found {len(missing_games)} missing games. Creating them...")
        created, updated = create_missing_games()
    else:
        print(f"\n✅ All required games exist in database")
        created, updated = 0, 0
    
    # Step 4: Test GameEngine support
    engine_ok = test_game_engine_support()
    
    # Step 5: Test AI battle creation
    battle_ok = test_ai_battle_creation()
    
    # Step 6: Test API endpoints
    api_ok = test_api_endpoints()
    
    # Final summary
    print("\n🎉 DIAGNOSTIC COMPLETE")
    print("=" * 30)
    
    final_total = GameType.objects.all().count()
    final_active = GameType.objects.filter(is_active=True).count()
    
    print(f"📊 Final Database State:")
    print(f"   Total games: {final_total}")
    print(f"   Active games: {final_active}")
    print(f"   Created: {created}")
    print(f"   Updated: {updated}")
    
    print(f"\n🧪 Test Results:")
    print(f"   GameEngine: {'✅ OK' if engine_ok else '❌ FAILED'}")
    print(f"   Battle Creation: {'✅ OK' if battle_ok else '❌ FAILED'}")
    print(f"   API Endpoints: {'✅ OK' if api_ok else '❌ FAILED'}")
    
    if engine_ok and battle_ok and api_ok:
        print(f"\n🎉 ALL SYSTEMS WORKING!")
        print(f"✅ All 5 games should work now:")
        for game in GameType.objects.filter(is_active=True).order_by('name'):
            print(f"   • {game.display_name}")
        print(f"\n💡 Try playing the games on your frontend!")
    else:
        print(f"\n⚠️ Some issues remain. Check the test results above.")


if __name__ == '__main__':
    main()
