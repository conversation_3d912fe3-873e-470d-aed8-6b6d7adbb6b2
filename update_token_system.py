#!/usr/bin/env python
"""
Update Token System
===================

This script updates the entire token system to the new standardized rules:
1. Updates signup bonus from 100 to 200 tokens for all users
2. Ensures all games follow the same token rules
3. Verifies the new token economy is working
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.db import models
from wallet.models import Wallet, WalletTransaction
from gaming.models import GameType


def update_signup_bonuses():
    """Give existing users additional 100 tokens to reach 200 total signup bonus"""
    print("🎁 Updating Signup Bonuses to 200 Tokens")
    print("=" * 50)
    
    users_updated = 0
    users_already_updated = 0
    users_new_bonus = 0
    
    for user in User.objects.all():
        try:
            wallet = user.wallet
            
            # Check existing signup bonus transactions
            signup_transactions = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            )
            
            total_signup_bonus = sum(tx.amount for tx in signup_transactions)
            
            if total_signup_bonus >= 200:
                users_already_updated += 1
                print(f"✅ {user.username}: Already has {total_signup_bonus} signup bonus")
            elif total_signup_bonus == 100:
                # Give additional 100 tokens
                wallet.add_tokens(
                    amount=100,
                    transaction_type='signup_bonus',
                    description="Additional signup bonus (100→200 tokens upgrade)"
                )
                users_updated += 1
                print(f"🎁 {user.username}: Added 100 tokens (100→200 upgrade)")
            elif total_signup_bonus == 0:
                # Give full 200 tokens
                wallet.add_tokens(
                    amount=200,
                    transaction_type='signup_bonus',
                    description="New 200 token signup bonus"
                )
                users_new_bonus += 1
                print(f"🆕 {user.username}: Added 200 tokens (new signup bonus)")
            else:
                # Unusual case - top up to 200
                additional = 200 - total_signup_bonus
                wallet.add_tokens(
                    amount=additional,
                    transaction_type='signup_bonus',
                    description=f"Signup bonus top-up to 200 tokens (+{additional})"
                )
                users_updated += 1
                print(f"🔧 {user.username}: Added {additional} tokens (top-up to 200)")
                
        except Wallet.DoesNotExist:
            # Create wallet with new 200 token bonus
            wallet = Wallet.objects.create(user=user)
            wallet.add_tokens(
                amount=200,
                transaction_type='signup_bonus',
                description="New 200 token signup bonus"
            )
            users_new_bonus += 1
            print(f"🆕 {user.username}: Created wallet with 200 tokens")
    
    print(f"\n📊 Signup Bonus Update Summary:")
    print(f"   Users already at 200+: {users_already_updated}")
    print(f"   Users upgraded (100→200): {users_updated}")
    print(f"   Users with new 200 bonus: {users_new_bonus}")
    print(f"   Total users processed: {User.objects.count()}")


def verify_new_token_rules():
    """Verify the new standardized token rules are in place"""
    print("\n🔍 Verifying New Token Rules")
    print("=" * 40)
    
    from django.conf import settings
    
    gaming_settings = getattr(settings, 'GAMING_SETTINGS', {})
    
    expected_rules = {
        'TOKEN_REWARD_WIN': 5,
        'TOKEN_REWARD_DRAW': 2,
        'TOKEN_REWARD_PARTICIPATION': 2,
        'TOKEN_PENALTY_LOSS': 1,
        'SIGNUP_BONUS': 200
    }
    
    print("Expected vs Actual Token Rules:")
    for rule, expected_value in expected_rules.items():
        actual_value = gaming_settings.get(rule, 'NOT SET')
        status = "✅" if actual_value == expected_value else "❌"
        print(f"   {status} {rule}: {actual_value} (expected: {expected_value})")
    
    print(f"\n🎮 Game Types in Database:")
    game_types = GameType.objects.all()
    for game in game_types:
        print(f"   • {game.display_name} ({game.name}) - Active: {game.is_active}")


def show_user_balances():
    """Show current user balances after update"""
    print("\n💰 Current User Balances")
    print("=" * 30)
    
    users = User.objects.all()[:10]  # Show first 10 users
    
    for user in users:
        try:
            wallet = user.wallet
            signup_bonus = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            ).aggregate(total=models.Sum('amount'))['total'] or 0
            
            print(f"👤 {user.username}:")
            print(f"   Balance: {wallet.balance} tokens")
            print(f"   Signup bonus: {signup_bonus} tokens")
            print(f"   Total earned: {wallet.total_earned} tokens")
            print(f"   Total spent: {wallet.total_spent} tokens")
            
        except Wallet.DoesNotExist:
            print(f"👤 {user.username}: No wallet")
    
    if users.count() > 10:
        print(f"   ... and {users.count() - 10} more users")


def test_new_user_creation():
    """Test creating a new user with the new 200 token bonus"""
    print("\n🧪 Testing New User Creation")
    print("=" * 35)
    
    # Create test user
    test_username = f"test_200_bonus_{int(os.urandom(4).hex(), 16)}"
    user = User.objects.create_user(
        username=test_username,
        email=f"{test_username}@test.com",
        password="testpass123"
    )
    
    print(f"Created test user: {user.username}")
    
    # Check wallet and bonus
    try:
        wallet = user.wallet
        signup_bonus = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='signup_bonus'
        ).first()
        
        if signup_bonus and signup_bonus.amount == 200:
            print(f"✅ New user gets 200 token signup bonus")
            print(f"   Wallet balance: {wallet.balance}")
        else:
            print(f"❌ New user signup bonus issue")
            print(f"   Bonus amount: {signup_bonus.amount if signup_bonus else 'None'}")
        
        # Clean up test user
        user.delete()
        print(f"🗑️ Cleaned up test user")
        
    except Exception as e:
        print(f"❌ Error testing new user: {e}")


def main():
    """Main update function"""
    print("🔧 Token System Update")
    print("=" * 50)
    print("Updating to new standardized token rules:")
    print("• Participate: +2 tokens")
    print("• Win: +5 tokens")
    print("• Draw: +2 tokens")
    print("• Loss: -1 token")
    print("• Signup Bonus: 200 tokens (increased from 100)")
    print("=" * 50)
    
    # Update signup bonuses
    update_signup_bonuses()
    
    # Verify new rules
    verify_new_token_rules()
    
    # Show user balances
    show_user_balances()
    
    # Test new user creation
    test_new_user_creation()
    
    print("\n🎉 Token System Update Complete!")
    print("=" * 40)
    print("✅ All users now have 200 token signup bonus")
    print("✅ New standardized token rules in place")
    print("✅ All 5 games follow same reward structure")
    print("\n💡 Next steps:")
    print("1. Deploy backend changes")
    print("2. Deploy frontend changes")
    print("3. Test gaming with new token rules")


if __name__ == '__main__':
    main()
