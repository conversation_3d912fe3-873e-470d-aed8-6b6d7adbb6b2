#!/usr/bin/env python3
"""
Simple API test to isolate the 500 error
"""
import os
import django
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

def test_simple_api():
    print("🔍 Testing API with Django Test Client...")
    
    try:
        # Create a test client
        client = Client()
        
        # Test without authentication first
        print("1. Testing without authentication...")
        response = client.get('/api/orders/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 401:
            print("   ✅ Correctly requires authentication")
        elif response.status_code == 500:
            print("   ❌ 500 error even without authentication")
            print(f"   Response: {response.content.decode()[:200]}...")
            return False
        
        # Create a user and test with authentication
        print("2. Testing with authentication...")
        User = get_user_model()
        
        # Get or create a test user
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'password': 'testpass123'
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            print(f"   ✅ Created test user: {user.username}")
        else:
            print(f"   ✅ Using existing user: {user.username}")
        
        # Login the user
        client.force_login(user)
        
        # Test the API with authentication
        response = client.get('/api/orders/')
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ API call successful!")
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            return True
        elif response.status_code == 500:
            print("   ❌ 500 error with authentication")
            print(f"   Response: {response.content.decode()[:200]}...")
            return False
        else:
            print(f"   ⚠️ Unexpected status: {response.status_code}")
            print(f"   Response: {response.content.decode()[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_orders_model():
    print("\n🔍 Testing Orders Model...")
    
    try:
        from orders.models import Order
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Check if we have any orders
        order_count = Order.objects.count()
        print(f"   📦 Total orders: {order_count}")
        
        if order_count > 0:
            # Get the first order
            order = Order.objects.first()
            print(f"   📦 First order: {order.id}")
            print(f"   👤 Order user: {order.user}")
            print(f"   💰 Order total: {order.total}")
            print(f"   📅 Created: {order.created_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing orders model: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Simple API Diagnostic Tool")
    print("=" * 50)
    
    # Test orders model
    model_success = test_orders_model()
    
    # Test API
    api_success = test_simple_api()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print(f"   Orders Model: {'✅ Working' if model_success else '❌ Failed'}")
    print(f"   API Test: {'✅ Working' if api_success else '❌ Failed'}")
    
    if api_success:
        print("\n🎉 SOLUTION:")
        print("   The API is working correctly with Django test client!")
        print("   The issue might be with the JWT authentication or CORS.")
        print("   Try logging out and logging back in to get a fresh token.")
    else:
        print("\n🔧 NEXT STEPS:")
        print("   1. Check Django server logs for specific errors")
        print("   2. Look for missing imports or configuration issues")
        print("   3. Check if all required packages are installed")
