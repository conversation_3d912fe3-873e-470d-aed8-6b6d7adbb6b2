#!/usr/bin/env python
"""
Script to fix Spin Wheel functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pickmetrend.settings')
django.setup()

from django.core.management import execute_from_command_line

def main():
    print("🎡 Fixing Spin Wheel Functionality...")
    print("=" * 50)
    
    try:
        # Step 1: Create migrations
        print("Step 1: Creating migrations...")
        execute_from_command_line(['manage.py', 'makemigrations', 'gaming'])
        print("✅ Migrations created successfully")
        
        # Step 2: Apply migrations
        print("\nStep 2: Applying migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        print("✅ Migrations applied successfully")
        
        # Step 3: Setup spin wheel
        print("\nStep 3: Setting up spin wheel...")
        execute_from_command_line(['manage.py', 'setup_spin_wheel'])
        print("✅ Spin wheel setup completed")
        
        # Step 4: Test models
        print("\nStep 4: Testing models...")
        from gaming.spin_wheel_models import SpinWheelReward, SpinWheelSettings
        
        rewards_count = SpinWheelReward.objects.count()
        settings_count = SpinWheelSettings.objects.count()
        
        print(f"✅ Found {rewards_count} rewards")
        print(f"✅ Found {settings_count} settings")
        
        if rewards_count == 0:
            print("⚠️ No rewards found - running setup again...")
            execute_from_command_line(['manage.py', 'setup_spin_wheel'])
        
        print("\n🎉 Spin Wheel fix completed successfully!")
        print("=" * 50)
        print("You can now test the Spin Wheel in your browser.")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Please check the error and try again.")

if __name__ == '__main__':
    main()
