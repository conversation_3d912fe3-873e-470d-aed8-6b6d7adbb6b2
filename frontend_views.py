import os
from django.http import HttpResponse, Http404
from django.conf import settings
from django.views.generic import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt

@method_decorator(csrf_exempt, name='dispatch')
class ReactAppView(View):
    """
    Serve the React app for all frontend routes
    """
    
    def get(self, request, *args, **kwargs):
        try:
            # Path to the React build index.html
            index_path = os.path.join(settings.REACT_BUILD_DIR, 'index.html')
            
            if os.path.exists(index_path):
                with open(index_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return HttpResponse(content, content_type='text/html')
            else:
                raise Http404("React app not found. Please run 'npm run build' in the frontend directory.")
                
        except Exception as e:
            return HttpResponse(
                f"Error serving React app: {str(e)}", 
                status=500,
                content_type='text/plain'
            )

@method_decorator(csrf_exempt, name='dispatch')
class ReactStaticView(View):
    """
    Serve React static files (JS, CSS, etc.)
    """
    
    def get(self, request, path):
        try:
            # Path to the requested static file
            file_path = os.path.join(settings.REACT_BUILD_DIR, path)
            
            if os.path.exists(file_path) and os.path.isfile(file_path):
                # Determine content type based on file extension
                content_type = 'text/plain'
                if path.endswith('.js'):
                    content_type = 'application/javascript'
                elif path.endswith('.css'):
                    content_type = 'text/css'
                elif path.endswith('.html'):
                    content_type = 'text/html'
                elif path.endswith('.json'):
                    content_type = 'application/json'
                elif path.endswith('.png'):
                    content_type = 'image/png'
                elif path.endswith('.jpg') or path.endswith('.jpeg'):
                    content_type = 'image/jpeg'
                elif path.endswith('.svg'):
                    content_type = 'image/svg+xml'
                elif path.endswith('.ico'):
                    content_type = 'image/x-icon'
                
                # Read and serve the file
                mode = 'rb' if content_type.startswith('image/') else 'r'
                encoding = None if content_type.startswith('image/') else 'utf-8'
                
                with open(file_path, mode, encoding=encoding) as f:
                    content = f.read()
                
                return HttpResponse(content, content_type=content_type)
            else:
                raise Http404(f"Static file not found: {path}")
                
        except Exception as e:
            return HttpResponse(
                f"Error serving static file: {str(e)}", 
                status=500,
                content_type='text/plain'
            )
