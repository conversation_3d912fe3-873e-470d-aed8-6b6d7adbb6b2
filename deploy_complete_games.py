#!/usr/bin/env python
"""
Deploy Complete Games
====================

Deploy the complete Color Match and Memory Card games to production
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ Success!")
            if result.stdout:
                print(f"Output: {result.stdout}")
        else:
            print(f"❌ Failed!")
            if result.stderr:
                print(f"Error: {result.stderr}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Deploy complete games"""
    print("🚀 DEPLOYING COMPLETE COLOR MATCH & MEMORY CARD GAMES")
    print("=" * 60)
    
    # Change to project directory
    os.chdir(r"C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion")
    print(f"Working directory: {os.getcwd()}")
    
    # Step 1: Add all files
    if not run_command("git add .", "Adding all files"):
        return False
    
    # Step 2: Commit changes
    commit_message = "🎮 Complete Color Match and Memory Card games with full React components and AI bots"
    if not run_command(f'git commit -m "{commit_message}"', "Committing changes"):
        print("⚠️ No changes to commit or commit failed")
    
    # Step 3: Push to backend repository
    if not run_command("git push origin master", "Pushing to backend repository"):
        return False
    
    print("\n" + "="*60)
    print("🎉 DEPLOYMENT COMPLETE!")
    print("=" * 60)
    
    print("\n📦 What was deployed:")
    print("✅ ColorMatchGame.tsx - Complete React component with Stroop effect")
    print("✅ MemoryCardGame.tsx - Complete React component with AI memory")
    print("✅ colorMatchService.ts - Frontend API service")
    print("✅ memoryCardService.ts - Frontend API service")
    print("✅ color_match_api.py - Backend API endpoints")
    print("✅ memory_card_api.py - Backend API endpoints")
    print("✅ process_game_transaction() - Token transaction function")
    print("✅ Updated GameLobby.tsx - Routes to new games")
    print("✅ Updated App.tsx - Game routes added")
    print("✅ Updated setup_games.py - All 5 games in database")
    
    print("\n🎮 Game Features:")
    print("✅ Color Match:")
    print("   • Stroop effect (color name vs text color)")
    print("   • Player vs AI turns")
    print("   • 80% AI accuracy")
    print("   • 5 rounds per game")
    print("   • Beautiful animations")
    
    print("✅ Memory Card Match:")
    print("   • 8 pairs of animal cards")
    print("   • Player vs AI turns")
    print("   • AI memory simulation")
    print("   • Turn-based matching")
    print("   • Card flip animations")
    
    print("\n💰 Token System:")
    print("✅ Win: +5 tokens")
    print("✅ Draw: +2 tokens")
    print("✅ Loss: -1 token")
    print("✅ Automatic wallet updates")
    print("✅ Transaction logging")
    
    print("\n🔐 Authentication:")
    print("✅ Django authentication required")
    print("✅ Game session storage")
    print("✅ User token validation")
    print("✅ Game history tracking")
    
    print("\n🎯 Next Steps:")
    print("1. Wait for Render to redeploy (2-3 minutes)")
    print("2. Run: python manage.py setup_games")
    print("3. Test games at: /gaming/color-match and /gaming/memory-card")
    print("4. All 5 games should work perfectly!")
    
    print("\n🌐 Game URLs:")
    print("• Color Match: https://your-frontend.com/gaming/color-match")
    print("• Memory Card: https://your-frontend.com/gaming/memory-card")
    print("• Game Lobby: https://your-frontend.com/gaming")
    
    print("\n💡 The complete interactive games are now ready!")
    print("Both games feature AI opponents, beautiful UI, and full token integration!")

if __name__ == '__main__':
    main()
