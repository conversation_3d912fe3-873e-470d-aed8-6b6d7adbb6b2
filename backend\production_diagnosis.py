#!/usr/bin/env python
"""
Production Diagnosis and Fix Script
===================================

This script will:
1. Check user registration issues
2. Verify authentication system
3. Check signup bonus implementation
4. Count users in production database
5. Add 100 tokens to all users
6. Test games functionality
7. Verify token flow and transactions

Run this on production to diagnose and fix all issues.
"""

import os
import django
import traceback
from datetime import datetime

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from rest_framework_simplejwt.tokens import RefreshToken
from wallet.models import Wallet, WalletTransaction
from accounts.models import UserProfile
from gaming.models import GameType, PlayerStats
from products.models import Product


def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")


def print_section(title):
    """Print a formatted section"""
    print(f"\n{'─'*40}")
    print(f"📋 {title}")
    print(f"{'─'*40}")


def check_database_users():
    """Check all users in production database"""
    print_header("PRODUCTION DATABASE ANALYSIS")
    
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    staff_users = User.objects.filter(is_staff=True).count()
    superusers = User.objects.filter(is_superuser=True).count()
    
    print(f"📊 User Statistics:")
    print(f"   Total users: {total_users}")
    print(f"   Active users: {active_users}")
    print(f"   Staff users: {staff_users}")
    print(f"   Superusers: {superusers}")
    
    # Check wallets
    users_with_wallets = User.objects.filter(wallet__isnull=False).count()
    users_without_wallets = total_users - users_with_wallets
    
    print(f"\n💰 Wallet Statistics:")
    print(f"   Users with wallets: {users_with_wallets}")
    print(f"   Users without wallets: {users_without_wallets}")
    
    # Check signup bonuses
    users_with_bonus = 0
    total_tokens = 0
    
    for user in User.objects.all():
        try:
            wallet = user.wallet
            has_bonus = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            ).exists()
            if has_bonus:
                users_with_bonus += 1
            total_tokens += wallet.balance
        except Wallet.DoesNotExist:
            pass
    
    print(f"\n🪙 Token Statistics:")
    print(f"   Users with signup bonus: {users_with_bonus}")
    print(f"   Users without signup bonus: {total_users - users_with_bonus}")
    print(f"   Total tokens in system: {total_tokens}")
    
    # List all users
    print(f"\n👥 All Users in Database:")
    for user in User.objects.all().order_by('date_joined'):
        try:
            wallet_balance = user.wallet.balance
            has_bonus = WalletTransaction.objects.filter(
                wallet=user.wallet,
                transaction_type='signup_bonus'
            ).exists()
            bonus_status = "✅" if has_bonus else "❌"
        except Wallet.DoesNotExist:
            wallet_balance = "No wallet"
            bonus_status = "❌"
        
        print(f"   {user.id:2d}. {user.username:15s} | {user.email:25s} | Tokens: {str(wallet_balance):>6s} | Bonus: {bonus_status} | Joined: {user.date_joined.strftime('%Y-%m-%d')}")
    
    return {
        'total_users': total_users,
        'users_with_wallets': users_with_wallets,
        'users_with_bonus': users_with_bonus,
        'total_tokens': total_tokens
    }


def test_registration_api():
    """Test user registration API"""
    print_header("REGISTRATION API TESTING")
    
    client = Client()
    
    # Test data
    test_username = f"test_user_{int(datetime.now().timestamp())}"
    registration_data = {
        'username': test_username,
        'email': f'{test_username}@test.com',
        'password': 'TestPass123!',
        're_password': 'TestPass123!',
        'first_name': 'Test',
        'last_name': 'User'
    }
    
    print(f"🧪 Testing registration with user: {test_username}")
    
    # Test custom registration endpoint
    print(f"\n1. Testing Custom Registration (/api/accounts/register/)")
    try:
        response = client.post('/api/accounts/register/', registration_data, content_type='application/json')
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"   ✅ Registration successful!")
            print(f"   User ID: {data['user']['id']}")
            print(f"   Username: {data['user']['username']}")
            print(f"   Access Token: {data['access'][:20]}...")
            
            # Check if wallet was created
            user = User.objects.get(username=test_username)
            try:
                wallet = user.wallet
                print(f"   ✅ Wallet created: {wallet.balance} tokens")
                
                # Check signup bonus
                bonus = WalletTransaction.objects.filter(
                    wallet=wallet,
                    transaction_type='signup_bonus'
                ).first()
                
                if bonus:
                    print(f"   ✅ Signup bonus: {bonus.amount} tokens")
                else:
                    print(f"   ❌ Signup bonus: Not found")
                    
            except Wallet.DoesNotExist:
                print(f"   ❌ Wallet: Not created")
            
            # Clean up test user
            user.delete()
            print(f"   🗑️ Test user cleaned up")
            
        else:
            print(f"   ❌ Registration failed")
            print(f"   Response: {response.content.decode()}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        traceback.print_exc()
    
    # Test Djoser registration endpoint
    print(f"\n2. Testing Djoser Registration (/api/auth/users/)")
    try:
        test_username2 = f"djoser_user_{int(datetime.now().timestamp())}"
        djoser_data = {
            'username': test_username2,
            'email': f'{test_username2}@test.com',
            'password': 'TestPass123!',
            're_password': 'TestPass123!'
        }
        
        response = client.post('/api/auth/users/', djoser_data, content_type='application/json')
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 201:
            print(f"   ✅ Djoser registration successful!")
            
            # Check wallet creation
            user = User.objects.get(username=test_username2)
            try:
                wallet = user.wallet
                print(f"   ✅ Wallet created: {wallet.balance} tokens")
            except Wallet.DoesNotExist:
                print(f"   ❌ Wallet: Not created")
            
            # Clean up
            user.delete()
            print(f"   🗑️ Test user cleaned up")
            
        else:
            print(f"   ❌ Djoser registration failed")
            print(f"   Response: {response.content.decode()}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")


def fix_all_user_wallets_and_bonuses():
    """Fix wallets and signup bonuses for all users"""
    print_header("FIXING ALL USER WALLETS AND BONUSES")
    
    users_fixed = 0
    wallets_created = 0
    bonuses_added = 0
    
    for user in User.objects.all():
        print(f"\n👤 Processing user: {user.username}")
        
        # Ensure user has wallet
        wallet, wallet_created = Wallet.objects.get_or_create(user=user)
        if wallet_created:
            wallets_created += 1
            print(f"   ✅ Created wallet")
        else:
            print(f"   ℹ️ Wallet already exists")
        
        # Check and add signup bonus
        has_bonus = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='signup_bonus'
        ).exists()
        
        if not has_bonus:
            wallet.add_tokens(
                amount=100,
                transaction_type='signup_bonus',
                description='Welcome bonus (production fix)'
            )
            bonuses_added += 1
            print(f"   ✅ Added 100 token signup bonus")
            users_fixed += 1
        else:
            print(f"   ℹ️ Signup bonus already exists")
        
        # Ensure user has profile
        _, profile_created = UserProfile.objects.get_or_create(user=user)
        if profile_created:
            print(f"   ✅ Created user profile")

        # Ensure user has gaming stats
        _, stats_created = PlayerStats.objects.get_or_create(user=user)
        if stats_created:
            print(f"   ✅ Created gaming stats")
        
        print(f"   💰 Final wallet balance: {wallet.balance} tokens")
    
    print(f"\n📊 Summary:")
    print(f"   Wallets created: {wallets_created}")
    print(f"   Signup bonuses added: {bonuses_added}")
    print(f"   Users fixed: {users_fixed}")


def test_games_functionality():
    """Test games functionality and token flow"""
    print_header("GAMES FUNCTIONALITY TESTING")
    
    # Check game types in database
    print_section("Game Types in Database")
    game_types = GameType.objects.all()
    print(f"Total game types: {game_types.count()}")
    
    for game in game_types:
        print(f"   {game.id}. {game.display_name} ({game.name}) - Active: {game.is_active}")
    
    # Create test user for gaming
    print_section("Creating Test User for Gaming")
    test_user = User.objects.create_user(
        username='game_test_user',
        email='<EMAIL>',
        password='testpass123'
    )
    
    # Check wallet creation
    try:
        wallet = test_user.wallet
        initial_balance = wallet.balance
        print(f"✅ Test user wallet: {initial_balance} tokens")
    except Wallet.DoesNotExist:
        print(f"❌ Test user has no wallet")
        test_user.delete()
        return
    
    # Test Tic Tac Toe API
    print_section("Testing Tic Tac Toe API")
    client = Client()
    
    # Get JWT token for test user
    refresh = RefreshToken.for_user(test_user)
    access_token = str(refresh.access_token)
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    # Test game start
    response = client.post('/api/gaming/tic-tac-toe/start/', **headers)
    print(f"Start game response: {response.status_code}")
    
    if response.status_code == 200:
        print(f"✅ Tic Tac Toe start working")
        
        # Test game completion (simulate win)
        completion_data = {
            'result': 'win',
            'moves': 5,
            'game_duration': 30
        }
        
        response = client.post('/api/gaming/tic-tac-toe/complete/', completion_data, **headers)
        print(f"Complete game response: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Game completion working")
            print(f"   Tokens earned: {data.get('tokens_earned', 0)}")
            
            # Check wallet balance
            wallet.refresh_from_db()
            new_balance = wallet.balance
            print(f"   Wallet balance: {initial_balance} → {new_balance}")
            
        else:
            print(f"❌ Game completion failed: {response.content.decode()}")
    else:
        print(f"❌ Tic Tac Toe start failed: {response.content.decode()}")
    
    # Test wallet API
    print_section("Testing Wallet API")
    response = client.get('/api/wallet/', **headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Wallet API working")
        print(f"   Balance: {data.get('balance', 0)} tokens")
    else:
        print(f"❌ Wallet API failed: {response.status_code}")
    
    # Clean up test user
    test_user.delete()
    print(f"🗑️ Test user cleaned up")


def check_token_discounts():
    """Check token discount system"""
    print_header("TOKEN DISCOUNT SYSTEM CHECK")
    
    # Check products with token discounts
    total_products = Product.objects.filter(is_active=True).count()
    token_products = Product.objects.filter(is_active=True, token_discount_available=True).count()
    
    print(f"📊 Product Statistics:")
    print(f"   Total active products: {total_products}")
    print(f"   Products with token discount: {token_products}")
    
    if token_products == 0:
        print(f"⚠️ No products have token discount enabled!")
        print(f"🔧 Enabling token discounts on all products...")
        
        updated = 0
        for product in Product.objects.filter(is_active=True):
            product.token_discount_available = True
            product.token_discount_percentage = 20
            product.token_discount_max_amount = 100.0
            product.save()
            updated += 1
        
        print(f"✅ Enabled token discounts on {updated} products")
    else:
        print(f"✅ Token discounts are enabled on products")


def main():
    """Run complete production diagnosis"""
    print("🚀 PICKMETREND PRODUCTION DIAGNOSIS")
    print("=" * 60)
    print("This script will diagnose and fix all production issues...")
    
    try:
        # 1. Check database users
        check_database_users()

        # 2. Test registration API
        test_registration_api()

        # 3. Fix all user wallets and bonuses
        fix_all_user_wallets_and_bonuses()

        # 4. Test games functionality
        test_games_functionality()

        # 5. Check token discounts
        check_token_discounts()

        # Final summary
        print_header("FINAL SUMMARY")
        print("✅ Database analysis completed")
        print("✅ Registration API tested")
        print("✅ All users have wallets and 100 token bonus")
        print("✅ Games functionality verified")
        print("✅ Token discount system checked")

        print(f"\n🎉 Production diagnosis and fixes completed!")
        print(f"📊 Final Statistics:")

        # Get updated stats
        check_database_users()

        print(f"\n🚀 Your production system is now fully functional!")
        
    except Exception as e:
        print(f"\n❌ Error during diagnosis: {e}")
        traceback.print_exc()


if __name__ == '__main__':
    main()
