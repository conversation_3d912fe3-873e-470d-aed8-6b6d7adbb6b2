from django.core.management.base import BaseCommand
from products.models import Product

class Command(BaseCommand):
    help = 'Delete all products that are not from Printify'

    def handle(self, *args, **options):
        # Count products before deletion
        total_before = Product.objects.count()
        printify_before = Product.objects.exclude(printify_id__isnull=True).exclude(printify_id='').count()
        non_printify_before = total_before - printify_before
        
        self.stdout.write(f"Before deletion:")
        self.stdout.write(f"Total products: {total_before}")
        self.stdout.write(f"Printify products: {printify_before}")
        self.stdout.write(f"Non-Printify products: {non_printify_before}")
        
        # Delete products without printify_id
        if non_printify_before > 0:
            deleted_null = Product.objects.filter(printify_id__isnull=True).delete()
            deleted_empty = Product.objects.filter(printify_id='').delete()
            self.stdout.write(self.style.SUCCESS(f"Deleted non-Printify products"))
        else:
            self.stdout.write(self.style.SUCCESS("No non-Printify products to delete"))
        
        # Count products after deletion
        total_after = Product.objects.count()
        printify_after = Product.objects.exclude(printify_id__isnull=True).exclude(printify_id='').count()
        non_printify_after = total_after - printify_after
        
        self.stdout.write(f"\nAfter deletion:")
        self.stdout.write(f"Total products: {total_after}")
        self.stdout.write(f"Printify products: {printify_after}")
        self.stdout.write(f"Non-Printify products: {non_printify_after}")
        
        # List remaining products
        self.stdout.write("\nRemaining products:")
        for product in Product.objects.all():
            self.stdout.write(f"- {product.name} (ID: {product.id}, Printify ID: {product.printify_id})")
