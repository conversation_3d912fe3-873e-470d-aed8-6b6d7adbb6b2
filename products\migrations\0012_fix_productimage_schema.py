# Generated manually to fix ProductImage schema conflicts

import products.fields
import products.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0011_product_cleaned'),
    ]

    operations = [
        # Ensure image_url field exists
        migrations.RunSQL(
            "ALTER TABLE products_productimage ADD COLUMN IF NOT EXISTS image_url VARCHAR(200);",
            reverse_sql="ALTER TABLE products_productimage DROP COLUMN IF EXISTS image_url;"
        ),
        
        # Update the image field to use our simplified URLOrFileField
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=products.fields.URLOrFileField(blank=True, null=True, upload_to='products/', validators=[products.models.validate_image_file]),
        ),
    ]
