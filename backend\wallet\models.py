from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
import uuid


class Wallet(models.Model):
    """
    User wallet for storing tokens
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet')
    balance = models.PositiveIntegerField(default=0, help_text="Token balance")
    total_earned = models.PositiveIntegerField(default=0, help_text="Total tokens earned")
    total_spent = models.PositiveIntegerField(default=0, help_text="Total tokens spent")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Wallet - {self.balance} tokens"

    @property
    def balance_in_inr(self):
        """Convert token balance to INR"""
        from django.conf import settings
        rate = settings.WALLET_SETTINGS.get('TOKEN_TO_INR_RATE', 0.1)
        return Decimal(str(self.balance * rate))

    def add_tokens(self, amount, transaction_type, description=""):
        """Add tokens to wallet"""
        if amount <= 0:
            raise ValueError("Amount must be positive")
        
        self.balance += amount
        self.total_earned += amount
        self.save()
        
        # Create transaction record
        WalletTransaction.objects.create(
            wallet=self,
            transaction_type=transaction_type,
            amount=amount,
            description=description,
            balance_after=self.balance
        )

    def spend_tokens(self, amount, transaction_type, description=""):
        """Spend tokens from wallet"""
        if amount <= 0:
            raise ValueError("Amount must be positive")
        if amount > self.balance:
            raise ValueError("Insufficient balance")

        self.balance -= amount
        self.total_spent += amount
        self.save()

        # Create transaction record
        WalletTransaction.objects.create(
            wallet=self,
            transaction_type=transaction_type,
            amount=-amount,
            description=description,
            balance_after=self.balance
        )

    def can_play_games(self):
        """Check if user can play token-based games"""
        return self.balance > 0

    def can_use_token_discounts(self):
        """Check if user can use token discounts in checkout"""
        return self.balance > 0

    def deduct_game_loss_penalty(self, game_id=None, description="Game loss penalty"):
        """Deduct 1 token for game loss"""
        if self.balance <= 0:
            raise ValueError("Cannot deduct tokens: insufficient balance")

        self.spend_tokens(
            amount=1,
            transaction_type='game_loss',
            description=description
        )

        # Update the transaction with game reference if provided
        if game_id:
            latest_transaction = self.transactions.first()
            # Convert string game_id to UUID if needed
            try:
                import uuid
                if isinstance(game_id, str):
                    game_id = uuid.UUID(game_id) if len(game_id) == 36 else None
                latest_transaction.game_id = game_id
                latest_transaction.save()
            except (ValueError, TypeError):
                # Invalid UUID format, skip setting game_id
                pass

    @classmethod
    def give_signup_bonus(cls, user):
        """Give 200 tokens signup bonus to new user (increased from 100)"""
        wallet, created = cls.objects.get_or_create(user=user)
        if created or wallet.transactions.filter(transaction_type='signup_bonus').count() == 0:
            wallet.add_tokens(
                amount=200,
                transaction_type='signup_bonus',
                description="Welcome bonus for new user (200 tokens)"
            )
            return True
        return False


class WalletTransaction(models.Model):
    """
    Transaction history for wallet
    """
    TRANSACTION_TYPES = [
        ('game_win', 'Game Win'),
        ('game_draw', 'Game Draw'),
        ('game_participation', 'Game Participation'),
        ('game_loss', 'Game Loss'),  # NEW: For token deduction on loss
        ('referral_bonus', 'Referral Bonus'),
        ('spin_wheel', 'Spin the Wheel'),
        ('purchase_redemption', 'Purchase Redemption'),
        ('admin_adjustment', 'Admin Adjustment'),
        ('bonus', 'Bonus'),
        ('signup_bonus', 'Signup Bonus'),  # NEW: For initial 100 tokens
        ('refill_bonus', 'Token Refill'),  # NEW: For admin token refills
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    amount = models.IntegerField(help_text="Positive for credit, negative for debit")
    description = models.TextField(blank=True, null=True)
    balance_after = models.PositiveIntegerField(help_text="Wallet balance after this transaction")
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Optional reference fields
    game_id = models.UUIDField(blank=True, null=True, help_text="Reference to game if applicable")
    order_id = models.UUIDField(blank=True, null=True, help_text="Reference to order if applicable")

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        sign = "+" if self.amount > 0 else ""
        return f"{self.wallet.user.username} - {sign}{self.amount} tokens ({self.get_transaction_type_display()})"


class TokenRequest(models.Model):
    """
    Model for users to request token refills when balance is 0
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='token_requests')
    message = models.TextField(blank=True, null=True, help_text="Optional message from user")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(blank=True, null=True)
    processed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_token_requests'
    )
    tokens_granted = models.PositiveIntegerField(default=0, help_text="Tokens granted if approved")
    admin_notes = models.TextField(blank=True, null=True, help_text="Internal notes for admin")

    class Meta:
        ordering = ['-requested_at']
        verbose_name = "Token Refill Request"
        verbose_name_plural = "Token Refill Requests"

    def __str__(self):
        return f"{self.user.username} - {self.get_status_display()} ({self.requested_at.strftime('%Y-%m-%d')})"

    def can_request_refill(self):
        """Check if user can request token refill"""
        # User must have 0 balance
        if self.user.wallet.balance > 0:
            return False, "You still have tokens remaining"

        # Check for pending requests
        pending_requests = TokenRequest.objects.filter(
            user=self.user,
            status='pending'
        ).count()

        if pending_requests > 0:
            return False, "You already have a pending token refill request"

        return True, "You can request a token refill"

    def approve(self, admin_user, tokens_to_grant=50, admin_notes=""):
        """Approve the token request and grant tokens"""
        if self.status != 'pending':
            raise ValueError("Only pending requests can be approved")

        from django.utils import timezone

        # Update request status
        self.status = 'approved'
        self.processed_at = timezone.now()
        self.processed_by = admin_user
        self.tokens_granted = tokens_to_grant
        self.admin_notes = admin_notes
        self.save()

        # Grant tokens to user
        wallet = self.user.wallet
        wallet.add_tokens(
            amount=tokens_to_grant,
            transaction_type='refill_bonus',
            description=f"Token refill approved by {admin_user.username}"
        )

        return True

    def reject(self, admin_user, admin_notes=""):
        """Reject the token request"""
        if self.status != 'pending':
            raise ValueError("Only pending requests can be rejected")

        from django.utils import timezone

        self.status = 'rejected'
        self.processed_at = timezone.now()
        self.processed_by = admin_user
        self.admin_notes = admin_notes
        self.save()

        return True


# Signal to create wallet when user is created
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=User)
def create_user_wallet(sender, instance, created, **kwargs):
    if created:
        # Create wallet and give signup bonus
        try:
            Wallet.give_signup_bonus(instance)
            print(f"✅ Created wallet and gave 200 token signup bonus to {instance.username}")
        except Exception as e:
            print(f"❌ Error creating wallet for {instance.username}: {e}")
            # Create wallet manually if signup bonus fails
            wallet, wallet_created = Wallet.objects.get_or_create(user=instance)
            if wallet_created:
                wallet.add_tokens(
                    amount=200,
                    transaction_type='signup_bonus',
                    description="Welcome bonus for new user (200 tokens)"
                )
                print(f"✅ Manually created wallet and gave 200 tokens to {instance.username}")

@receiver(post_save, sender=User)
def save_user_wallet(sender, instance, **kwargs):
    if hasattr(instance, 'wallet'):
        instance.wallet.save()
