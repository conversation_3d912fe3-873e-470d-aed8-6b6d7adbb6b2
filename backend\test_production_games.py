#!/usr/bin/env python
"""
Production Games Test
====================

Test all 5 games in production to verify they're working properly
"""

import requests
import json
import time
from datetime import datetime

# Production API base URL
BASE_URL = "https://pickmetrendofficial-render.onrender.com"

class ProductionGamesTester:
    def __init__(self):
        self.session = requests.Session()
        self.auth_token = None
        self.user_balance = 0
        
    def log(self, message, level="INFO"):
        """Log messages with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_authentication(self):
        """Test user authentication"""
        self.log("Testing authentication...")
        
        # Try to get user profile (this will show if auth is working)
        try:
            response = self.session.get(f"{BASE_URL}/api/auth/user/")
            if response.status_code == 200:
                user_data = response.json()
                self.log(f"✅ Authentication working - User: {user_data.get('username', 'Unknown')}")
                return True
            else:
                self.log(f"❌ Authentication failed - Status: {response.status_code}")
                return False
        except Exception as e:
            self.log(f"❌ Authentication error: {e}", "ERROR")
            return False
    
    def test_wallet_status(self):
        """Test wallet and token balance"""
        self.log("Testing wallet status...")
        
        try:
            response = self.session.get(f"{BASE_URL}/api/wallet/balance/")
            if response.status_code == 200:
                wallet_data = response.json()
                self.user_balance = wallet_data.get('balance', 0)
                self.log(f"✅ Wallet working - Balance: {self.user_balance} tokens")
                
                if self.user_balance < 10:
                    self.log("⚠️ Low token balance - may need refill for testing")
                
                return True
            else:
                self.log(f"❌ Wallet check failed - Status: {response.status_code}")
                return False
        except Exception as e:
            self.log(f"❌ Wallet error: {e}", "ERROR")
            return False
    
    def test_rock_paper_scissors(self):
        """Test Rock Paper Scissors game"""
        self.log("🎮 Testing Rock Paper Scissors...")
        
        try:
            # Test create AI battle
            response = self.session.post(
                f"{BASE_URL}/api/gaming/create-ai-battle/",
                json={"game_type": "rock_paper_scissors"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 201:
                battle_data = response.json()
                battle_id = battle_data.get('battle_id')
                self.log(f"✅ Rock Paper Scissors - Battle created: {battle_id}")
                return True
            else:
                self.log(f"❌ Rock Paper Scissors failed - Status: {response.status_code}")
                self.log(f"Response: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Rock Paper Scissors error: {e}", "ERROR")
            return False
    
    def test_number_guessing(self):
        """Test Number Guessing game"""
        self.log("🎮 Testing Number Guessing...")
        
        try:
            # Test create AI battle
            response = self.session.post(
                f"{BASE_URL}/api/gaming/create-ai-battle/",
                json={"game_type": "number_guessing"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 201:
                battle_data = response.json()
                battle_id = battle_data.get('battle_id')
                self.log(f"✅ Number Guessing - Battle created: {battle_id}")
                return True
            else:
                self.log(f"❌ Number Guessing failed - Status: {response.status_code}")
                self.log(f"Response: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Number Guessing error: {e}", "ERROR")
            return False
    
    def test_tic_tac_toe(self):
        """Test Tic Tac Toe game"""
        self.log("🎮 Testing Tic Tac Toe...")
        
        try:
            # Test dedicated Tic Tac Toe endpoint
            response = self.session.post(
                f"{BASE_URL}/api/gaming/tic-tac-toe/start/",
                json={"difficulty": "medium"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                game_data = response.json()
                game_id = game_data.get('game_id')
                self.log(f"✅ Tic Tac Toe - Game started: {game_id}")
                return True
            else:
                self.log(f"❌ Tic Tac Toe failed - Status: {response.status_code}")
                self.log(f"Response: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Tic Tac Toe error: {e}", "ERROR")
            return False
    
    def test_color_match(self):
        """Test Color Match game"""
        self.log("🎮 Testing Color Match...")
        
        try:
            # Test dedicated Color Match endpoint
            response = self.session.post(
                f"{BASE_URL}/api/gaming/color-match/start/",
                json={"difficulty": "medium"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                game_data = response.json()
                game_id = game_data.get('game_id')
                self.log(f"✅ Color Match - Game started: {game_id}")
                return True
            else:
                self.log(f"❌ Color Match failed - Status: {response.status_code}")
                self.log(f"Response: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Color Match error: {e}", "ERROR")
            return False
    
    def test_memory_card(self):
        """Test Memory Card Match game"""
        self.log("🎮 Testing Memory Card Match...")
        
        try:
            # Test dedicated Memory Card endpoint
            response = self.session.post(
                f"{BASE_URL}/api/gaming/memory-card/start/",
                json={"difficulty": "medium"},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                game_data = response.json()
                game_id = game_data.get('game_id')
                self.log(f"✅ Memory Card Match - Game started: {game_id}")
                return True
            else:
                self.log(f"❌ Memory Card Match failed - Status: {response.status_code}")
                self.log(f"Response: {response.text}")
                return False
                
        except Exception as e:
            self.log(f"❌ Memory Card Match error: {e}", "ERROR")
            return False
    
    def test_gaming_dashboard_access(self):
        """Test if gaming dashboard is accessible"""
        self.log("Testing gaming dashboard access...")
        
        try:
            response = self.session.get(f"{BASE_URL}/gaming/")
            if response.status_code == 200:
                self.log("✅ Gaming dashboard accessible")
                return True
            else:
                self.log(f"❌ Gaming dashboard failed - Status: {response.status_code}")
                return False
        except Exception as e:
            self.log(f"❌ Gaming dashboard error: {e}", "ERROR")
            return False
    
    def run_comprehensive_test(self):
        """Run comprehensive test of all games"""
        self.log("🧪 STARTING COMPREHENSIVE PRODUCTION GAMES TEST")
        self.log("=" * 60)
        
        results = {
            "auth": False,
            "wallet": False,
            "dashboard": False,
            "rock_paper_scissors": False,
            "number_guessing": False,
            "tic_tac_toe": False,
            "color_match": False,
            "memory_card": False
        }
        
        # Test authentication
        results["auth"] = self.test_authentication()
        
        # Test wallet
        results["wallet"] = self.test_wallet_status()
        
        # Test dashboard access
        results["dashboard"] = self.test_gaming_dashboard_access()
        
        # Test all games
        if results["auth"] and results["wallet"]:
            self.log("\n🎮 TESTING ALL GAMES")
            self.log("-" * 30)
            
            results["rock_paper_scissors"] = self.test_rock_paper_scissors()
            time.sleep(1)  # Small delay between tests
            
            results["number_guessing"] = self.test_number_guessing()
            time.sleep(1)
            
            results["tic_tac_toe"] = self.test_tic_tac_toe()
            time.sleep(1)
            
            results["color_match"] = self.test_color_match()
            time.sleep(1)
            
            results["memory_card"] = self.test_memory_card()
        else:
            self.log("⚠️ Skipping game tests due to auth/wallet issues")
        
        # Generate report
        self.generate_report(results)
        
        return results
    
    def generate_report(self, results):
        """Generate comprehensive test report"""
        self.log("\n📊 PRODUCTION GAMES TEST REPORT")
        self.log("=" * 50)
        
        # Count successes
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        
        self.log(f"Overall Status: {passed_tests}/{total_tests} tests passed")
        
        # Detailed results
        self.log("\n📋 Detailed Results:")
        status_map = {
            "auth": "🔐 Authentication",
            "wallet": "💰 Wallet System",
            "dashboard": "🎮 Gaming Dashboard",
            "rock_paper_scissors": "✂️ Rock Paper Scissors",
            "number_guessing": "🔢 Number Guessing",
            "tic_tac_toe": "⭕ Tic Tac Toe",
            "color_match": "🎨 Color Match",
            "memory_card": "🃏 Memory Card Match"
        }
        
        for key, description in status_map.items():
            status = "✅ WORKING" if results[key] else "❌ FAILED"
            self.log(f"   {description}: {status}")
        
        # Game-specific analysis
        game_results = {k: v for k, v in results.items() if k not in ["auth", "wallet", "dashboard"]}
        working_games = sum(1 for result in game_results.values() if result)
        
        self.log(f"\n🎯 Games Status: {working_games}/5 games working")
        
        if working_games == 5:
            self.log("🎉 ALL GAMES ARE WORKING PERFECTLY!")
        elif working_games >= 3:
            self.log("✅ Most games are working - minor issues to fix")
        else:
            self.log("⚠️ Major issues detected - needs immediate attention")
        
        # Recommendations
        self.log("\n💡 Recommendations:")
        if not results["auth"]:
            self.log("   • Fix authentication system")
        if not results["wallet"]:
            self.log("   • Check wallet/token system")
        if not results["dashboard"]:
            self.log("   • Verify gaming dashboard deployment")
        
        failed_games = [game for game, status in game_results.items() if not status]
        if failed_games:
            self.log(f"   • Fix these games: {', '.join(failed_games)}")
        
        self.log("\n🚀 Test completed!")


def main():
    """Main function to run production tests"""
    print("🧪 PRODUCTION GAMES TESTING TOOL")
    print("=" * 40)
    print("This tool will test all 5 games in production")
    print("URL:", BASE_URL)
    print()
    
    # Note about authentication
    print("⚠️ NOTE: This test requires authentication")
    print("Please ensure you have a valid session or provide credentials")
    print()
    
    # Create tester and run tests
    tester = ProductionGamesTester()
    results = tester.run_comprehensive_test()
    
    return results


if __name__ == '__main__':
    main()
