from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import ReturnRequest


@admin.register(ReturnRequest)
class ReturnRequestAdmin(admin.ModelAdmin):
    """
    Admin interface for return requests
    """
    list_display = [
        'id', 'order_link', 'user_email', 'reason_display',
        'status_badge', 'refund_amount', 'created_at', 'updated_at'
    ]
    list_filter = ['status', 'reason', 'created_at', 'updated_at']
    search_fields = [
        'order__id', 'user__email', 'user__username',
        'reason_detail', 'admin_response'
    ]
    readonly_fields = [
        'id', 'order', 'user', 'created_at', 'updated_at',
        'order_details', 'user_details'
    ]

    fieldsets = (
        ('Return Request Information', {
            'fields': ('id', 'order', 'user', 'created_at', 'updated_at')
        }),
        ('Return Details', {
            'fields': ('reason', 'reason_detail', 'status')
        }),
        ('Admin Response', {
            'fields': ('admin_response', 'admin_notes', 'refund_amount', 'tracking_number', 'processed_at')
        }),
        ('Related Information', {
            'fields': ('order_details', 'user_details'),
            'classes': ('collapse',)
        })
    )

    actions = ['approve_returns', 'reject_returns', 'mark_as_processing']

    def order_link(self, obj):
        """Create a link to the order in admin"""
        url = reverse('admin:orders_order_change', args=[obj.order.id])
        return format_html('<a href="{}">{}</a>', url, obj.order.id)
    order_link.short_description = 'Order'

    def user_email(self, obj):
        """Display user email"""
        return obj.user.email
    user_email.short_description = 'Customer Email'

    def reason_display(self, obj):
        """Display reason with tooltip"""
        return format_html(
            '<span title="{}">{}</span>',
            obj.reason_detail[:100] + '...' if len(obj.reason_detail) > 100 else obj.reason_detail,
            obj.get_reason_display()
        )
    reason_display.short_description = 'Reason'

    def status_badge(self, obj):
        """Display status with colored badge"""
        colors = {
            'pending': '#ffc107',      # Yellow
            'approved': '#28a745',     # Green
            'rejected': '#dc3545',     # Red
            'processing': '#17a2b8',   # Blue
            'completed': '#6c757d',    # Gray
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def order_details(self, obj):
        """Display order details"""
        order = obj.order
        items_html = ""
        for item in order.items.all():
            items_html += f"<li>{item.quantity}x {item.product.name} - ₹{item.total_price}</li>"

        return format_html(
            """
            <div>
                <strong>Order Total:</strong> ₹{}<br>
                <strong>Order Status:</strong> {}<br>
                <strong>Payment Status:</strong> {}<br>
                <strong>Items:</strong>
                <ul>{}</ul>
            </div>
            """,
            order.total,
            order.get_status_display(),
            order.get_payment_status_display(),
            items_html
        )
    order_details.short_description = 'Order Details'

    def user_details(self, obj):
        """Display user details"""
        user = obj.user
        return format_html(
            """
            <div>
                <strong>Name:</strong> {}<br>
                <strong>Email:</strong> {}<br>
                <strong>Username:</strong> {}<br>
                <strong>Date Joined:</strong> {}
            </div>
            """,
            user.get_full_name() or 'Not provided',
            user.email,
            user.username,
            user.date_joined.strftime('%Y-%m-%d %H:%M')
        )
    user_details.short_description = 'Customer Details'

    def approve_returns(self, request, queryset):
        """Bulk approve return requests"""
        updated = queryset.filter(status='pending').update(status='approved')
        self.message_user(request, f'{updated} return requests approved.')
    approve_returns.short_description = 'Approve selected return requests'

    def reject_returns(self, request, queryset):
        """Bulk reject return requests"""
        updated = queryset.filter(status='pending').update(status='rejected')
        self.message_user(request, f'{updated} return requests rejected.')
    reject_returns.short_description = 'Reject selected return requests'

    def mark_as_processing(self, request, queryset):
        """Bulk mark as processing"""
        updated = queryset.filter(status__in=['pending', 'approved']).update(status='processing')
        self.message_user(request, f'{updated} return requests marked as processing.')
    mark_as_processing.short_description = 'Mark as processing'

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('order', 'user')

    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of return requests"""
        return False
