from rest_framework import serializers
from .models import PrintifyConfig, PrintifyProduct, PrintifyOrder

class PrintifyConfigSerializer(serializers.ModelSerializer):
    """
    Serializer for PrintifyConfig model
    """
    class Meta:
        model = PrintifyConfig
        fields = ['id', 'api_token', 'is_active', 'created_at', 'updated_at']
        extra_kwargs = {
            'api_token': {'write_only': True}
        }

class PrintifyProductSerializer(serializers.ModelSerializer):
    """
    Serializer for PrintifyProduct model
    """
    class Meta:
        model = PrintifyProduct
        fields = [
            'id', 'printify_id', 'title', 'description', 'blueprint_id',
            'print_provider_id', 'variants_json', 'images_json',
            'created_at', 'updated_at'
        ]

class PrintifyOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for PrintifyOrder model
    """
    class Meta:
        model = PrintifyOrder
        fields = [
            'id', 'printify_id', 'user', 'status', 'external_id',
            'shipping_method', 'shipping_address_json', 'line_items_json',
            'total_price', 'total_shipping', 'total_tax',
            'created_at', 'updated_at'
        ]

class PrintifyCatalogSerializer(serializers.Serializer):
    """
    Serializer for Printify catalog data
    """
    id = serializers.IntegerField()
    title = serializers.CharField()
    description = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    brand = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    model = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    images = serializers.ListField(child=serializers.URLField(), required=False)

class PrintifyVariantSerializer(serializers.Serializer):
    """
    Serializer for Printify variant data
    """
    id = serializers.IntegerField()
    title = serializers.CharField()
    options = serializers.DictField(required=False)
    placeholders = serializers.ListField(required=False)
    is_enabled = serializers.BooleanField(required=False)
    is_default = serializers.BooleanField(required=False)
    sku = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    cost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)

class PrintifyShippingSerializer(serializers.Serializer):
    """
    Serializer for Printify shipping data
    """
    handling_time = serializers.DictField(required=False)
    profiles = serializers.ListField(required=False)

class PrintifyOrderCreateSerializer(serializers.Serializer):
    """
    Serializer for creating a Printify order
    """
    external_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    line_items = serializers.ListField()
    shipping_method = serializers.IntegerField(required=False)
    shipping_address = serializers.DictField()
    send_shipping_notification = serializers.BooleanField(required=False, default=False)

class PrintifyImageUploadSerializer(serializers.Serializer):
    """
    Serializer for uploading an image to Printify
    """
    file_name = serializers.CharField()
    url = serializers.URLField(required=False)
    contents = serializers.CharField(required=False)
