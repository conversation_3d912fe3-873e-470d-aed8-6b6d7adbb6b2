#!/usr/bin/env python
"""
Simple script to check if token discount fields exist
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from products.models import Product

# Check if fields exist
print("Checking Product model fields...")

# Get a product instance
try:
    product = Product.objects.first()
    if product:
        print(f"Found product: {product.name}")
        
        # Check for token fields
        fields_to_check = [
            'allow_token_discount',
            'token_discount_percentage', 
            'token_discount_max_amount'
        ]
        
        for field in fields_to_check:
            if hasattr(product, field):
                value = getattr(product, field)
                print(f"✅ {field}: {value}")
            else:
                print(f"❌ {field}: NOT FOUND")
                
        # Try to access the property
        try:
            available = product.token_discount_available
            print(f"✅ token_discount_available property: {available}")
        except AttributeError as e:
            print(f"❌ token_discount_available property: {e}")
            
        # Try to call the method
        try:
            discount_info = product.calculate_max_token_discount(1)
            print(f"✅ calculate_max_token_discount method: {discount_info}")
        except AttributeError as e:
            print(f"❌ calculate_max_token_discount method: {e}")
            
    else:
        print("No products found in database")
        
except Exception as e:
    print(f"Error: {e}")

# Check model fields directly
print("\nChecking model fields directly...")
for field in Product._meta.get_fields():
    if 'token' in field.name.lower():
        print(f"Found token field: {field.name} ({type(field).__name__})")
