#!/usr/bin/env python
"""
Test cart API endpoint
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from orders.models import Cart
from django.urls import reverse


def test_cart_api():
    print("🛒 Testing Cart API Endpoint")
    print("=" * 50)
    
    # Get test user
    user = User.objects.filter(username='token_discount_test').first()
    if not user:
        print("❌ Test user not found")
        return
    
    print(f"👤 Test User: {user.username}")
    
    # Get or create cart
    cart, created = Cart.objects.get_or_create(user=user)
    print(f"🛒 Cart ID: {cart.id}")
    
    # Generate JWT token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    client = Client()
    
    # Test the cart token discount endpoint
    url = f'/api/orders/cart/{cart.id}/token-discount-info/'
    print(f"🌐 Testing URL: {url}")
    
    response = client.get(
        url,
        HTTP_AUTHORIZATION=f'Bearer {access_token}'
    )
    
    print(f"📊 Response Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Success! Response: {data}")
    else:
        print(f"❌ Error: {response.content.decode()[:500]}")
    
    # Test cart list endpoint
    print(f"\n🛒 Testing Cart List Endpoint")
    response = client.get(
        '/api/orders/cart/',
        HTTP_AUTHORIZATION=f'Bearer {access_token}'
    )
    
    print(f"📊 Cart List Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Cart List Success! Found {len(data)} carts")
    else:
        print(f"❌ Cart List Error: {response.content.decode()[:200]}")


if __name__ == '__main__':
    test_cart_api()
