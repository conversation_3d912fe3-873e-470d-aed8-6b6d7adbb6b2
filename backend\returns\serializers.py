from rest_framework import serializers
from .models import ReturnRequest
from orders.models import Order
from orders.serializers import OrderSerializer


class ReturnRequestCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating return requests
    """
    order_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = ReturnRequest
        fields = ['order_id', 'reason', 'reason_detail']

    def validate_order_id(self, value):
        """Validate that the order exists and belongs to the user"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            raise serializers.ValidationError("Authentication required")

        try:
            order = Order.objects.get(id=value, user=request.user)
        except Order.DoesNotExist:
            raise serializers.ValidationError("Order not found or does not belong to you")

        # Check if order is delivered
        if order.status != 'delivered':
            raise serializers.ValidationError("Returns can only be requested for delivered orders")

        # Check if return request already exists
        if ReturnRequest.objects.filter(order=order, user=request.user).exists():
            raise serializers.ValidationError("A return request already exists for this order")

        return value

    def create(self, validated_data):
        """Create return request"""
        request = self.context.get('request')
        order_id = validated_data.pop('order_id')
        order = Order.objects.get(id=order_id)

        return ReturnRequest.objects.create(
            order=order,
            user=request.user,
            **validated_data
        )


class ReturnRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for return request details
    """
    order = OrderSerializer(read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_name = serializers.CharField(source='user.get_full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reason_display = serializers.CharField(source='get_reason_display', read_only=True)
    can_be_returned = serializers.BooleanField(read_only=True)

    class Meta:
        model = ReturnRequest
        fields = [
            'id', 'order', 'user_email', 'user_name',
            'reason', 'reason_display', 'reason_detail',
            'status', 'status_display', 'admin_response',
            'refund_amount', 'tracking_number',
            'created_at', 'updated_at', 'processed_at',
            'can_be_returned'
        ]
        read_only_fields = [
            'id', 'order', 'user_email', 'user_name',
            'status', 'admin_response', 'refund_amount',
            'tracking_number', 'created_at', 'updated_at',
            'processed_at', 'can_be_returned'
        ]


class ReturnRequestUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for admin to update return requests
    """
    class Meta:
        model = ReturnRequest
        fields = ['status', 'admin_response', 'admin_notes', 'refund_amount', 'tracking_number']

    def validate_status(self, value):
        """Validate status transitions"""
        instance = self.instance
        if instance and instance.status == 'completed':
            raise serializers.ValidationError("Cannot modify completed return requests")
        return value


class ReturnRequestListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for listing return requests
    """
    order_id = serializers.CharField(source='order.id', read_only=True)
    order_total = serializers.DecimalField(source='order.total', max_digits=10, decimal_places=2, read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reason_display = serializers.CharField(source='get_reason_display', read_only=True)

    class Meta:
        model = ReturnRequest
        fields = [
            'id', 'order_id', 'order_total', 'user_email',
            'reason', 'reason_display', 'status', 'status_display',
            'refund_amount', 'created_at', 'updated_at'
        ]
