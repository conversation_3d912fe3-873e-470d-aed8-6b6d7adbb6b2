@echo off
REM 🚀 PickMeTrend Repository Preparation Script (Windows)
REM Prepares backend and frontend for separate GitHub repositories

echo 🚀 PickMeTrend Repository Preparation
echo ====================================

REM Check if we're in the right directory
if not exist "backend" (
    echo [ERROR] Backend directory not found
    echo Please run this script from the project root directory
    echo Expected structure: .\backend\ and .\frontend\
    pause
    exit /b 1
)

if not exist "frontend" (
    echo [ERROR] Frontend directory not found
    echo Please run this script from the project root directory
    echo Expected structure: .\backend\ and .\frontend\
    pause
    exit /b 1
)

echo [INFO] Preparing backend repository...

REM Navigate to backend directory
cd backend

REM Check if git is already initialized
if not exist ".git" (
    echo [INFO] Initializing git repository for backend...
    git init
    echo [SUCCESS] Git repository initialized
) else (
    echo [WARNING] Git repository already exists in backend
)

REM Create .gitignore for backend
echo [INFO] Creating backend .gitignore...
(
echo # Python
echo __pycache__/
echo *.py[cod]
echo *$py.class
echo *.so
echo .Python
echo build/
echo develop-eggs/
echo dist/
echo downloads/
echo eggs/
echo .eggs/
echo lib/
echo lib64/
echo parts/
echo sdist/
echo var/
echo wheels/
echo *.egg-info/
echo .installed.cfg
echo *.egg
echo.
echo # Django
echo *.log
echo local_settings.py
echo db.sqlite3
echo db.sqlite3-journal
echo media/
echo.
echo # Environment variables
echo .env
echo .venv
echo env/
echo venv/
echo ENV/
echo env.bak/
echo venv.bak/
echo.
echo # IDE
echo .vscode/
echo .idea/
echo *.swp
echo *.swo
echo *~
echo.
echo # OS
echo .DS_Store
echo .DS_Store?
echo ._*
echo .Spotlight-V100
echo .Trashes
echo ehthumbs.db
echo Thumbs.db
echo.
echo # Backup files
echo *.bak
echo *.backup
echo backups/
echo.
echo # Temporary files
echo *.tmp
echo *.temp
) > .gitignore

echo [SUCCESS] Backend .gitignore created

REM Add all backend files
echo [INFO] Adding backend files to git...
git add .
echo [SUCCESS] Backend files added

REM Create initial commit
echo [INFO] Creating initial backend commit...
git commit -m "🎮 Initial backend setup with gaming system

Features:
- Django REST API with gaming system
- Tic Tac Toe game with hard mode AI
- Token economy (Win +5, Draw +2, Loss -1)
- Wallet system with shopping integration
- E-commerce API with Printify integration
- Razorpay payment integration
- Redis/WebSocket support for real-time gaming
- Production ready with Render deployment config

Tech Stack:
- Django 4.2 + DRF
- PostgreSQL database
- Redis (Upstash) for gaming/caching
- Django Channels for WebSocket
- Cloudinary for media storage
- SendGrid for email
- Production deployment via Render.com"

echo [SUCCESS] Backend initial commit created

REM Go back to project root
cd ..

echo [INFO] Preparing frontend repository...

REM Navigate to frontend directory
cd frontend

REM Check if git is already initialized
if not exist ".git" (
    echo [INFO] Initializing git repository for frontend...
    git init
    echo [SUCCESS] Git repository initialized
) else (
    echo [WARNING] Git repository already exists in frontend
)

REM Create .gitignore for frontend
echo [INFO] Creating frontend .gitignore...
(
echo # Dependencies
echo node_modules/
echo /.pnp
echo .pnp.js
echo.
echo # Production build
echo /build
echo.
echo # Environment variables
echo .env
echo .env.local
echo .env.development.local
echo .env.test.local
echo .env.production.local
echo.
echo # Logs
echo npm-debug.log*
echo yarn-debug.log*
echo yarn-error.log*
echo lerna-debug.log*
echo.
echo # IDE
echo .vscode/
echo .idea/
echo *.swp
echo *.swo
echo *~
echo.
echo # OS
echo .DS_Store
echo .DS_Store?
echo ._*
echo .Spotlight-V100
echo .Trashes
echo ehthumbs.db
echo Thumbs.db
echo.
echo # Optional npm cache directory
echo .npm
echo.
echo # Optional eslint cache
echo .eslintcache
echo.
echo # Temporary folders
echo tmp/
echo temp/
) > .gitignore

echo [SUCCESS] Frontend .gitignore created

REM Add all frontend files
echo [INFO] Adding frontend files to git...
git add .
echo [SUCCESS] Frontend files added

REM Create initial commit
echo [INFO] Creating initial frontend commit...
git commit -m "🎮 Initial frontend setup with gaming interface

Features:
- React TypeScript application
- Gaming interface with Tic Tac Toe game
- Beautiful UI with modern design system
- Token wallet integration
- E-commerce shopping interface
- Razorpay payment integration
- Real-time token balance updates
- Responsive design for all devices

Gaming Features:
- Hard mode Tic Tac Toe with AI opponent
- Token rewards system
- Game statistics tracking
- Celebration animations and effects

E-commerce Features:
- Product catalog browsing
- Shopping cart with variants
- Token discount system
- Secure checkout flow

Tech Stack:
- React 18 + TypeScript
- Tailwind CSS for styling
- React Router for navigation
- Axios for API communication
- Context API for state management
- Production deployment via Render.com"

echo [SUCCESS] Frontend initial commit created

REM Go back to project root
cd ..

echo [SUCCESS] Repository preparation complete!
echo.
echo 📋 Next Steps:
echo ==============
echo.
echo 1. 🔧 Create GitHub Repositories:
echo    - Backend: Using existing 'pickmetrendofficial-render' repository
echo    - Create 'pickmetrend-frontend' repository on GitHub
echo.
echo 2. 🚀 Push Backend:
echo    cd backend
echo    git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git
echo    git branch -M main
echo    git push -u origin main
echo.
echo 3. 🎨 Push Frontend:
echo    cd frontend
echo    git remote add origin https://github.com/yourusername/pickmetrend-frontend.git
echo    git branch -M main
echo    git push -u origin main
echo.
echo 4. 🌐 Deploy to Render:
echo    - Backend: Use Blueprint deployment with render.yaml
echo    - Frontend: Use Static Site deployment
echo.
echo 5. 📖 Read the deployment guide:
echo    - Check SEPARATE_REPOSITORY_DEPLOYMENT.md for detailed instructions
echo.
echo [SUCCESS] Your gaming e-commerce platform is ready for deployment! 🎮🛒🚀
echo.
pause
