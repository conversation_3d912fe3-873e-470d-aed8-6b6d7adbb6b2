from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON>out<PERSON>
from . import views

# Create a router for the API
router = DefaultRouter()
router.register(r'tracking', views.OrderTrackingViewSet, basename='order-tracking')

# URL patterns
urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),

    # Direct API access (for easier testing)
    path('tracking/', views.OrderTrackingViewSet.as_view({'get': 'list'}), name='tracking-list'),
    path('tracking/<str:pk>/', views.OrderTrackingViewSet.as_view({'get': 'retrieve'}), name='tracking-detail'),

    # Frontend view
    path('orders/<uuid:order_id>/tracking/', views.order_tracking_view, name='order-tracking'),
]
