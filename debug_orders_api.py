#!/usr/bin/env python
"""
Debug the orders API to understand why frontend can't load recent orders
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from orders.models import Order
from products.models import Product, Category
from decimal import Decimal
import json

def debug_orders_api():
    print("🔍 Debugging Orders API Issue")
    print("=" * 50)
    
    # Check if we have any users
    users = User.objects.all()
    print(f"👤 Total Users: {users.count()}")
    
    if users.count() == 0:
        print("❌ No users found! Creating test user...")
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"✅ Created test user: {user.username}")
    else:
        user = users.first()
        print(f"✅ Using existing user: {user.username}")
    
    # Check if we have any orders
    orders = Order.objects.filter(user=user)
    print(f"📦 Orders for {user.username}: {orders.count()}")
    
    if orders.count() == 0:
        print("📦 No orders found! Creating test order...")
        
        # Create a test category and product first
        category, created = Category.objects.get_or_create(
            name='Test Category',
            defaults={'slug': 'test-category', 'is_active': True}
        )
        
        product, created = Product.objects.get_or_create(
            name='Test Product',
            defaults={
                'slug': 'test-product',
                'price': Decimal('99.99'),
                'stock': 10,
                'is_active': True
            }
        )
        product.categories.add(category)
        
        # Create test order
        order = Order.objects.create(
            user=user,
            total=Decimal('99.99'),
            status='processing',
            payment_method='razorpay',
            full_name='Test User',
            email='<EMAIL>',
            phone='1234567890',
            address='Test Address',
            city='Test City',
            state='Test State',
            zipcode='123456',
            country='India'
        )
        print(f"✅ Created test order: {order.id}")
    
    # Test API endpoints
    client = Client()
    
    print("\n🌐 Testing API Endpoints")
    print("-" * 30)
    
    # Test without authentication
    print("1. Testing without authentication:")
    response = client.get('/api/orders/')
    print(f"   Status: {response.status_code}")
    if response.status_code != 200:
        print(f"   Response: {response.content.decode()}")
    
    # Test with authentication
    print("\n2. Testing with authentication:")
    client.force_login(user)
    response = client.get('/api/orders/')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Success! Found {len(data.get('results', []))} orders")
        if data.get('results'):
            order = data['results'][0]
            print(f"   📦 Sample order: {order.get('id', 'N/A')} - {order.get('status', 'N/A')} - ₹{order.get('total', 'N/A')}")
    else:
        print(f"   ❌ Error: {response.content.decode()}")
    
    # Test with limit parameter
    print("\n3. Testing with limit parameter:")
    response = client.get('/api/orders/?limit=5')
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Success! Found {len(data.get('results', []))} orders with limit")
    else:
        print(f"   ❌ Error: {response.content.decode()}")
    
    # Test JWT token authentication (simulate frontend)
    print("\n4. Testing JWT token authentication:")
    from rest_framework_simplejwt.tokens import RefreshToken
    
    # Generate JWT token
    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)
    
    # Test with JWT header
    response = client.get(
        '/api/orders/?limit=5',
        HTTP_AUTHORIZATION=f'JWT {access_token}'
    )
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ JWT Success! Found {len(data.get('results', []))} orders")
    else:
        print(f"   ❌ JWT Error: {response.content.decode()}")
    
    # Test with Bearer token (alternative format)
    print("\n5. Testing Bearer token authentication:")
    response = client.get(
        '/api/orders/?limit=5',
        HTTP_AUTHORIZATION=f'Bearer {access_token}'
    )
    print(f"   Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Bearer Success! Found {len(data.get('results', []))} orders")
    else:
        print(f"   ❌ Bearer Error: {response.content.decode()}")
    
    print("\n" + "=" * 50)
    print("🔧 Debugging Summary:")
    print(f"   👤 User: {user.username} (ID: {user.id})")
    print(f"   📦 Orders: {Order.objects.filter(user=user).count()}")
    print(f"   🔑 JWT Token: {access_token[:50]}...")
    
    print("\n💡 Frontend Fix Suggestions:")
    print("1. Check if JWT token is properly stored in localStorage")
    print("2. Verify API_URL environment variable")
    print("3. Check browser network tab for actual error")
    print("4. Try using 'Bearer' instead of 'JWT' in Authorization header")
    
    print("\n🧪 Test JWT Token in Browser Console:")
    print(f"localStorage.setItem('access_token', '{access_token}');")

if __name__ == '__main__':
    debug_orders_api()
