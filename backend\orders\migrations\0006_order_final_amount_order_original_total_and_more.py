# Generated by Django 5.0.2 on 2025-06-03 11:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0005_alter_cartitem_unique_together'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='final_amount',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Final amount after token discount', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='original_total',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Original total before token discount', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='order',
            name='token_value',
            field=models.DecimalField(decimal_places=2, default=0, help_text='INR value of redeemed tokens', max_digits=10),
        ),
        migrations.AddField(
            model_name='order',
            name='tokens_used',
            field=models.PositiveIntegerField(default=0, help_text='Number of tokens redeemed for this order'),
        ),
    ]
