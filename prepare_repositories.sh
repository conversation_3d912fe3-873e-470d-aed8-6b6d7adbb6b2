#!/bin/bash

# 🚀 PickMeTrend Repository Preparation Script
# Prepares backend and frontend for separate GitHub repositories

echo "🚀 PickMeTrend Repository Preparation"
echo "===================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_error "Please run this script from the project root directory"
    print_error "Expected structure: ./backend/ and ./frontend/"
    exit 1
fi

print_status "Preparing backend repository..."

# Create backend repository structure
cd backend

# Check if git is already initialized
if [ ! -d ".git" ]; then
    print_status "Initializing git repository for backend..."
    git init
    print_success "Git repository initialized"
else
    print_warning "Git repository already exists in backend"
fi

# Create .gitignore for backend
print_status "Creating backend .gitignore..."
cat > .gitignore << EOF
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
backups/

# Temporary files
*.tmp
*.temp
EOF

print_success "Backend .gitignore created"

# Add all backend files
print_status "Adding backend files to git..."
git add .
print_success "Backend files added"

# Create initial commit
print_status "Creating initial backend commit..."
git commit -m "🎮 Initial backend setup with gaming system

Features:
- Django REST API with gaming system
- Tic Tac Toe game with hard mode AI
- Token economy (Win +5, Draw +2, Loss -1)
- Wallet system with shopping integration
- E-commerce API with Printify integration
- Razorpay payment integration
- Redis/WebSocket support for real-time gaming
- Production ready with Render deployment config

Tech Stack:
- Django 4.2 + DRF
- PostgreSQL database
- Redis (Upstash) for gaming/caching
- Django Channels for WebSocket
- Cloudinary for media storage
- SendGrid for email
- Production deployment via Render.com"

print_success "Backend initial commit created"

# Go back to project root
cd ..

print_status "Preparing frontend repository..."

# Create frontend repository structure
cd frontend

# Check if git is already initialized
if [ ! -d ".git" ]; then
    print_status "Initializing git repository for frontend..."
    git init
    print_success "Git repository initialized"
else
    print_warning "Git repository already exists in frontend"
fi

# Create .gitignore for frontend
print_status "Creating frontend .gitignore..."
cat > .gitignore << EOF
# Dependencies
node_modules/
/.pnp
.pnp.js

# Production build
/build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
EOF

print_success "Frontend .gitignore created"

# Add all frontend files
print_status "Adding frontend files to git..."
git add .
print_success "Frontend files added"

# Create initial commit
print_status "Creating initial frontend commit..."
git commit -m "🎮 Initial frontend setup with gaming interface

Features:
- React TypeScript application
- Gaming interface with Tic Tac Toe game
- Beautiful UI with modern design system
- Token wallet integration
- E-commerce shopping interface
- Razorpay payment integration
- Real-time token balance updates
- Responsive design for all devices

Gaming Features:
- Hard mode Tic Tac Toe with AI opponent
- Token rewards system
- Game statistics tracking
- Celebration animations and effects

E-commerce Features:
- Product catalog browsing
- Shopping cart with variants
- Token discount system
- Secure checkout flow

Tech Stack:
- React 18 + TypeScript
- Tailwind CSS for styling
- React Router for navigation
- Axios for API communication
- Context API for state management
- Production deployment via Render.com"

print_success "Frontend initial commit created"

# Go back to project root
cd ..

print_success "Repository preparation complete!"
echo ""
echo "📋 Next Steps:"
echo "=============="
echo ""
echo "1. 🔧 Create GitHub Repositories:"
echo "   - Backend: Using existing 'pickmetrendofficial-render' repository"
echo "   - Create 'pickmetrend-frontend' repository on GitHub"
echo ""
echo "2. 🚀 Push Backend:"
echo "   cd backend"
echo "   git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "3. 🎨 Push Frontend:"
echo "   cd frontend"
echo "   git remote add origin https://github.com/yourusername/pickmetrend-frontend.git"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "4. 🌐 Deploy to Render:"
echo "   - Backend: Use Blueprint deployment with render.yaml"
echo "   - Frontend: Use Static Site deployment"
echo ""
echo "5. 📖 Read the deployment guide:"
echo "   - Check SEPARATE_REPOSITORY_DEPLOYMENT.md for detailed instructions"
echo ""
print_success "Your gaming e-commerce platform is ready for deployment! 🎮🛒🚀"
