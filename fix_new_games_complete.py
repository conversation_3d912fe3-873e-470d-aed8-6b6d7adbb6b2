#!/usr/bin/env python
"""
Complete Fix for New Games
==========================

This script comprehensively fixes all issues with Color Match and Memory Card games.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, Battle
from gaming.game_logic import GameEngine
from gaming.ai_bot import AIBot
from wallet.models import Wallet
from django.test import Client
import json


def fix_game_types():
    """Ensure all game types exist in database"""
    print("🎮 Fixing Game Types in Database")
    print("=" * 40)
    
    # Define ALL games that should exist
    all_games = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic battle game - Play vs AI or human opponents!',
            'rules': {
                'choices': ['rock', 'paper', 'scissors'],
                'win_conditions': {
                    'rock': 'scissors',
                    'paper': 'rock',
                    'scissors': 'paper'
                },
                'max_rounds': 3
            }
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number - Play vs AI or human opponents!',
            'rules': {
                'min_number': 1,
                'max_number': 100,
                'max_attempts': 5
            }
        },
        {
            'name': 'tic_tac_toe',
            'display_name': 'Tic Tac Toe',
            'description': 'Classic strategy game against AI. Get 3 in a row to win!',
            'rules': {
                'board_size': 3,
                'win_condition': '3_in_a_row',
                'ai_difficulty': 'hard'
            }
        },
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember color sequences - Play vs AI or human opponents!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True,
                'difficulty_levels': ['easy', 'medium', 'hard']
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs - Play vs AI or human opponents!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
                'difficulty_levels': ['easy', 'medium', 'hard']
            }
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for game_data in all_games:
        game_type, created = GameType.objects.get_or_create(
            name=game_data['name'],
            defaults={
                'display_name': game_data['display_name'],
                'description': game_data['description'],
                'rules': game_data['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created: {game_type.display_name}")
            created_count += 1
        else:
            # Update existing to ensure correct configuration
            game_type.display_name = game_data['display_name']
            game_type.description = game_data['description']
            game_type.rules = game_data['rules']
            game_type.is_active = True
            game_type.save()
            print(f"🔄 Updated: {game_type.display_name}")
            updated_count += 1
    
    print(f"\n📊 Game Types Summary:")
    print(f"   Created: {created_count}")
    print(f"   Updated: {updated_count}")
    print(f"   Total active: {GameType.objects.filter(is_active=True).count()}")
    
    return created_count, updated_count


def test_game_engine():
    """Test GameEngine with all games"""
    print("\n🔧 Testing Game Engine")
    print("=" * 25)
    
    engine = GameEngine()
    
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing {game_type.display_name}:")
        
        try:
            # Test initial state creation
            initial_state = engine.create_initial_state(game_type.name)
            print(f"   ✅ Initial state: {list(initial_state.keys())}")
            
            # Test if game class exists
            game_class = engine.get_game_class(game_type.name)
            if game_class:
                print(f"   ✅ Game class: {game_class.__name__}")
            else:
                print(f"   ❌ No game class found")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")


def test_ai_bot():
    """Test AI bot with all games"""
    print("\n🤖 Testing AI Bot")
    print("=" * 20)
    
    ai_bot = AIBot(difficulty='medium')
    
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing AI for {game_type.display_name}:")
        
        try:
            if game_type.name == 'color_match':
                test_state = {
                    'colors': ['red', 'blue', 'green'],
                    'sequence': ['red', 'blue'],
                    'player2_sequence': []
                }
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
            elif game_type.name == 'memory_card':
                test_state = {
                    'cards': ['🐶', '🐱', '🐶', '🐱'],
                    'revealed': [False, False, False, False],
                    'matched': [False, False, False, False],
                    'selected_cards': []
                }
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
            elif game_type.name in ['rock_paper_scissors', 'number_guessing', 'tic_tac_toe']:
                test_state = {'round': 1}
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")


def test_battle_creation():
    """Test actual battle creation for all games"""
    print("\n🧪 Testing Battle Creation")
    print("=" * 30)
    
    # Get test user
    test_user = User.objects.first()
    if not test_user:
        print("❌ No users found")
        return
    
    print(f"Testing with user: {test_user.username}")
    
    # Ensure user has tokens
    try:
        wallet = test_user.wallet
        if wallet.balance < 10:
            wallet.add_tokens(50, 'test', 'Test tokens for battle creation')
            print(f"Added test tokens, balance: {wallet.balance}")
    except Wallet.DoesNotExist:
        wallet = Wallet.objects.create(user=test_user)
        wallet.add_tokens(50, 'test', 'Test tokens for battle creation')
        print(f"Created wallet with test tokens: {wallet.balance}")
    
    # Test each game
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing {game_type.display_name}:")
        
        try:
            # Create battle
            battle = Battle.objects.create(
                game_type=game_type,
                player1=test_user,
                is_ai_battle=True,
                status='waiting'
            )
            
            print(f"   ✅ Battle created: {battle.id}")
            
            # Test initial game state
            from gaming.game_logic import GameEngine
            initial_state = GameEngine.create_initial_state(game_type.name)
            battle.game_state = initial_state
            battle.save()
            
            print(f"   ✅ Game state initialized")
            
            # Clean up
            battle.delete()
            print(f"   🗑️ Test battle cleaned up")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            import traceback
            traceback.print_exc()


def test_api_endpoints():
    """Test API endpoints for battle creation"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    client = Client()
    test_user = User.objects.first()
    
    if not test_user:
        print("❌ No users found")
        return
    
    client.force_login(test_user)
    print(f"Logged in as: {test_user.username}")
    
    # Test create-ai-battle endpoint
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing API for {game_type.display_name}:")
        
        try:
            response = client.post('/api/gaming/create-ai-battle/', 
                                 data=json.dumps({'game_type': game_type.name}),
                                 content_type='application/json')
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                response_data = json.loads(response.content.decode())
                print(f"   ✅ Success: Battle ID {response_data.get('battle_id', 'N/A')}")
                
                # Clean up
                if 'battle_id' in response_data:
                    try:
                        battle = Battle.objects.get(id=response_data['battle_id'])
                        battle.delete()
                        print(f"   🗑️ Cleaned up")
                    except:
                        pass
            else:
                error_content = response.content.decode()
                print(f"   ❌ Error: {error_content}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")


def main():
    """Main fix function"""
    print("🔧 Complete Fix for New Games")
    print("=" * 50)
    
    # Step 1: Fix game types
    created, updated = fix_game_types()
    
    # Step 2: Test game engine
    test_game_engine()
    
    # Step 3: Test AI bot
    test_ai_bot()
    
    # Step 4: Test battle creation
    test_battle_creation()
    
    # Step 5: Test API endpoints
    test_api_endpoints()
    
    print("\n🎉 Complete Fix Summary")
    print("=" * 30)
    
    total_games = GameType.objects.filter(is_active=True).count()
    print(f"✅ Total active games: {total_games}")
    print(f"✅ Game types fixed: {created + updated}")
    
    if total_games == 5:
        print("✅ All 5 games are properly configured")
        print("\n🎮 Available Games:")
        for game in GameType.objects.filter(is_active=True).order_by('name'):
            print(f"   • {game.display_name} ({game.name})")
        
        print("\n💡 Next steps:")
        print("1. Refresh your gaming dashboard")
        print("2. Try 'Play vs AI' on Color Match")
        print("3. Try 'Play vs AI' on Memory Card Match")
        print("4. Both should work without errors!")
    else:
        print(f"⚠️ Expected 5 games, found {total_games}")


if __name__ == '__main__':
    main()
