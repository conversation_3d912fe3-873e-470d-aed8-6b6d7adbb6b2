#!/usr/bin/env python
"""
Test script to verify checkout page handles tokens correctly
"""
import os
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from orders.models import Cart, CartItem
from products.models import Product
from wallet.models import Wallet
# from rest_framework.authtoken.models import Token

def test_checkout_with_tokens():
    print("🛒 Testing Checkout with Token Redemption...")
    print("=" * 60)
    
    # Create or get test user
    user, created = User.objects.get_or_create(
        username='checkout_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Checkout',
            'last_name': 'Test'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Create or get wallet with tokens
    wallet, created = Wallet.objects.get_or_create(user=user)
    if wallet.balance < 500:
        # Add some tokens for testing
        wallet.add_tokens(500, 'test_credit', 'Test tokens for checkout')
        print(f"✅ Added 500 tokens to wallet. Current balance: {wallet.balance}")
    else:
        print(f"✅ Wallet has {wallet.balance} tokens")
    
    # Create or get a test product
    product, created = Product.objects.get_or_create(
        name='Test Checkout Product',
        defaults={
            'description': 'Test product for checkout',
            'price': 100.00,
            'stock': 10,
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created test product: {product.name} - ₹{product.price}")
    else:
        print(f"✅ Using existing test product: {product.name} - ₹{product.price}")
    
    # Create or get cart and add product
    cart, created = Cart.objects.get_or_create(user=user)
    
    # Clear existing cart items
    cart.items.all().delete()
    
    # Add product to cart
    cart_item = CartItem.objects.create(
        cart=cart,
        product=product,
        quantity=2  # ₹200 total
    )
    print(f"✅ Added {cart_item.quantity}x {product.name} to cart")
    print(f"💰 Cart total: ₹{cart.total_price}")
    
    # We'll use Django test client which handles authentication automatically
    
    # Test 1: Calculate token redemption
    print("\n🧮 Test 1: Calculate Token Redemption")
    print("-" * 40)
    
    client = Client()
    client.force_login(user)
    
    # Test the calculate redemption endpoint
    response = client.post('/api/wallet/calculate-redemption/',
        data=json.dumps({'order_amount': float(cart.total_price)}),
        content_type='application/json')
    
    if response.status_code == 200:
        redemption_data = response.json()
        print(f"✅ Redemption calculation successful:")
        print(f"   💰 Available tokens: {redemption_data['available_tokens']}")
        print(f"   🎯 Redeemable tokens: {redemption_data['redeemable_tokens']}")
        print(f"   💵 Redeemable INR: ₹{redemption_data['redeemable_inr']}")
        print(f"   📊 Remaining amount: ₹{redemption_data['remaining_amount']}")
        print(f"   ⚙️ Token rate: ₹{redemption_data['token_rate']} per token")
        
        # Use 50 tokens for checkout test
        tokens_to_use = min(50, redemption_data['redeemable_tokens'])
        token_value = tokens_to_use * redemption_data['token_rate']
        final_amount = cart.total_price - token_value
        
        print(f"\n🎯 Using {tokens_to_use} tokens (₹{token_value}) for checkout")
        print(f"💰 Final amount to pay: ₹{final_amount}")
    else:
        print(f"❌ Redemption calculation failed: {response.status_code}")
        print(f"Response: {response.content}")
        return False
    
    # Test 2: Create order with token redemption
    print("\n🛒 Test 2: Create Order with Token Redemption")
    print("-" * 50)
    
    order_data = {
        'full_name': 'Test Checkout User',
        'email': '<EMAIL>',
        'phone': '9876543210',
        'address': '123 Test Street',
        'city': 'Test City',
        'state': 'Test State',
        'zipcode': '123456',
        'country': 'IN',
        'payment_method': 'razorpay',
        'notes': f'Test order with {tokens_to_use} tokens redeemed',
        
        # Token redemption data
        'tokens_used': tokens_to_use,
        'token_value': token_value,
        'final_amount': final_amount
    }
    
    # First redeem tokens
    redeem_response = client.post('/api/wallet/redeem/',
        data=json.dumps({
            'tokens_to_redeem': tokens_to_use,
            'order_id': 'test_order'
        }),
        content_type='application/json')
    
    if redeem_response.status_code == 200:
        redeem_data = redeem_response.json()
        print(f"✅ Token redemption successful:")
        print(f"   🎯 Tokens redeemed: {redeem_data['tokens_redeemed']}")
        print(f"   💵 INR value: ₹{redeem_data['inr_value']}")
        print(f"   💰 Remaining balance: {redeem_data['remaining_balance']} tokens")
    else:
        print(f"❌ Token redemption failed: {redeem_response.status_code}")
        print(f"Response: {redeem_response.content}")
        return False
    
    # Create order
    order_response = client.post('/api/orders/',
        data=json.dumps(order_data),
        content_type='application/json')
    
    if order_response.status_code == 201:
        order_data_response = order_response.json()
        print(f"✅ Order creation successful:")
        print(f"   🆔 Order ID: {order_data_response['id']}")
        print(f"   💰 Original total: ₹{order_data_response.get('original_total', 'N/A')}")
        print(f"   🎯 Tokens used: {order_data_response.get('tokens_used', 'N/A')}")
        print(f"   💵 Token value: ₹{order_data_response.get('token_value', 'N/A')}")
        print(f"   💳 Final amount: ₹{order_data_response.get('final_amount', 'N/A')}")
        
        # Check if Razorpay order was created
        if 'razorpay' in order_data_response:
            razorpay_data = order_data_response['razorpay']
            print(f"   🏦 Razorpay order ID: {razorpay_data['order_id']}")
            print(f"   💰 Razorpay amount: ₹{razorpay_data['amount'] / 100}")  # Convert paise to rupees
            print(f"   🔑 Razorpay key: {razorpay_data['key_id']}")
            
            # Verify that Razorpay amount matches final amount
            razorpay_amount_inr = razorpay_data['amount'] / 100
            if abs(razorpay_amount_inr - final_amount) < 0.01:  # Allow small floating point differences
                print(f"   ✅ Razorpay amount matches final amount after token discount")
            else:
                print(f"   ❌ Razorpay amount mismatch! Expected: ₹{final_amount}, Got: ₹{razorpay_amount_inr}")
        
        print(f"\n🎉 Checkout with tokens working perfectly!")
        return True
    else:
        print(f"❌ Order creation failed: {order_response.status_code}")
        print(f"Response: {order_response.content}")
        return False

if __name__ == '__main__':
    success = test_checkout_with_tokens()
    if success:
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! Checkout handles tokens correctly!")
        print("✅ Token calculation works")
        print("✅ Token redemption works") 
        print("✅ Order creation with tokens works")
        print("✅ Razorpay integration with discounted amount works")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ TESTS FAILED! There are issues with token handling")
        print("=" * 60)
