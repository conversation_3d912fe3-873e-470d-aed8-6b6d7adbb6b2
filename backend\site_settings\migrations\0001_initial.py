# Generated by Django 5.0.2 on 2025-05-08 13:18

import site_settings.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_logo', models.ImageField(blank=True, help_text='Site logo image (max 2MB, 1200x1200)', null=True, upload_to=site_settings.models.logo_upload_path, validators=[site_settings.models.validate_image])),
                ('favicon', models.ImageField(blank=True, help_text='Site favicon (max 2MB, recommended 32x32 or 64x64)', null=True, upload_to=site_settings.models.favicon_upload_path, validators=[site_settings.models.validate_image])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Site Settings',
                'verbose_name_plural': 'Site Settings',
            },
        ),
    ]
