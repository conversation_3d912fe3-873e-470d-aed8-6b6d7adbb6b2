# Generated by Django 5.0.2 on 2025-05-08 06:14

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OrderTracking',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('order_id', models.UUIDField(help_text='The ID of the order in our system')),
                ('printify_order_id', models.CharField(blank=True, help_text='The ID of the order in Printify', max_length=255, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('shipped', 'Shipped'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='pending', max_length=50)),
                ('tracking_number', models.CharField(blank=True, max_length=100, null=True)),
                ('carrier', models.CharField(blank=True, max_length=100, null=True)),
                ('carrier_link', models.URLField(blank=True, help_text="Link to the carrier's tracking page", null=True)),
                ('estimated_delivery', models.DateField(blank=True, null=True)),
                ('last_update', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_tracking', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Order Tracking',
                'verbose_name_plural': 'Order Tracking',
                'ordering': ['-last_update'],
                'unique_together': {('order_id', 'printify_order_id')},
            },
        ),
    ]
