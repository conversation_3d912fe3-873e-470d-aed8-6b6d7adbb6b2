# Generated manually to definitively fix ProductImage model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0013_simplify_productimage'),
    ]

    operations = [
        # Use RunSQL to ensure the table structure is correct
        migrations.RunSQL(
            # Forward SQL - ensure the table has the correct structure
            """
            -- Ensure image_url column exists
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns 
                    WHERE table_name = 'products_productimage' 
                    AND column_name = 'image_url'
                ) THEN
                    ALTER TABLE products_productimage ADD COLUMN image_url VARCHAR(200);
                END IF;
            END $$;
            
            -- Ensure image column allows NULL
            ALTER TABLE products_productimage ALTER COLUMN image DROP NOT NULL;
            """,
            
            # Reverse SQL - for rollback (optional)
            """
            -- This is a no-op for safety
            SELECT 1;
            """
        ),
        
        # Ensure the model fields are correctly defined
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='products/'),
        ),
        
        migrations.AlterField(
            model_name='productimage',
            name='image_url',
            field=models.URLField(blank=True, null=True, help_text='URL for the image (used for display only)'),
        ),
    ]
