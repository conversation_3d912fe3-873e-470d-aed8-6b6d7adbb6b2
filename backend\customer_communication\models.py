from django.db import models
from django.conf import settings
from django.utils import timezone
from django.contrib.auth import get_user_model
from orders.models import Order

User = get_user_model()

class SupportTicket(models.Model):
    """
    Model for storing customer support tickets.
    """
    STATUS_CHOICES = (
        ('new', 'New'),
        ('in_progress', 'In Progress'),
        ('waiting', 'Waiting for Customer'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    )

    PRIORITY_CHOICES = (
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    )

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='support_tickets'
    )
    order = models.ForeignKey(
        Order,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='support_tickets'
    )
    name = models.CharField(max_length=100)
    email = models.EmailField()
    subject = models.Char<PERSON><PERSON>(max_length=200)
    message = models.TextField()
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='new'
    )
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='medium'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Support Ticket'
        verbose_name_plural = 'Support Tickets'

    def __str__(self):
        return f"{self.name} - {self.subject} ({self.get_status_display()})"


class TicketResponse(models.Model):
    """
    Model for storing responses to support tickets.
    """
    ticket = models.ForeignKey(
        SupportTicket,
        on_delete=models.CASCADE,
        related_name='responses'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='ticket_responses'
    )
    is_staff = models.BooleanField(default=False)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']
        verbose_name = 'Ticket Response'
        verbose_name_plural = 'Ticket Responses'

    def __str__(self):
        return f"Response to {self.ticket.subject} by {'Staff' if self.is_staff else 'Customer'}"


class EmailTemplate(models.Model):
    """
    Model for storing email templates.
    """
    TEMPLATE_TYPES = (
        ('order_confirmation', 'Order Confirmation'),
        ('feedback_request', 'Feedback Request'),
        ('support_ticket_confirmation', 'Support Ticket Confirmation'),
        ('support_ticket_response', 'Support Ticket Response'),
        ('custom', 'Custom Template'),
    )

    name = models.CharField(max_length=100)
    template_type = models.CharField(
        max_length=50,
        choices=TEMPLATE_TYPES,
        default='custom'
    )
    subject = models.CharField(max_length=200)
    html_content = models.TextField(
        help_text="HTML content of the email. Use {{ variable }} for dynamic content."
    )
    text_content = models.TextField(
        help_text="Plain text content of the email. Use {{ variable }} for dynamic content."
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Email Template'
        verbose_name_plural = 'Email Templates'

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


class CustomerFeedback(models.Model):
    """
    Model for storing customer feedback.
    """
    RATING_CHOICES = (
        (1, '1 - Very Dissatisfied'),
        (2, '2 - Dissatisfied'),
        (3, '3 - Neutral'),
        (4, '4 - Satisfied'),
        (5, '5 - Very Satisfied'),
    )

    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='feedback'
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='feedback'
    )
    rating = models.PositiveSmallIntegerField(choices=RATING_CHOICES)
    comment = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Customer Feedback'
        verbose_name_plural = 'Customer Feedback'
        unique_together = ['order', 'user']

    def __str__(self):
        return f"Feedback for Order {self.order.id} - Rating: {self.rating}"
