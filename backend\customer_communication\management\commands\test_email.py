from django.core.management.base import BaseCommand
from orders.models import Order
from customer_communication.tasks import send_order_confirmation_email, send_support_ticket_confirmation
from customer_communication.models import SupportTicket

class Command(BaseCommand):
    help = 'Test email functionality'

    def add_arguments(self, parser):
        parser.add_argument('--type', type=str, help='Type of email to test (order or support)')

    def handle(self, *args, **options):
        email_type = options.get('type', 'order')
        
        if email_type == 'order':
            # Test order confirmation email
            order = Order.objects.first()
            if order:
                self.stdout.write(self.style.SUCCESS(f'Using order: {order.id}'))
                send_order_confirmation_email.delay(str(order.id))
                self.stdout.write(self.style.SUCCESS('Order confirmation email task scheduled'))
            else:
                self.stdout.write(self.style.ERROR('No orders found'))
        
        elif email_type == 'support':
            # Test support ticket confirmation email
            ticket = SupportTicket.objects.first()
            if ticket:
                self.stdout.write(self.style.SUCCESS(f'Using support ticket: {ticket.id}'))
                send_support_ticket_confirmation.delay(ticket.id)
                self.stdout.write(self.style.SUCCESS('Support ticket confirmation email task scheduled'))
            else:
                self.stdout.write(self.style.ERROR('No support tickets found'))
        
        else:
            self.stdout.write(self.style.ERROR(f'Unknown email type: {email_type}'))
