from django.shortcuts import render
from django.http import HttpResponse
from django.views.decorators.http import require_GET
from rest_framework import viewsets, permissions
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from .models import SiteSettings
from .serializers import SiteSettingsSerializer

@require_GET
def test_template_view(request):
    """
    A simple view to test the base.html template with site settings.
    """
    return render(request, 'test_page.html', {
        'title': 'Site Settings Test',
    })

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def site_settings_api(request):
    """
    API endpoint to get site settings.

    Returns the site logo and favicon URLs.
    """
    settings = SiteSettings.objects.first()
    if settings:
        serializer = SiteSettingsSerializer(settings, context={'request': request})
        return Response(serializer.data)
    return Response({
        'logo_url': None,
        'favicon_url': None
    })
