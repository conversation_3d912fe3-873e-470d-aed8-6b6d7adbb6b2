"""
Django settings for dropshipping_backend project.

Generated by 'django-admin startproject' using Django 5.0.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import os
from datetime import timedelta

# Try to import dj_database_url, but don't fail if it's not installed
try:
    import dj_database_url
except ImportError:
    dj_database_url = None

# Try to import dotenv, but don't fail if it's not installed
try:
    from dotenv import load_dotenv
    # Load environment variables from .env.local file for local development
    env_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"Loaded environment variables from {env_file}")
    else:
        # Load environment variables from .env file
        load_dotenv()
except ImportError:
    pass

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-4uxtr!n01iev9u7in9awn-(p0i52$dui8gj_t48ucf^dqeywau')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'

ALLOWED_HOSTS = [
    'localhost',
    '127.0.0.1',
    'testserver',  # For testing
    '.onrender.com',
    'pickmetrend.com',
    'www.pickmetrend.com',
    '*.pickmetrend.com',
    '.elasticbeanstalk.com',
    '.amazonaws.com'
]


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Third-party apps
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'corsheaders',
    'djoser',

    # Local apps
    'accounts',
    'products',
    'orders',
    'returns',
    'contact',
    'printify',
    'razorpay_settings',
    'order_tracking',
    'site_settings',
    'team_members',
    'customer_communication',
    'service_control',
    'gaming',
    'wallet',
]

# Add optional apps if available (only in production)
if not DEBUG:  # Only add Celery apps in production
    try:
        import django_celery_beat
        INSTALLED_APPS.append('django_celery_beat')
        print("Added django_celery_beat to INSTALLED_APPS")
    except ImportError:
        pass

    try:
        import django_celery_results
        INSTALLED_APPS.append('django_celery_results')
        print("Added django_celery_results to INSTALLED_APPS")
    except ImportError:
        pass
else:
    print("Development mode: Skipping Celery apps")

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'dropshipping_backend.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'site_settings.context_processors.site_settings',
                'team_members.context_processors.team_members',
            ],
        },
    },
]

WSGI_APPLICATION = 'dropshipping_backend.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

# Database configuration
# Default to PostgreSQL for AWS deployment
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME', 'pickmetrend'),
        'USER': os.environ.get('DB_USER', 'postgres'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
        # Enable connection pooling
        'CONN_MAX_AGE': 600,
    }
}

# Use SQLite for local development if specified
USE_SQLITE = os.environ.get('USE_SQLITE', 'false').lower() == 'true'
if USE_SQLITE:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
    print("Using SQLite database for development")
else:
    print("Using PostgreSQL database")

# For backwards compatibility, still support DATABASE_URL if provided
DATABASE_URL = os.environ.get('DATABASE_URL')
if DATABASE_URL and dj_database_url is not None:
    try:
        # Parse the URL and update the default database configuration
        db_config = dj_database_url.parse(DATABASE_URL)
        DATABASES['default'].update(db_config)
        print(f"Using database from DATABASE_URL: {db_config.get('ENGINE')}")
    except Exception as e:
        print(f"Error parsing DATABASE_URL: {e}")


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Cloudinary Configuration
# Get Cloudinary credentials from environment variables
CLOUDINARY_STORAGE = {
    'CLOUD_NAME': os.environ.get('CLOUDINARY_CLOUD_NAME', 'dxn3vh6uz'),
    'API_KEY': os.environ.get('CLOUDINARY_API_KEY', '888158425671185'),
    'API_SECRET': os.environ.get('CLOUDINARY_API_SECRET', 'vJKmIRGacRcq5XkBBjhBDBrB_BE'),
}

# Configure Cloudinary if available and properly configured
USE_CLOUDINARY = os.environ.get('USE_CLOUDINARY', 'true').lower() == 'true'
CLOUDINARY_CLOUD_NAME = CLOUDINARY_STORAGE.get('CLOUD_NAME', '')

if USE_CLOUDINARY and CLOUDINARY_CLOUD_NAME:
    try:
        import cloudinary
        import cloudinary.uploader
        import cloudinary.api

        cloudinary.config(
            cloud_name=CLOUDINARY_STORAGE['CLOUD_NAME'],
            api_key=CLOUDINARY_STORAGE['API_KEY'],
            api_secret=CLOUDINARY_STORAGE['API_SECRET'],
            secure=True
        )

        # Test the configuration
        try:
            cloudinary.api.ping()
            # Use Cloudinary for media file storage
            DEFAULT_FILE_STORAGE = 'cloudinary_storage.storage.MediaCloudinaryStorage'
            print(f"✅ Cloudinary configured for media storage with cloud: {CLOUDINARY_CLOUD_NAME}")
        except Exception as e:
            print(f"❌ Cloudinary configuration test failed: {e}")
            print("⚠️ Falling back to default file storage")
            USE_CLOUDINARY = False

    except ImportError:
        print("⚠️ Cloudinary not installed, using default file storage")
        USE_CLOUDINARY = False
else:
    print("⚠️ Cloudinary disabled or cloud name not configured, using default file storage")
    USE_CLOUDINARY = False

# For production deployment
if not DEBUG:
    # Check if we should serve media files via Django in production (for Render)
    SERVE_MEDIA_IN_PRODUCTION = os.getenv('SERVE_MEDIA_IN_PRODUCTION', 'true').lower() == 'true'

    if SERVE_MEDIA_IN_PRODUCTION:
        print("Serving media files via Django in production (Render deployment)")
        # Use whitenoise for static file serving only
        MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
        STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
        # Media files will be served by Django URLs (configured in urls.py)
    else:
        # Check if we're using AWS S3 for storage
        USE_S3 = os.environ.get('USE_S3', 'false').lower() == 'true'

        if USE_S3:
            # AWS S3 settings
            AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID', '')
            AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY', '')
            AWS_STORAGE_BUCKET_NAME = os.environ.get('AWS_STORAGE_BUCKET_NAME', 'pickmetrend-media')
            AWS_S3_REGION_NAME = os.environ.get('AWS_S3_REGION_NAME', 'us-east-1')
            AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'
            AWS_S3_OBJECT_PARAMETERS = {
                'CacheControl': 'max-age=86400',
            }

            # Static files settings
            STATICFILES_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
            STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/static/'

            # Media files settings
            DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
            MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/media/'

            print("Using AWS S3 for static and media files")
        else:
            # Use whitenoise for static file serving
            MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
            STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
            print("Using whitenoise for static files only")

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5 MB
FILE_UPLOAD_PERMISSIONS = 0o644

# Supported image file types
SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/svg+xml'
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "https://pickmetrend.up.railway.app",
    "https://*.railway.app",
    "https://pickmetrend-frontend-cwie66ow1-phinihas-projects-6aed2c47.vercel.app",
    "https://pickmetrend-frontend1.vercel.app",
    "https://pickmetrend.com",
    "https://www.pickmetrend.com",
    "https://*.pickmetrend.com",
    "https://*.elasticbeanstalk.com",
    "https://*.amazonaws.com",
    "https://*.onrender.com",
    "https://pickmetrend-frontend.onrender.com"
]

# In development, allow all origins
CORS_ALLOW_ALL_ORIGINS = DEBUG
CORS_ALLOW_CREDENTIALS = True
CORS_EXPOSE_HEADERS = ['Content-Type', 'X-CSRFToken']

# Security settings for production
if not DEBUG:
    # HTTPS settings
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

    # Security headers
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'

    # HSTS settings (for custom domain)
    SECURE_HSTS_SECONDS = 31536000  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True

    # Session security
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticatedOrReadOnly',  # Allow read access to unauthenticated users
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
}

# JWT settings
SIMPLE_JWT = {
    'AUTH_HEADER_TYPES': ('Bearer', 'JWT'),  # Support both Bearer and JWT
    'ACCESS_TOKEN_LIFETIME': timedelta(days=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
}

# Djoser settings
DJOSER = {
    'LOGIN_FIELD': 'username',  # Changed back to username for compatibility
    'USER_CREATE_PASSWORD_RETYPE': True,
    'USERNAME_CHANGED_EMAIL_CONFIRMATION': False,  # Changed to False for development
    'PASSWORD_CHANGED_EMAIL_CONFIRMATION': False,  # Changed to False for development
    'SEND_CONFIRMATION_EMAIL': False,  # Changed to False for development
    'SET_USERNAME_RETYPE': True,
    'SET_PASSWORD_RETYPE': True,
    'PASSWORD_RESET_CONFIRM_URL': 'password/reset/confirm/{uid}/{token}',
    'USERNAME_RESET_CONFIRM_URL': 'email/reset/confirm/{uid}/{token}',
    'ACTIVATION_URL': 'activate/{uid}/{token}',
    'SEND_ACTIVATION_EMAIL': False,  # Changed to False for development
    'SERIALIZERS': {
        'user_create': 'accounts.serializers.UserCreateSerializer',
        'user': 'accounts.serializers.UserSerializer',
        'current_user': 'accounts.serializers.UserSerializer',
        'user_delete': 'djoser.serializers.UserDeleteSerializer',
    },
}

# Email settings (for development)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'
ADMIN_EMAIL = '<EMAIL>'  # Change this to your actual admin email

# Razorpay settings
# Use environment variables for production, fallback to test keys for development
if DEBUG:
    # Development/Test keys
    RAZORPAY_KEY_ID = os.environ.get('RAZORPAY_KEY_ID', 'rzp_test_RFROlcqlSc1bpM')
    RAZORPAY_KEY_SECRET = os.environ.get('RAZORPAY_KEY_SECRET', 'zLwVXZzKgDPsWn5W9fNskYDZ')
else:
    # Production/Live keys - MUST be set in environment variables
    RAZORPAY_KEY_ID = os.environ.get('RAZORPAY_KEY_ID', '***********************')
    RAZORPAY_KEY_SECRET = os.environ.get('RAZORPAY_KEY_SECRET', 'y0CXtCz5Cdfet2PcH3tXetia')

RAZORPAY_WEBHOOK_SECRET = os.environ.get('RAZORPAY_WEBHOOK_SECRET', '')  # Optional, for webhook verification

# Print Razorpay keys for debugging
print(f"Using Razorpay Key ID: {RAZORPAY_KEY_ID}")
print(f"Using Razorpay Key Secret: {'*' * len(RAZORPAY_KEY_SECRET)}")

# IMPORTANT: Live keys are now configured for production deployment

# Printify API settings
PRINTIFY_API_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UYnB2ZwEQy6s4CTNTV6SrKz1iInAxJnS3OEKl7i2C0TvsGnyfZ3Wy1XBeWZJ-6RFqIksudNXJPwnS1fnTaR9oaaPFH802SviDpbi2rOhoAWk5F5jRR_mX-uhw4c0KmkD1u9o3Z3hquMR7MiokHggNNFhw2uQzpgi9J-A77m5feBg3JQjM5cXEHlHICKsSfZnrKoMxO5NUiR7lE7qx8Pyt9xk57-DmJJybSjyFBkKXPwK1o8ZZDdqKi6c2-FqQRk_G49uH4yyHcpunWQxBFbaCQvPLIMCfmdVH4wBcp70PE8gOcA6H4F1bL8TkPFSK8y4b8yPNc94o1bSq-nxiHbdolmEs2wXpPpXa3OP0rXFBk5ASkpI6GWxudauwaSMIJkMxyzYpc_77_nrw7Mga6D2pif9RBOXxdnxF7KJSVwnVQgxuDxCM99BBu4h2GIhbAcMy2z7xoHIl1XArM032ufBLo3KXmFaXval-1Kew-23LdW9G1FQjpql4kpzJmR-uGoznp0eUHgoLQK2TM6iA1rP4mEqHF0OdcDSJ1x3u0fmMxYDPgN7YeGI7DkDqYE-dmywbvYbHj0wwz7wr9Ve-TkwnvRFKQhL5tpNVNOwp-T15GKphWDx1JdKo8m_KOzhGKDZKrX1IiuPHEIAYhdU9UmXlpPlUQE25B2EJeHEJmEDr8o'
PRINTIFY_API_BASE_URL = 'https://api.printify.com/v1'
PRINTIFY_SHOP_ID = '22150635'  # The shop ID for www.pickmetrend.com

# Print Printify API token for debugging
print(f"Using Printify API Token: {'*' * 10}")

# Increase the maximum number of fields allowed in a form
# This is needed for products with many variants
DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000  # Default is 1000

# Redis Configuration for Gaming and Celery
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

# Configure Redis connection with TLS support for Upstash
def get_redis_connection():
    """Get Redis connection with proper TLS configuration for Upstash"""
    import redis

    # Parse Redis URL
    if 'upstash.io' in REDIS_URL:
        # Upstash Redis requires TLS - use connection_kwargs for SSL settings
        return redis.Redis.from_url(
            REDIS_URL,
            decode_responses=True,
            ssl_cert_reqs=None,  # Don't verify SSL certificates
            ssl_check_hostname=False,
            ssl_ca_certs=None
        )
    else:
        # Local or other Redis
        return redis.Redis.from_url(REDIS_URL, decode_responses=True)

# Check if Redis is available
REDIS_AVAILABLE = False
if not DEBUG:  # Only try Redis in production
    try:
        # Validate Redis URL format first
        if REDIS_URL and ('redis://' in REDIS_URL or 'rediss://' in REDIS_URL):
            r = get_redis_connection()
            r.ping()
            REDIS_AVAILABLE = True
            print("✅ Redis connection successful (Upstash)")
        else:
            print(f"❌ Invalid Redis URL format: {REDIS_URL}")
            print("Using dummy Celery")
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("Using dummy Celery")
else:
    print("Development mode: using dummy Celery")

if REDIS_AVAILABLE:
    CELERY_BROKER_URL = REDIS_URL
    # Only configure Celery if it's installed
    try:
        import celery
        CELERY_RESULT_BACKEND = 'django-db'
        CELERY_ACCEPT_CONTENT = ['json']
        CELERY_TASK_SERIALIZER = 'json'
        CELERY_RESULT_SERIALIZER = 'json'
        CELERY_TIMEZONE = TIME_ZONE
        CELERY_TASK_TRACK_STARTED = True
        CELERY_TASK_TIME_LIMIT = 30 * 60  # 30 minutes
        # Django Celery Beat Configuration
        CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
    except ImportError:
        # Celery is not installed, so we don't need to configure it
        pass

    # Django Channels Configuration for WebSocket Gaming
    try:
        import channels
        CHANNEL_LAYERS = {
            "default": {
                "BACKEND": "channels_redis.core.RedisChannelLayer",
                "CONFIG": {
                    "hosts": [REDIS_URL],
                    "symmetric_encryption_keys": [SECRET_KEY],
                },
            },
        }
        print("✅ Django Channels configured with Upstash Redis")
    except ImportError:
        print("⚠️ Django Channels not installed - WebSocket gaming disabled")
        CHANNEL_LAYERS = {
            "default": {
                "BACKEND": "channels.layers.InMemoryChannelLayer"
            }
        }
else:
    # Use dummy task queue for development
    CELERY_TASK_ALWAYS_EAGER = True
    CELERY_TASK_EAGER_PROPAGATES = True

    # In-memory channel layer for development
    CHANNEL_LAYERS = {
        "default": {
            "BACKEND": "channels.layers.InMemoryChannelLayer"
        }
    }

# Number of days after an order to send feedback request
CUSTOMER_FEEDBACK_DAYS = 7

# Customer Communication Settings
CUSTOMER_FEEDBACK_DAYS = 7  # Days after order to send feedback email
SUPPORT_EMAIL = '<EMAIL>'  # Email for support tickets
ADMIN_EMAIL = '<EMAIL>'  # Admin email for notifications
# Site URL for email links
if DEBUG:
    SITE_URL = 'http://localhost:8000'
else:
    # For deployment, prioritize custom domain
    SITE_URL = os.environ.get('SITE_URL', 'https://www.pickmetrend.com')

    # Fallback to deployment platform URLs if custom domain not set
    if SITE_URL == 'https://www.pickmetrend.com':
        # Check if we have deployment platform URLs
        platform_url = (os.environ.get('RAILWAY_STATIC_URL') or
                       os.environ.get('RENDER_EXTERNAL_URL'))
        if platform_url:
            SITE_URL = platform_url

    # Make sure the URL has https:// prefix
    if not SITE_URL.startswith('http'):
        SITE_URL = f'https://{SITE_URL}'

# Email Configuration
import os

# For development, use console backend
if DEBUG:
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
# For production, use SendGrid
else:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = 'smtp.sendgrid.net'
    EMAIL_PORT = 587
    EMAIL_USE_TLS = True
    EMAIL_HOST_USER = 'apikey'  # This is literally the string 'apikey'
    # Get SendGrid API key from environment variable
    EMAIL_HOST_PASSWORD = os.environ.get('SENDGRID_API_KEY', '')

DEFAULT_FROM_EMAIL = 'PickMeTrend <<EMAIL>>'  # This format works with SendGrid

# Gaming system settings - STANDARDIZED TOKEN RULES
GAMING_SETTINGS = {
    'AI_BOT_DIFFICULTY': 'hard',  # Options: 'easy', 'medium', 'hard'
    'TOKEN_REWARD_WIN': 5,        # Win: +5 tokens (standardized)
    'TOKEN_REWARD_DRAW': 2,       # Draw: +2 tokens (standardized)
    'TOKEN_REWARD_PARTICIPATION': 2,  # Participate: +2 tokens (standardized)
    'TOKEN_PENALTY_LOSS': 1,      # Loss: -1 token (standardized)
    'SIGNUP_BONUS': 200,          # Signup bonus: 200 tokens (increased)
    'MATCHMAKING_TIMEOUT_SECONDS': 60,
}

# Wallet system settings
WALLET_SETTINGS = {
    'TOKEN_TO_INR_RATE': 0.1,  # 1 token = 0.1 INR (example rate)
}

