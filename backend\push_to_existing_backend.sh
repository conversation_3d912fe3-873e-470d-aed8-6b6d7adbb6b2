#!/bin/bash

# 🚀 Push Backend to Existing Repository
# Pushes backend code to https://github.com/phinihas30/pickmetrendofficial-render.git

echo "🚀 Pushing Backend to Existing Repository"
echo "========================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "backend" ]; then
    print_error "Backend directory not found"
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Preparing backend for existing repository..."

# Navigate to backend directory
cd backend

# Check if git is already initialized
if [ -d ".git" ]; then
    print_warning "Git repository already exists in backend"
    print_status "Checking remote configuration..."
    
    # Check if remote already exists
    if git remote get-url origin >/dev/null 2>&1; then
        current_remote=$(git remote get-url origin)
        print_status "Current remote: $current_remote"
        
        if [ "$current_remote" != "https://github.com/phinihas30/pickmetrendofficial-render.git" ]; then
            print_status "Updating remote to correct repository..."
            git remote set-url origin https://github.com/phinihas30/pickmetrendofficial-render.git
            print_success "Remote updated"
        else
            print_success "Remote already correctly configured"
        fi
    else
        print_status "Adding remote repository..."
        git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git
        print_success "Remote added"
    fi
else
    print_status "Initializing git repository..."
    git init
    print_success "Git repository initialized"
    
    print_status "Adding remote repository..."
    git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git
    print_success "Remote added"
fi

# Create/update .gitignore
print_status "Creating/updating .gitignore..."
cat > .gitignore << 'EOF'
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
backups/

# Temporary files
*.tmp
*.temp

# Development files
test_*.py
debug_*.py
simple_*.py
check_*.py
fix_*.py
analyze_*.py
migrate_*.py
sync_*.py
update_*.py
init_*.py
setup_*.py
EOF

print_success ".gitignore created/updated"

# Add all files
print_status "Adding all backend files..."
git add .
print_success "Files added to git"

# Check if there are changes to commit
if git diff --staged --quiet; then
    print_warning "No changes to commit"
else
    # Create commit with gaming system details
    print_status "Creating commit with gaming system updates..."
    git commit -m "🎮 Gaming System Complete - Production Ready

✨ New Features:
- Tic Tac Toe game with hard mode AI (minimax algorithm)
- Token economy: Win +5, Draw +2, Loss -1 tokens
- Real-time gaming with WebSocket support
- Wallet system with shopping integration
- Complete e-commerce API with Printify integration

🔧 Technical Updates:
- Django REST API with gaming endpoints
- Redis integration with Upstash for production
- Token transaction system with database persistence
- Razorpay live payment integration
- Production-ready settings and deployment config

🎮 Gaming API Endpoints:
- POST /api/gaming/tic-tac-toe/start/
- POST /api/gaming/tic-tac-toe/complete/
- GET /api/gaming/tic-tac-toe/stats/

💰 Token System:
- Real token rewards for gameplay
- Shopping discount integration
- Secure transaction validation
- Admin panel for monitoring

🚀 Production Ready:
- Render.com deployment configuration
- Upstash Redis for gaming/caching
- PostgreSQL database
- Cloudinary media storage
- SendGrid email integration
- Security hardening and CORS setup

Ready for deployment! 🎮🛒🚀"

    print_success "Commit created with gaming system details"
fi

# Push to repository
print_status "Pushing to GitHub repository..."
git branch -M main

# Check if we need to pull first
if git ls-remote --heads origin main | grep -q main; then
    print_status "Remote main branch exists, pulling latest changes..."
    git pull origin main --allow-unrelated-histories || {
        print_warning "Pull failed, force pushing instead..."
        git push -f origin main
    }
else
    print_status "Remote main branch doesn't exist, creating it..."
fi

git push -u origin main

if [ $? -eq 0 ]; then
    print_success "Successfully pushed to GitHub!"
else
    print_error "Push failed. You may need to resolve conflicts manually."
    exit 1
fi

# Go back to project root
cd ..

print_success "Backend deployment complete!"
echo ""
echo "🎉 Your backend is now live at:"
echo "📁 Repository: https://github.com/phinihas30/pickmetrendofficial-render"
echo ""
echo "🚀 Next Steps:"
echo "=============="
echo ""
echo "1. 🌐 Deploy to Render:"
echo "   - Login to https://render.com"
echo "   - Create new Blueprint"
echo "   - Connect your repository"
echo "   - Deploy automatically with render.yaml"
echo ""
echo "2. 🎮 Gaming System Features:"
echo "   - Tic Tac Toe with hard mode AI"
echo "   - Token rewards: Win +5, Draw +2, Loss -1"
echo "   - Real-time WebSocket gaming"
echo "   - Wallet integration for shopping"
echo ""
echo "3. 🛒 E-commerce Features:"
echo "   - Complete product API"
echo "   - Shopping cart with variants"
echo "   - Razorpay payment integration"
echo "   - Order management system"
echo ""
echo "4. 📱 Create Frontend Repository:"
echo "   - Create new repository for React frontend"
echo "   - Push frontend code separately"
echo "   - Deploy as static site on Render"
echo ""
print_success "Your gaming e-commerce backend is ready for production! 🎮🛒🚀"
