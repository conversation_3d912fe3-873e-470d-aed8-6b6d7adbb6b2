"""
URL configuration for dropshipping_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from orders import views as orders_views
from printify.health_check import printify_health_check, printify_detailed_status
from products import debug_views

def home_view(request):
    """Simple view to handle the root URL"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>PickMeTrend API</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }
            h1 {
                color: #333;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            }
            ul {
                list-style-type: none;
                padding: 0;
            }
            li {
                margin-bottom: 10px;
            }
            a {
                color: #0066cc;
                text-decoration: none;
            }
            a:hover {
                text-decoration: underline;
            }
        </style>
    </head>
    <body>
        <h1>PickMeTrend API</h1>
        <p>Welcome to the PickMeTrend API. This is the backend server for the PickMeTrend dropshipping application.</p>

        <h2>Available Endpoints:</h2>
        <ul>
            <li><a href="/api/">/api/</a> - API Root</li>
            <li><a href="/admin/">/admin/</a> - Admin Interface</li>
            <li><a href="/api/products/">/api/products/</a> - Products API</li>
            <li><a href="/api/orders/">/api/orders/</a> - Orders API</li>
            <li><a href="/api/printify/">/api/printify/</a> - Printify API</li>
            <li><a href="/api/health/printify/">/api/health/printify/</a> - Printify Health Check</li>
            <li><a href="/api/health/printify/detailed/">/api/health/printify/detailed/</a> - Detailed Status</li>
        </ul>

        <p>Note: This is the backend API server. The frontend application needs to be run separately.</p>
    </body>
    </html>
    """
    return HttpResponse(html)

class APIRootView(APIView):
    permission_classes = [AllowAny]
    def get(self, request):
        return Response({
            "products": "/api/products/",
            "orders": "/api/orders/",
            "returns": "/api/returns/",
            "auth": "/api/auth/",
            "accounts": "/api/accounts/",
            "printify": "/api/printify/",
            "order_tracking": "/api/order_tracking/",
            "service_control": "/api/service-control/"
        })

urlpatterns = [
    # Root URL - simple welcome page
    path('', home_view, name='home'),

    # API endpoints
    path('api/', APIRootView.as_view(), name='api-root'),
    path('admin/', admin.site.urls),
    path('api/auth/', include('djoser.urls')),
    path('api/auth/', include('djoser.urls.jwt')),
    path('api/products/', include('products.urls')),
    path('api/orders/', include('orders.urls')),
    path('api/returns/', include('returns.urls')),
    path('api/accounts/', include('accounts.urls')),
    path('api/contact/', include('contact.urls')),
    path('api/printify/', include('printify.urls')),
    path('api/order_tracking/', include(('order_tracking.urls', 'order_tracking'), namespace='order-tracking-api')),
    path('order_tracking/', include('order_tracking.urls')),
    path('site-settings/', include('site_settings.urls')),
    path('team/', include('team_members.urls')),
    path('customer/', include('customer_communication.urls')),

    # Service Control API endpoints
    path('api/service-control/', include(('service_control.urls', 'service_control_api'), namespace='service_control_api')),
    path('service-control/', include(('service_control.urls', 'service_control_app'), namespace='service_control_app')),

    # Health check endpoints
    path('api/health/printify/', printify_health_check, name='printify_health_check'),
    path('api/health/printify/detailed/', printify_detailed_status, name='printify_detailed_status'),

    # Razorpay test page - direct path to avoid authentication middleware
    path('razorpay/test/', orders_views.razorpay_test_view, name='razorpay_test'),
    path('razorpay/test/create_test_order/', orders_views.create_test_order, name='create_test_order'),
    path('razorpay/test/verify_test_payment/', orders_views.verify_test_payment, name='verify_test_payment'),

    # Gaming and Wallet API endpoints
    path('api/gaming/', include('gaming.urls')),
    path('api/wallet/', include('wallet.urls')),

    # Debug views for production troubleshooting
    path('debug/product/<uuid:product_id>/', include('products.debug_urls')),
    path('debug/system/', debug_views.debug_system_info, name='debug_system_info'),
]

# Serve media files in development and in production if SERVE_MEDIA_IN_PRODUCTION is set
if settings.DEBUG or getattr(settings, 'SERVE_MEDIA_IN_PRODUCTION', False):
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
