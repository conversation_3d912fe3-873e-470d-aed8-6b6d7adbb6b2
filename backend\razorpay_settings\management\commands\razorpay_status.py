"""
Management command to display the current Razorpay mode and keys.
"""
from django.core.management.base import BaseCommand
from django.utils.termcolors import colorize
from razorpay_settings.models import RazorpaySettings
from razorpay_settings.utils import get_razorpay_keys, get_razorpay_mode


class Command(BaseCommand):
    help = 'Display the current Razorpay mode (Test or Live) and keys'

    def add_arguments(self, parser):
        parser.add_argument(
            '--show-secrets',
            action='store_true',
            help='Show the full key secrets (use with caution)',
        )

    def handle(self, *args, **options):
        show_secrets = options.get('show_secrets', False)
        
        # Get the RazorpaySettings instance
        razorpay_settings = RazorpaySettings.objects.first()
        
        if not razorpay_settings:
            self.stdout.write(self.style.WARNING(
                'No Razorpay settings found. Please create one in the admin panel.'
            ))
            return
        
        # Get the current mode and keys
        mode = get_razorpay_mode()
        key_id, key_secret = get_razorpay_keys()
        
        # Display the mode with appropriate color
        if mode == "Live":
            mode_display = colorize(f"LIVE MODE", fg="red")
            warning = colorize("WARNING: Live mode is active! Real transactions will be processed.", fg="red")
            self.stdout.write(self.style.WARNING(f"{mode_display} - {warning}"))
        else:
            mode_display = colorize(f"TEST MODE", fg="green")
            self.stdout.write(self.style.SUCCESS(f"{mode_display} - Safe for testing"))
        
        # Display the keys
        self.stdout.write(f"\nActive Key ID: {key_id}")
        
        if show_secrets:
            self.stdout.write(f"Active Key Secret: {key_secret}")
            self.stdout.write(self.style.WARNING(
                "\nCAUTION: Key secrets are sensitive information. "
                "Do not share or expose them in logs or public repositories."
            ))
        else:
            masked_secret = key_secret[:4] + "*" * (len(key_secret) - 8) + key_secret[-4:]
            self.stdout.write(f"Active Key Secret: {masked_secret}")
            self.stdout.write(
                "\nUse --show-secrets to view the full key secret (use with caution)"
            )
        
        # Display all available keys
        self.stdout.write("\nAvailable Keys:")
        self.stdout.write("---------------")
        self.stdout.write(f"Test Key ID: {razorpay_settings.test_key_id}")
        
        if show_secrets:
            self.stdout.write(f"Test Key Secret: {razorpay_settings.test_key_secret}")
        else:
            masked_test_secret = razorpay_settings.test_key_secret[:4] + "*" * (len(razorpay_settings.test_key_secret) - 8) + razorpay_settings.test_key_secret[-4:]
            self.stdout.write(f"Test Key Secret: {masked_test_secret}")
        
        self.stdout.write(f"Live Key ID: {razorpay_settings.live_key_id}")
        
        if show_secrets:
            self.stdout.write(f"Live Key Secret: {razorpay_settings.live_key_secret}")
        else:
            masked_live_secret = razorpay_settings.live_key_secret[:4] + "*" * (len(razorpay_settings.live_key_secret) - 8) + razorpay_settings.live_key_secret[-4:]
            self.stdout.write(f"Live Key Secret: {masked_live_secret}")
        
        # Display last updated time
        self.stdout.write(f"\nLast Updated: {razorpay_settings.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
