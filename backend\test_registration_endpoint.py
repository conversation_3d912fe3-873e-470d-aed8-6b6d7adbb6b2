#!/usr/bin/env python
"""
Test Registration Endpoint
===========================

This script tests the registration endpoint directly to ensure it's working.
"""

import os
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from accounts.models import UserProfile
from wallet.models import Wallet
from gaming.models import PlayerStats


def test_registration_endpoint():
    """Test the registration endpoint"""
    print("🧪 Testing Registration Endpoint")
    print("=" * 40)
    
    client = Client()
    
    # Test data
    test_username = "test_reg_user_123"
    test_email = "<EMAIL>"
    
    # Clean up any existing test user
    User.objects.filter(username=test_username).delete()
    User.objects.filter(email=test_email).delete()
    
    registration_data = {
        'username': test_username,
        'email': test_email,
        'password': 'TestPass123!',
        're_password': 'TestPass123!',
        'first_name': 'Test',
        'last_name': 'User'
    }
    
    print(f"Testing registration with data: {registration_data}")
    
    # Test the registration endpoint
    try:
        response = client.post('/api/auth/register/', 
                             data=json.dumps(registration_data),
                             content_type='application/json')
        
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content.decode()}")
        
        if response.status_code == 201:
            print("✅ Registration successful!")
            
            # Check if user was created
            try:
                user = User.objects.get(username=test_username)
                print(f"✅ User created: {user.username} (ID: {user.id})")
                print(f"   - Email: {user.email}")
                print(f"   - Active: {user.is_active}")
                print(f"   - First name: {user.first_name}")
                print(f"   - Last name: {user.last_name}")
                
                # Check profile
                try:
                    profile = user.profile
                    print(f"✅ Profile created: {profile}")
                except UserProfile.DoesNotExist:
                    print("❌ No profile found")
                
                # Check wallet
                try:
                    wallet = user.wallet
                    print(f"✅ Wallet created: {wallet.balance} tokens")
                    
                    # Check signup bonus
                    from wallet.models import WalletTransaction
                    bonus = WalletTransaction.objects.filter(
                        wallet=wallet,
                        transaction_type='signup_bonus'
                    ).first()
                    
                    if bonus:
                        print(f"✅ Signup bonus: {bonus.amount} tokens")
                    else:
                        print("❌ No signup bonus found")
                        
                except Wallet.DoesNotExist:
                    print("❌ No wallet found")
                
                # Check gaming stats
                try:
                    stats = user.gaming_stats
                    print(f"✅ Gaming stats created: {stats}")
                except PlayerStats.DoesNotExist:
                    print("❌ No gaming stats found")
                
                # Clean up
                user.delete()
                print("✅ Test user cleaned up")
                
            except User.DoesNotExist:
                print("❌ User was not created")
                
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            try:
                response_data = json.loads(response.content.decode())
                print(f"Error details: {response_data}")
            except:
                print(f"Raw response: {response.content.decode()}")
                
    except Exception as e:
        print(f"❌ Exception during registration test: {e}")
        import traceback
        traceback.print_exc()


def test_login_endpoint():
    """Test the login endpoint"""
    print("\n🔐 Testing Login Endpoint")
    print("=" * 40)
    
    client = Client()
    
    # Create a test user first
    test_username = "test_login_user_123"
    test_email = "<EMAIL>"
    test_password = "TestPass123!"
    
    # Clean up any existing test user
    User.objects.filter(username=test_username).delete()
    User.objects.filter(email=test_email).delete()
    
    # Create user
    user = User.objects.create_user(
        username=test_username,
        email=test_email,
        password=test_password
    )
    user.is_active = True
    user.save()
    
    print(f"Created test user: {user.username}")
    
    # Test login with username
    login_data = {
        'username': test_username,
        'password': test_password
    }
    
    try:
        response = client.post('/api/auth/login/',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        print(f"Login response status: {response.status_code}")
        print(f"Login response content: {response.content.decode()}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            try:
                response_data = json.loads(response.content.decode())
                if 'access' in response_data and 'refresh' in response_data:
                    print("✅ Tokens received")
                else:
                    print("❌ No tokens in response")
            except:
                print("❌ Could not parse response JSON")
        else:
            print(f"❌ Login failed with status {response.status_code}")
            
        # Clean up
        user.delete()
        print("✅ Test user cleaned up")
        
    except Exception as e:
        print(f"❌ Exception during login test: {e}")
        import traceback
        traceback.print_exc()


def test_url_patterns():
    """Test URL patterns"""
    print("\n🔗 Testing URL Patterns")
    print("=" * 40)
    
    from django.urls import reverse
    
    try:
        # Test if URLs can be reversed
        register_url = reverse('register')
        print(f"✅ Register URL: {register_url}")
        
        login_url = reverse('login')
        print(f"✅ Login URL: {login_url}")
        
        profile_url = reverse('user-profile')
        print(f"✅ Profile URL: {profile_url}")
        
    except Exception as e:
        print(f"❌ URL pattern error: {e}")


def main():
    """Main test function"""
    print("🔧 Registration Endpoint Test")
    print("=" * 50)
    
    test_url_patterns()
    test_registration_endpoint()
    test_login_endpoint()
    
    print("\n📋 Summary")
    print("=" * 40)
    print("If all tests pass, the registration endpoint should work from frontend.")
    print("If tests fail, check the Django logs for more details.")


if __name__ == '__main__':
    main()
