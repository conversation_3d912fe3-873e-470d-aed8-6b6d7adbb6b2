# 🛍️ **PickMeTrend Manual Product Management Guide**

## 🚀 **Quick Start: Adding Your First Product**

### **Step 1: Access Django Admin**
1. **Start the server:** `cd backend && python manage.py runserver`
2. **Open admin:** http://localhost:8000/admin/
3. **Login** with your superuser credentials

### **Step 2: Create Categories First**
1. **Go to:** Products → Categories
2. **Click:** "Add Category"
3. **Fill in:**
   - **Name:** "T-Shirts" (example)
   - **Slug:** "t-shirts" (auto-filled)
   - **Description:** "Comfortable cotton t-shirts"
   - **Is Active:** ✅ Checked
4. **Save**

### **Step 3: Add Your First Product**
1. **Go to:** Products → Products
2. **Click:** "Add Product"
3. **Fill in Basic Information:**
   - **Name:** "Cool Gaming T-Shirt"
   - **Slug:** "cool-gaming-t-shirt" (auto-filled)
   - **Description:** "Awesome t-shirt for gamers"
   - **Categories:** Select "T-Shirts"
   - **Is Featured:** ✅ (if you want it featured)
   - **Is Active:** ✅ Checked

4. **Fill in Pricing & Inventory:**
   - **Price:** 599.00
   - **Compare Price:** 799.00 (optional - shows discount)
   - **Stock:** 50

5. **Configure Token Discount (Optional):**
   - **Allow Token Discount:** ✅ (if you want customers to use tokens)
   - **Token Discount Percentage:** 30 (30% max discount with tokens)
   - **Token Discount Max Amount:** 100.00 (₹100 max discount cap)

6. **Add Product Images:**
   - Scroll down to "Product Images" section
   - **Click:** "Add another Product image"
   - **Upload image** or **paste image URL**
   - **Alt Text:** "Cool Gaming T-Shirt"
   - **Is Primary:** ✅ (for main image)

7. **Save**

## 📋 **Product Fields Explained**

### **Basic Information**
- **Name:** Product title (e.g., "Premium Cotton T-Shirt")
- **Slug:** URL-friendly name (auto-generated)
- **Description:** Product details, features, materials
- **Categories:** Product categories (create these first)
- **Is Featured:** Shows on homepage/featured sections
- **Is Active:** Makes product visible to customers

### **Pricing & Inventory**
- **Price:** Selling price in INR (e.g., 599.00)
- **Compare Price:** Original/MRP price (shows discount)
- **Stock:** Available quantity

### **Token Discount Settings**
- **Allow Token Discount:** Enable token payments for this product
- **Token Discount Percentage:** Max % of price payable with tokens (1-100%)
- **Token Discount Max Amount:** Maximum INR discount cap

### **Product Images**
- **Image:** Upload from computer
- **Image URL:** Use external image URL
- **Alt Text:** SEO description
- **Is Primary:** Main product image

## 🎯 **Token Discount Examples**

### **Example 1: T-Shirt (₹500)**
- **Allow Token Discount:** ✅
- **Token Discount Percentage:** 20%
- **Token Discount Max Amount:** ₹50
- **Result:** Customers can use up to ₹50 worth of tokens (500 tokens)

### **Example 2: Premium Jacket (₹2000)**
- **Allow Token Discount:** ✅
- **Token Discount Percentage:** 30%
- **Token Discount Max Amount:** ₹200
- **Result:** Customers can use up to ₹200 worth of tokens (2000 tokens)
- **Note:** Without cap, 30% would be ₹600, but cap limits it to ₹200

## 🔧 **Admin Features**

### **Bulk Actions**
1. **Select multiple products** in the product list
2. **Choose action:**
   - "Enable token discount for selected products"
   - "Disable token discount for selected products"
3. **Click "Go"**

### **Product List Columns**
- **Name:** Product name
- **Price:** Selling price
- **Compare Price:** Original price
- **Discount:** Regular discount percentage
- **Token Discount:** ✅ 30% (if enabled) or ✗ (if disabled)
- **Stock:** Available quantity
- **Categories:** Product categories
- **Featured:** ⭐ if featured
- **Active:** ✅ if active

### **Filtering & Search**
- **Filter by:** Active, Featured, Categories
- **Search:** Product name or description

## 🛒 **Customer Experience**

### **Product Display**
- Products with token discounts show **🪙 Token Discount Available** badge
- Discount percentage displayed on product cards

### **Shopping Cart**
- Mixed cart: token-eligible and regular products
- Token discount calculated automatically
- Shows maximum token usage and final amount

### **Checkout**
- **Token toggle:** "Use Wallet Tokens"
- **Real-time calculation:** Shows tokens used, discount, final amount
- **Payment:** Customer pays reduced amount

## 📊 **Product Management Tips**

### **Best Practices**
1. **Create categories first** before adding products
2. **Use high-quality images** (minimum 800x800px)
3. **Write detailed descriptions** for better SEO
4. **Set appropriate stock levels**
5. **Use token discounts strategically** on popular items

### **Token Discount Strategy**
- **High-margin products:** Higher token discount percentages
- **Popular items:** Enable token discounts to drive engagement
- **New products:** Use token discounts for promotion
- **Expensive items:** Set discount caps to control costs

### **SEO Optimization**
- **Product names:** Include relevant keywords
- **Descriptions:** Detailed, keyword-rich content
- **Alt text:** Descriptive image text
- **Categories:** Organize logically for navigation

## 🔄 **Product Workflow**

### **Adding New Products**
1. **Plan categories** → Create categories
2. **Prepare content** → Names, descriptions, images
3. **Add products** → Fill all required fields
4. **Configure tokens** → Set discount rules
5. **Test frontend** → Verify display and functionality

### **Managing Existing Products**
1. **Regular updates** → Stock, prices, descriptions
2. **Token adjustments** → Modify discount rules
3. **Image updates** → Replace or add new images
4. **Category changes** → Reorganize as needed

## 🎮 **Integration with Gaming System**

### **Token Economy**
- **Earn:** 5 tokens per game (₹0.50 value)
- **Spend:** Up to configured percentage on eligible products
- **Rate:** 1 token = ₹0.10

### **Customer Journey**
1. **Play games** → Earn tokens
2. **Browse products** → See token-eligible items
3. **Add to cart** → Mix eligible and regular products
4. **Checkout** → Apply tokens for discount
5. **Pay** → Reduced amount after token discount

This system creates a **gamified shopping experience** that encourages customer engagement and repeat purchases! 🎯🛒💰
