from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from rest_framework import serializers
from djoser.serializers import UserCreateSerializer as BaseUserCreateSerializer
from .models import UserProfile


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['user_type', 'phone_number', 'address', 'city', 'state', 'country', 'zip_code']

    def validate(self, attrs):
        print(f"UserProfileSerializer.validate called with attrs: {attrs}")
        try:
            return super().validate(attrs)
        except Exception as e:
            print(f"UserProfile validation error: {str(e)}")
            raise


class UserCreateSerializer(BaseUserCreateSerializer):
    """
    Custom UserCreateSerializer that ensures username is always set.
    If no username is provided, it will use the email address as username.
    """
    class Meta(BaseUserCreateSerializer.Meta):
        model = User
        fields = ['id', 'email', 'username', 'password', 'first_name', 'last_name']

    def validate(self, attrs):
        # If username is not provided, use email as username
        if 'username' not in attrs or not attrs['username']:
            attrs['username'] = attrs.get('email')

        return super().validate(attrs)


class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'profile', 'date_joined', 'is_staff']
        read_only_fields = ['id', 'date_joined', 'is_staff']

    def update(self, instance, validated_data):
        print(f"UserSerializer.update called with validated_data: {validated_data}")
        profile_data = validated_data.pop('profile', None)
        print(f"Profile data: {profile_data}")

        # Update User fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update Profile fields
        if profile_data:
            profile = instance.profile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance

    def validate(self, attrs):
        print(f"UserSerializer.validate called with attrs: {attrs}")
        try:
            return super().validate(attrs)
        except Exception as e:
            print(f"Validation error: {str(e)}")
            raise


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)

    def validate_new_password(self, value):
        validate_password(value)
        return value