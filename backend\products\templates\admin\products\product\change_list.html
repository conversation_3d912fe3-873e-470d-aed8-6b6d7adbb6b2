{% extends "admin/change_list.html" %}
{% load i18n admin_urls %}

{% block object-tools-items %}
    <li>
        <a href="{% url 'admin:sync-products' %}?quick=true" class="addlink" style="background-color: #28a745; border-color: #28a745;">
            {% trans 'Quick Sync (2 products)' %}
        </a>
    </li>
    <li>
        <a href="{% url 'admin:sync-products' %}" class="addlink" style="background-color: #007cba; border-color: #007cba;">
            {% trans 'Full Sync (Background)' %}
        </a>
    </li>
    {{ block.super }}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .object-tools li a {
            margin-right: 5px;
        }
        .sync-status {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .sync-status.running {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .sync-status.completed {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .sync-status.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
{% endblock %}

{% block content %}
    <!-- Sync Status Display -->
    <div id="sync-status-container">
        <!-- Status will be loaded via JavaScript -->
    </div>

    {{ block.super }}

    <script>
        // Check sync status periodically
        function checkSyncStatus() {
            fetch('/api/products/debug/sync-status/')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('sync-status-container');
                    if (data.status && data.status !== 'none') {
                        let statusClass = 'sync-status ' + data.status;
                        let statusHtml = `
                            <div class="${statusClass}">
                                <strong>Sync Status:</strong> ${data.message || data.status}
                                ${data.duration ? ` (${data.duration.toFixed(1)}s)` : ''}
                                ${data.synced_count ? ` - ${data.synced_count} products synced` : ''}
                            </div>
                        `;
                        container.innerHTML = statusHtml;

                        // If running, check again in 5 seconds
                        if (data.status === 'running') {
                            setTimeout(checkSyncStatus, 5000);
                        }
                    } else {
                        container.innerHTML = '';
                    }
                })
                .catch(error => {
                    console.log('Sync status check failed:', error);
                });
        }

        // Check status on page load
        document.addEventListener('DOMContentLoaded', checkSyncStatus);
    </script>
{% endblock %}
