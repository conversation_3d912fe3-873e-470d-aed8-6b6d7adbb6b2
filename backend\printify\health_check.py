
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from datetime import datetime
import json
import time

from .monitoring import get_printify_status, printify_monitor
from .models import PrintifyProduct
from products.models import Product, ProductImage

@csrf_exempt
@require_http_methods(["GET"])
def printify_health_check(request):
    """Comprehensive health check endpoint for Printify API"""
    start_time = time.time()

    try:
        # Get comprehensive status
        status = get_printify_status()

        # Additional checks
        printify_products_count = PrintifyProduct.objects.count()
        products_count = Product.objects.count()
        images_count = ProductImage.objects.count()

        # Test image accessibility (sample)
        sample_images = ProductImage.objects.all()[:5]
        accessible_images = 0
        for img in sample_images:
            if img.image_url:
                try:
                    import requests
                    response = requests.head(img.image_url, timeout=5)
                    if response.status_code == 200:
                        accessible_images += 1
                except:
                    pass

        response_time = time.time() - start_time

        health_data = {
            "status": "healthy" if status['health_check']['success'] else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "response_time_seconds": round(response_time, 3),
            "printify_api": {
                "connected": status['health_check']['success'],
                "status": status['api_status']['status'],
                "last_success": status['api_status']['last_success'].isoformat() if status['api_status']['last_success'] else None,
                "error_count": status['api_status']['error_count'],
                "message": status['health_check']['message']
            },
            "configuration": {
                "api_token_configured": bool(settings.PRINTIFY_API_TOKEN),
                "shop_id_configured": bool(settings.PRINTIFY_SHOP_ID),
                "shop_id": settings.PRINTIFY_SHOP_ID if settings.PRINTIFY_SHOP_ID else None
            },
            "database": {
                "printify_products": printify_products_count,
                "products": products_count,
                "product_images": images_count
            },
            "images": {
                "sample_tested": len(sample_images),
                "accessible": accessible_images,
                "accessibility_rate": f"{(accessible_images/len(sample_images)*100):.1f}%" if sample_images else "N/A"
            },
            "fallback": {
                "available": True,
                "cache_enabled": True,
                "database_fallback": printify_products_count > 0
            }
        }

        # Determine overall health
        is_healthy = (
            status['health_check']['success'] or  # API working
            printify_products_count > 0 or       # Database fallback available
            images_count > 0                     # Images available
        )

        health_data["status"] = "healthy" if is_healthy else "unhealthy"

        status_code = 200 if is_healthy else 503
        return JsonResponse(health_data, status=status_code)

    except Exception as e:
        return JsonResponse({
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "printify_api": "unknown",
            "response_time_seconds": round(time.time() - start_time, 3)
        }, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def printify_detailed_status(request):
    """Detailed status endpoint with more comprehensive information"""
    try:
        # Test API connectivity
        api_test_start = time.time()
        api_status = printify_monitor.check_api_health()
        api_test_time = time.time() - api_test_start

        # Get products test
        products_test_start = time.time()
        try:
            from .monitoring import get_products_safely
            products, source = get_products_safely(settings.PRINTIFY_SHOP_ID)
            products_test_time = time.time() - products_test_start
            products_success = True
        except Exception as e:
            products_test_time = time.time() - products_test_start
            products_success = False
            products = []
            source = 'error'

        return JsonResponse({
            "timestamp": datetime.now().isoformat(),
            "api_connectivity": {
                "success": api_status[0],
                "message": api_status[1],
                "response_time": round(api_test_time, 3)
            },
            "products_fetch": {
                "success": products_success,
                "count": len(products),
                "source": source,
                "response_time": round(products_test_time, 3)
            },
            "environment": {
                "debug_mode": settings.DEBUG,
                "api_base_url": "https://api.printify.com/v1",
                "shop_id": settings.PRINTIFY_SHOP_ID
            }
        })

    except Exception as e:
        return JsonResponse({
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "success": False
        }, status=500)
