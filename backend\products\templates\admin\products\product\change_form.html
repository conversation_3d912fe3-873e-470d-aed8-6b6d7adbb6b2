{% extends "admin/change_form.html" %}
{% load i18n admin_urls %}

{% block object-tools-items %}
    {% if original.printify_id %}
    <li>
        <a href="{% url 'admin:sync-product-variants' original.pk %}" class="historylink">
            {% trans 'Sync Variants' %}
        </a>
    </li>
    <li>
        <a href="{% url 'admin:sync-images' original.pk %}" class="addlink">
            {% trans 'Sync Images' %}
        </a>
    </li>
    {% endif %}
    {% if original.pk %}
    <li>
        <a href="{% url 'admin:clean-description' original.pk %}" class="button">
            {% if original.cleaned %}
                Re-Clean Description
            {% else %}
                Clean Description
            {% endif %}
        </a>
    </li>
    <li>
        <a href="{% url 'admin:clean-description' original.pk %}?use_ai=true" class="button">
            Clean with AI
        </a>
    </li>
    {% endif %}
    {{ block.super }}
{% endblock %}
