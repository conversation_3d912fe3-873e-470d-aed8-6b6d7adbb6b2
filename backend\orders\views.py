from django.shortcuts import render
from rest_framework import viewsets, generics, permissions, status, serializers
from rest_framework.response import Response
from rest_framework.decorators import action, api_view, permission_classes
from django.shortcuts import get_object_or_404
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from .models import Cart, CartItem, Order, OrderItem
from .serializers import (
    CartSerializer, CartItemSerializer,
    OrderSerializer, OrderItemSerializer,
    OrderCreateSerializer, OrderStatusUpdateSerializer,
    PaymentUpdateSerializer
)
from products.models import Product
from .razorpay_utils import create_razorpay_order, verify_payment_signature
from razorpay_settings.utils import get_razorpay_keys, initialize_razorpay_client
import json
import uuid
import logging
from decimal import Decimal

# Set up logging
logger = logging.getLogger(__name__)

# Initialize Razorpay client using our utility function
razorpay_client = initialize_razorpay_client()

@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def razorpay_test_view(request):
    """
    View to test Razorpay integration
    """
    # Get the active Razorpay key ID using our utility function
    key_id, _ = get_razorpay_keys()

    context = {
        'razorpay_key_id': key_id
    }
    return render(request, 'orders/razorpay_test.html', context)


@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAdminUser])  # Restrict to admin users only
def debug_create_order(request):
    """
    Debug endpoint for order creation
    """
    # Print raw request data for debugging
    print("DEBUG ENDPOINT CALLED")
    print(f"Request method: {request.method}")
    print(f"Request content type: {request.content_type}")
    print(f"Request body: {request.body}")
    print(f"Request POST: {request.POST}")
    print(f"Request data: {request.data}")
    print(f"Request user: {request.user}")
    print(f"Request headers: {request.headers}")

    # For GET requests, just return a simple response
    if request.method == 'GET':
        return Response({
            'message': 'Debug endpoint is working. Use POST to create a test order.',
            'user': request.user.username
        })
    try:
        # Print the request data
        print(f"Debug order creation - Request data: {request.data}")

        # Check if the user has a cart
        try:
            cart = Cart.objects.get(user=request.user)
            print(f"Found cart: {cart.id} with {cart.items.count()} items")
        except Cart.DoesNotExist:
            return Response(
                {'detail': 'You do not have a cart.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if cart is empty
        if not cart.items.exists():
            return Response(
                {'detail': 'Your cart is empty.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create a simple order
        order = Order.objects.create(
            user=request.user,
            full_name=request.data.get('full_name', 'Test User'),
            email=request.data.get('email', '<EMAIL>'),
            phone=request.data.get('phone', '9876543210'),
            address=request.data.get('address', 'Test Address'),
            city=request.data.get('city', 'Test City'),
            state=request.data.get('state', 'Test State'),
            zipcode=request.data.get('zipcode', '123456'),
            country=request.data.get('country', 'IN'),
            payment_method=request.data.get('payment_method', 'cod'),
            total=cart.total_price
        )

        # Create order items
        for item in cart.items.all():
            OrderItem.objects.create(
                order=order,
                product=item.product,
                price=item.product.price,
                quantity=item.quantity
            )

        # Return the order
        return Response(
            OrderSerializer(order).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        print(f"Debug order creation error: {str(e)}")
        return Response(
            {'detail': f'Error: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET', 'POST'])
@permission_classes([permissions.IsAdminUser])  # Restrict to admin users only
def simple_test_order(request):
    """
    Super simple endpoint to create a test order with minimal validation
    """
    print("SIMPLE TEST ORDER ENDPOINT CALLED")
    print(f"User: {request.user.username}")

    try:
        # Check if user has a cart
        try:
            cart = Cart.objects.get(user=request.user)
            print(f"Found cart with {cart.items.count()} items")

            if not cart.items.exists():
                # Add a dummy item to the cart if it's empty
                print("Cart is empty, adding a dummy product")
                try:
                    # Try to get a product
                    from products.models import Product
                    product = Product.objects.first()
                    if not product:
                        return Response(
                            {'detail': 'No products available to add to cart'},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Add to cart
                    CartItem.objects.create(
                        cart=cart,
                        product=product,
                        quantity=1
                    )
                    print(f"Added product {product.id} to cart")
                except Exception as e:
                    print(f"Error adding product to cart: {str(e)}")
                    return Response(
                        {'detail': f'Error adding product to cart: {str(e)}'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )

        except Cart.DoesNotExist:
            # Create a cart if it doesn't exist
            print("No cart found, creating one")
            cart = Cart.objects.create(user=request.user)

            # Add a dummy item to the cart
            print("Adding a dummy product to the new cart")
            try:
                # Try to get a product
                from products.models import Product
                product = Product.objects.first()
                if not product:
                    return Response(
                        {'detail': 'No products available to add to cart'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Add to cart
                CartItem.objects.create(
                    cart=cart,
                    product=product,
                    quantity=1
                )
                print(f"Added product {product.id} to cart")
            except Exception as e:
                print(f"Error adding product to cart: {str(e)}")
                return Response(
                    {'detail': f'Error adding product to cart: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        # Create a simple order with hardcoded values
        order = Order.objects.create(
            user=request.user,
            full_name="Test User",
            email="<EMAIL>",
            phone="9876543210",
            address="Test Address",
            city="Test City",
            state="Test State",  # Make sure this is not empty
            zipcode="123456",
            country="IN",
            payment_method="cod",
            total=cart.total_price or 100  # Use cart total or default to 100
        )
        print(f"Created order {order.id}")

        # Create order items from cart items
        for item in cart.items.all():
            OrderItem.objects.create(
                order=order,
                product=item.product,
                price=item.product.price,
                quantity=item.quantity
            )
        print(f"Added {cart.items.count()} items to order")

        # Clear the cart
        cart.items.all().delete()
        print("Cleared the cart")

        # Return the order
        return Response(
            OrderSerializer(order).data,
            status=status.HTTP_201_CREATED
        )
    except Exception as e:
        print(f"Error in simple_test_order: {str(e)}")
        return Response(
            {'detail': f'Error creating test order: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def create_test_order(request):
    """
    Create a test Razorpay order
    """
    try:
        # Log the request
        print(f"Creating test order with request: {request.body}")

        # Parse the request body
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            print("Error decoding JSON from request body")
            data = {}

        amount = data.get('amount', 50000)  # Default to 500 INR
        currency = data.get('currency', 'INR')
        receipt = data.get('receipt', f'test_receipt_{uuid.uuid4()}')

        print(f"Creating Razorpay order with amount: {amount}, currency: {currency}, receipt: {receipt}")

        # Create Razorpay order
        order_data = {
            'amount': amount,
            'currency': currency,
            'receipt': receipt,
            'payment_capture': 1  # Auto-capture payment
        }

        # Create the order
        order = razorpay_client.order.create(order_data)
        print(f"Razorpay order created: {order}")

        # Return the order with key_id from our utility function
        key_id, _ = get_razorpay_keys()
        response_data = order.copy()
        response_data['key_id'] = key_id

        return JsonResponse(response_data)
    except Exception as e:
        print(f"Error creating test order: {str(e)}")
        return JsonResponse({'error': str(e)}, status=400)

@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def verify_test_payment(request):
    """
    Verify a test Razorpay payment
    """
    try:
        # Log the request
        print(f"Verifying test payment with request: {request.body}")

        # Parse the request body
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            print("Error decoding JSON from request body")
            return JsonResponse({
                'verified': False,
                'message': 'Invalid JSON in request body'
            }, status=400)

        # Extract payment details
        razorpay_payment_id = data.get('razorpay_payment_id')
        razorpay_order_id = data.get('razorpay_order_id')
        razorpay_signature = data.get('razorpay_signature')

        # Check if all required parameters are present
        if not all([razorpay_payment_id, razorpay_order_id, razorpay_signature]):
            missing = []
            if not razorpay_payment_id:
                missing.append('razorpay_payment_id')
            if not razorpay_order_id:
                missing.append('razorpay_order_id')
            if not razorpay_signature:
                missing.append('razorpay_signature')

            error_message = f"Missing required parameters: {', '.join(missing)}"
            print(error_message)
            return JsonResponse({
                'verified': False,
                'message': error_message
            }, status=400)

        # Create parameters dict for verification
        params_dict = {
            'razorpay_payment_id': razorpay_payment_id,
            'razorpay_order_id': razorpay_order_id,
            'razorpay_signature': razorpay_signature
        }

        print(f"Verifying payment with parameters: {params_dict}")

        # Verify signature
        try:
            # This will raise an exception if verification fails
            razorpay_client.utility.verify_payment_signature(params_dict)
            is_valid = True
            print("Payment signature verified successfully")
        except Exception as e:
            is_valid = False
            print(f"Payment signature verification failed: {str(e)}")

        if is_valid:
            # Get payment details from Razorpay
            try:
                payment_details = razorpay_client.payment.fetch(razorpay_payment_id)
                print(f"Payment details: {payment_details}")
            except Exception as e:
                print(f"Error fetching payment details: {str(e)}")
                payment_details = {}

            return JsonResponse({
                'verified': True,
                'message': 'Payment verified successfully',
                'payment_id': razorpay_payment_id,
                'payment_details': payment_details
            })
        else:
            return JsonResponse({
                'verified': False,
                'message': 'Payment verification failed'
            }, status=400)
    except Exception as e:
        print(f"Error verifying payment: {str(e)}")
        return JsonResponse({
            'verified': False,
            'message': str(e)
        }, status=400)


class IsAdminOrOwner(permissions.BasePermission):
    """
    Permission to only allow owners of an object or admins to access it
    """
    def has_object_permission(self, request, view, obj):
        # Check if user is admin
        if request.user.is_staff:
            return True

        # Check if the object has a user attribute and if it's the current user
        if hasattr(obj, 'user'):
            return obj.user == request.user

        return False


class CartViewSet(viewsets.ModelViewSet):
    """
    ViewSet for cart operations
    """
    serializer_class = CartSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Cart.objects.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        # Check if user already has a cart
        if Cart.objects.filter(user=request.user).exists():
            return Response(
                {'detail': 'You already have a cart.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        cart = Cart.objects.create(user=request.user)
        serializer = self.get_serializer(cart)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def add_item(self, request, pk=None):
        print(f"Adding item to cart {pk} with data: {request.data}")

        try:
            cart = self.get_object()
            serializer = CartItemSerializer(data=request.data)

            if serializer.is_valid():
                try:
                    # The validation is now handled in the serializer
                    serializer.save(cart=cart)

                    # Return the updated cart
                    cart_serializer = self.get_serializer(cart)
                    return Response(cart_serializer.data)
                except serializers.ValidationError as e:
                    print(f"Validation error when adding item: {e}")
                    return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    print(f"Unexpected error when adding item: {str(e)}")
                    return Response(
                        {'detail': f'Error adding item to cart: {str(e)}'},
                        status=status.HTTP_500_INTERNAL_SERVER_ERROR
                    )
            else:
                print(f"Invalid serializer data: {serializer.errors}")
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"Error in add_item view: {str(e)}")
            return Response(
                {'detail': f'Error processing request: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def remove_item(self, request, pk=None):
        cart = self.get_object()
        item_id = request.data.get('item_id')

        if not item_id:
            return Response(
                {'detail': 'Item ID is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            item = CartItem.objects.get(id=item_id, cart=cart)
            item.delete()
            # Return the updated cart
            serializer = self.get_serializer(cart)
            return Response(serializer.data)
        except CartItem.DoesNotExist:
            return Response(
                {'detail': 'Item not found in cart.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def update_item(self, request, pk=None):
        cart = self.get_object()
        item_id = request.data.get('item_id')
        quantity = request.data.get('quantity')

        if not item_id or not quantity:
            return Response(
                {'detail': 'Item ID and quantity are required.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            item = CartItem.objects.get(id=item_id, cart=cart)
            product = item.product

            # Check if product has enough stock
            if product.stock < int(quantity):
                return Response(
                    {'detail': 'Not enough stock available.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            item.quantity = int(quantity)
            item.save()

            # Return the updated cart
            serializer = self.get_serializer(cart)
            return Response(serializer.data)
        except CartItem.DoesNotExist:
            return Response(
                {'detail': 'Item not found in cart.'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def clear(self, request, pk=None):
        cart = self.get_object()
        cart.items.all().delete()
        serializer = self.get_serializer(cart)
        return Response(serializer.data)


class OrderViewSet(viewsets.ModelViewSet):
    """
    ViewSet for order operations
    """
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminOrOwner]

    def get_queryset(self):
        user = self.request.user
        if user.is_staff:
            return Order.objects.all()
        return Order.objects.filter(user=user)

    def get_serializer_class(self):
        if self.action == 'create':
            return OrderCreateSerializer
        elif self.action == 'update_status':
            return OrderStatusUpdateSerializer
        elif self.action == 'update_payment':
            return PaymentUpdateSerializer
        return OrderSerializer

    @action(detail=False, methods=['post'])
    def create_from_cart(self, request):
        print(f"Creating order from cart for user: {request.user.username}")
        print(f"Request data: {request.data}")

        # Get user's cart
        try:
            cart = Cart.objects.get(user=request.user)
            print(f"Found cart: {cart.id} with {cart.items.count()} items")
        except Cart.DoesNotExist:
            print(f"No cart found for user: {request.user.username}")
            return Response(
                {'detail': 'You do not have a cart. Please add items to your cart first.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if cart is empty
        if not cart.items.exists():
            print(f"Cart {cart.id} is empty")
            return Response(
                {'detail': 'Your cart is empty. Please add items to your cart before checkout.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Print the request data for debugging
        print(f"Request data for order creation: {request.data}")
        print(f"Request data type: {type(request.data)}")
        print(f"Request content type: {request.content_type}")
        print(f"Request headers: {request.headers}")

        # Check if payment_method is in the request data
        if 'payment_method' not in request.data:
            print("Error: payment_method is missing from request data")
            print(f"Available keys in request.data: {request.data.keys() if hasattr(request.data, 'keys') else 'No keys method'}")
            return Response(
                {'detail': 'payment_method is required', 'received_data': request.data},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if payment method is COD and reject it
        if request.data.get('payment_method', '').lower() == 'cod':
            print("Error: COD payment method is not available")
            return Response(
                {'detail': 'Cash on Delivery is currently not available. Please use our secure online payment options (Credit/Debit Cards, UPI, Net Banking, Digital Wallets).'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate the data with the serializer
        serializer = OrderCreateSerializer(data=request.data)
        if not serializer.is_valid():
            print(f"Order validation errors: {serializer.errors}")
            return Response(
                {'detail': 'Validation failed', 'errors': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create order
        print(f"Creating order with data: {serializer.validated_data}")

        # Validate cart total
        if cart.total_price <= 0:
            print(f"Invalid cart total: {cart.total_price}")
            return Response(
                {'detail': 'Invalid cart total. Please try again or contact support.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Extract data from serializer
            validated_data = serializer.validated_data

            # Calculate token discount info
            from wallet.models import Wallet
            from decimal import Decimal
            wallet, _ = Wallet.objects.get_or_create(user=request.user)
            discount_info = cart.calculate_token_discount_info(wallet.balance)
            eligible_product_ids = set()
            per_item_discount = {}
            if discount_info['eligible']:
                for item in discount_info['eligible_items']:
                    eligible_product_ids.add(item['product_name'])
                    # Ensure we're working with Decimal types
                    item_discount = Decimal(str(item['max_inr_discount']))
                    per_item_discount[item['product_name']] = item_discount / Decimal(str(item['quantity'])) if item['quantity'] else Decimal('0')
                total_token_discount = Decimal(str(discount_info['max_inr_discount']))
            else:
                total_token_discount = Decimal('0')

            # Create the order
            order = Order.objects.create(
                user=request.user,
                total=Decimal(str(cart.total_price)) - total_token_discount,
                **validated_data
            )

            # Populate shipping address fields from billing address if not provided
            if not order.shipping_first_name:
                name_parts = order.full_name.split(' ', 1)
                order.shipping_first_name = name_parts[0]
                order.shipping_last_name = name_parts[1] if len(name_parts) > 1 else ''
            if not order.shipping_address1:
                order.shipping_address1 = order.address
            if not order.shipping_city:
                order.shipping_city = order.city
            if not order.shipping_state:
                order.shipping_state = order.state
            if not order.shipping_zip:
                order.shipping_zip = order.zipcode
            if not order.shipping_country:
                order.shipping_country = order.country
            order.save()

            print(f"Order created: {order.id} with total: {order.total}")

            # Create order items, applying discount only to eligible products
            for item in cart.items.all():
                variant_id = getattr(item, 'variant_id', None)
                # Only apply discount if product is eligible
                discount_per_item = Decimal('0')
                if item.product.name in per_item_discount:
                    discount_per_item = per_item_discount[item.product.name]

                # Calculate final price after discount
                final_price = item.product.price - discount_per_item

                OrderItem.objects.create(
                    order=order,
                    product=item.product,
                    price=final_price,
                    quantity=item.quantity,
                    variant_id=variant_id
                )
            print(f"Created {cart.items.count()} order items")
        except Exception as e:
            print(f"Error creating order: {str(e)}")
            return Response(
                {'detail': f'Error creating order: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        # If payment method is Razorpay, create a Razorpay order
        if order.payment_method == 'razorpay':
                try:
                    # Log the order details
                    print(f"Creating Razorpay order for order ID: {order.id}, amount: {order.total}")
                    print(f"Order total type: {type(order.total)}, value: {order.total}")

                    # Validate Razorpay credentials
                    if not settings.RAZORPAY_KEY_ID or not settings.RAZORPAY_KEY_SECRET:
                        raise ValueError("Razorpay API keys are not properly configured")

                    # Validate order amount
                    if order.total <= 0:
                        raise ValueError(f"Invalid order amount: {order.total}. Amount must be positive.")

                    # Create Razorpay order
                    razorpay_order = create_razorpay_order(order.id, order.total)

                    # Log the Razorpay order response
                    print(f"Razorpay order created: {razorpay_order}")

                    # Validate the Razorpay order response
                    if not razorpay_order or not isinstance(razorpay_order, dict) or 'id' not in razorpay_order:
                        raise ValueError(f"Invalid Razorpay order response: {razorpay_order}")

                    # Save Razorpay order ID to our order
                    order.razorpay_order_id = razorpay_order['id']
                    order.save()

                    # Return order with Razorpay details
                    # Get the active Razorpay key using our utility function
                    key_id, _ = get_razorpay_keys()

                    response_data = OrderSerializer(order).data
                    response_data['razorpay'] = {
                        'order_id': razorpay_order['id'],
                        'amount': razorpay_order['amount'],
                        'currency': razorpay_order['currency'],
                        'key_id': key_id
                    }

                    # Add customer details for Razorpay prefill
                    response_data['full_name'] = order.full_name
                    response_data['email'] = order.email
                    response_data['phone'] = order.phone

                    print(f"Returning order with Razorpay details: {response_data['razorpay']}")
                    return Response(response_data, status=status.HTTP_201_CREATED)
                except Exception as e:
                    # Log the error
                    print(f"Error creating Razorpay order: {str(e)}")

                    # If Razorpay order creation fails, return a detailed error
                    response_data = OrderSerializer(order).data
                    response_data['razorpay_error'] = str(e)

                    # Return a 400 status to indicate the error
                    return Response(
                        {
                            'detail': f"Failed to create Razorpay order: {str(e)}",
                            'order': response_data
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

        # Clear cart after order creation
        cart.items.all().delete()

        # Return the created order
        return Response(
            OrderSerializer(order).data,
            status=status.HTTP_201_CREATED
        )

    @action(detail=True, methods=['post'])
    def verify_razorpay_payment(self, request, pk=None):
        """
        Verify Razorpay payment and update order status
        """
        try:
            order = self.get_object()

            # Log the verification attempt
            print(f"Verifying Razorpay payment for order: {order.id}")
            print(f"Request data: {request.data}")

            # Get payment details from request
            razorpay_payment_id = request.data.get('razorpay_payment_id')
            razorpay_order_id = request.data.get('razorpay_order_id')
            razorpay_signature = request.data.get('razorpay_signature')

            # Validate required fields
            if not all([razorpay_payment_id, razorpay_order_id, razorpay_signature]):
                missing_fields = []
                if not razorpay_payment_id:
                    missing_fields.append('razorpay_payment_id')
                if not razorpay_order_id:
                    missing_fields.append('razorpay_order_id')
                if not razorpay_signature:
                    missing_fields.append('razorpay_signature')

                error_message = f"Missing required payment information: {', '.join(missing_fields)}"
                print(error_message)

                return Response(
                    {'detail': error_message},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Check if the order ID matches
            if order.razorpay_order_id != razorpay_order_id:
                error_message = f"Order ID mismatch. Expected: {order.razorpay_order_id}, Got: {razorpay_order_id}"
                print(error_message)

                return Response(
                    {'detail': error_message},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verify payment signature
            print(f"Verifying payment signature for payment ID: {razorpay_payment_id}")
            is_valid = verify_payment_signature(
                razorpay_payment_id,
                razorpay_order_id,
                razorpay_signature
            )

            if is_valid:
                print(f"Payment signature verified successfully for order: {order.id}")

                # Update order with payment details
                order.razorpay_payment_id = razorpay_payment_id
                order.razorpay_signature = razorpay_signature
                order.payment_status = 'completed'
                order.status = 'processing'
                order.is_paid = True
                order.save()

                print(f"Order updated with payment details: {order.id}")

                # Call the payment_success endpoint to place the order on Printify
                try:
                    # Prepare the data for the payment_success endpoint
                    payment_success_data = {
                        'order_id': str(order.id)
                    }

                    # Make a request to the payment_success endpoint
                    from django.urls import reverse
                    from django.http import HttpRequest
                    from rest_framework.test import APIRequestFactory
                    from .views_printify import handle_payment_success

                    # Create a request object
                    factory = APIRequestFactory()
                    request_obj = factory.post(
                        reverse('payment_success'),
                        data=payment_success_data,
                        format='json'
                    )

                    # Call the payment_success view
                    print(f"Calling payment_success endpoint for order: {order.id}")
                    response = handle_payment_success(request_obj)

                    print(f"Payment success response: {response.data if hasattr(response, 'data') else response}")

                    # Check if the Printify order placement was successful
                    if hasattr(response, 'data') and response.data.get('success'):
                        print(f"Successfully placed order on Printify for order: {order.id}")
                    else:
                        print(f"Failed to place order on Printify for order: {order.id}")
                except Exception as e:
                    print(f"Error calling payment_success endpoint: {str(e)}")
                    # Don't fail the payment verification if Printify order placement fails
                    # We'll handle this separately

                return Response(
                    {'detail': 'Payment verified successfully.', 'order': OrderSerializer(order).data},
                    status=status.HTTP_200_OK
                )
            else:
                error_message = "Payment signature verification failed"
                print(f"{error_message} for order: {order.id}")

                return Response(
                    {'detail': error_message},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            error_message = f"Error verifying payment: {str(e)}"
            print(error_message)

            return Response(
                {'detail': error_message},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['patch'], permission_classes=[permissions.IsAdminUser])
    def update_status(self, request, pk=None):
        order = self.get_object()
        serializer = OrderStatusUpdateSerializer(order, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(OrderSerializer(order).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['patch'], permission_classes=[permissions.IsAdminUser])
    def update_payment(self, request, pk=None):
        order = self.get_object()
        serializer = PaymentUpdateSerializer(order, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(OrderSerializer(order).data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Direct cart operation endpoints
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_to_cart(request):
    """
    Add an item to the user's cart
    """
    logger.info(f"Adding item to cart for user {request.user.username} with data: {request.data}")

    try:
        # Get or create user's cart
        cart, created = Cart.objects.get_or_create(user=request.user)
        if created:
            logger.info(f"Created new cart for user {request.user.username}")
        else:
            logger.info(f"Using existing cart {cart.id} for user {request.user.username}")

        # Get product, quantity, and variant_id from request
        product_id = request.data.get('product_id')
        quantity = request.data.get('quantity', 1)
        variant_id = request.data.get('variant_id')

        if not product_id:
            return Response(
                {'detail': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            quantity = int(quantity)
            if quantity <= 0:
                return Response(
                    {'detail': 'Quantity must be a positive integer'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'detail': 'Quantity must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the product
        try:
            product = Product.objects.get(id=product_id)
            logger.info(f"Found product {product.id}: {product.name}")
        except Product.DoesNotExist:
            logger.error(f"Product with ID {product_id} not found")
            return Response(
                {'detail': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if product has enough stock
        if product.stock < quantity:
            logger.warning(f"Not enough stock for product {product.id}. Requested: {quantity}, Available: {product.stock}")
            return Response(
                {'detail': f'Not enough stock available. Only {product.stock} items left.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if item already exists in cart with the same variant
        try:
            # If variant_id is provided, include it in the lookup
            if variant_id:
                cart_item = CartItem.objects.get(cart=cart, product=product, variant_id=variant_id)
            else:
                cart_item = CartItem.objects.get(cart=cart, product=product, variant_id__isnull=True)

            logger.info(f"Item already exists in cart with quantity {cart_item.quantity}")

            # Update quantity
            cart_item.quantity += quantity
            cart_item.save()
            logger.info(f"Updated item quantity to {cart_item.quantity}")
        except CartItem.DoesNotExist:
            # Create new cart item
            cart_item = CartItem.objects.create(
                cart=cart,
                product=product,
                quantity=quantity,
                variant_id=variant_id
            )
            logger.info(f"Created new cart item with quantity {quantity} and variant_id {variant_id}")

        # Return the updated cart
        serializer = CartSerializer(cart)
        return Response(serializer.data)
    except Exception as e:
        logger.error(f"Error adding item to cart: {str(e)}")
        return Response(
            {'detail': f'Error adding item to cart: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_from_cart(request, item_id):
    """
    Remove an item from the user's cart
    """
    logger.info(f"Removing item {item_id} from cart for user {request.user.username}")

    try:
        # Get user's cart
        try:
            cart = Cart.objects.get(user=request.user)
            logger.info(f"Found cart {cart.id} for user {request.user.username}")
        except Cart.DoesNotExist:
            logger.error(f"No cart found for user {request.user.username}")
            return Response(
                {'detail': 'You do not have a cart'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get the cart item
        try:
            cart_item = CartItem.objects.get(id=item_id, cart=cart)
            logger.info(f"Found cart item {cart_item.id} for product {cart_item.product.id}")
        except CartItem.DoesNotExist:
            logger.error(f"Cart item {item_id} not found in cart {cart.id}")
            return Response(
                {'detail': 'Item not found in cart'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Delete the cart item
        cart_item.delete()
        logger.info(f"Deleted cart item {item_id}")

        # Return the updated cart
        serializer = CartSerializer(cart)
        return Response(serializer.data)
    except Exception as e:
        logger.error(f"Error removing item from cart: {str(e)}")
        return Response(
            {'detail': f'Error removing item from cart: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_cart_item(request, item_id):
    """
    Update the quantity of an item in the user's cart
    """
    logger.info(f"Updating item {item_id} in cart for user {request.user.username} with data: {request.data}")

    try:
        # Get quantity from request
        quantity = request.data.get('quantity')

        if quantity is None:
            return Response(
                {'detail': 'quantity is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            quantity = int(quantity)
            if quantity <= 0:
                return Response(
                    {'detail': 'Quantity must be a positive integer'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'detail': 'Quantity must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get user's cart
        try:
            cart = Cart.objects.get(user=request.user)
            logger.info(f"Found cart {cart.id} for user {request.user.username}")
        except Cart.DoesNotExist:
            logger.error(f"No cart found for user {request.user.username}")
            return Response(
                {'detail': 'You do not have a cart'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Get the cart item
        try:
            cart_item = CartItem.objects.get(id=item_id, cart=cart)
            logger.info(f"Found cart item {cart_item.id} for product {cart_item.product.id}")
        except CartItem.DoesNotExist:
            logger.error(f"Cart item {item_id} not found in cart {cart.id}")
            return Response(
                {'detail': 'Item not found in cart'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if product has enough stock
        if cart_item.product.stock < quantity:
            logger.warning(f"Not enough stock for product {cart_item.product.id}. Requested: {quantity}, Available: {cart_item.product.stock}")
            return Response(
                {'detail': f'Not enough stock available. Only {cart_item.product.stock} items left.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update the cart item
        cart_item.quantity = quantity
        cart_item.save()
        logger.info(f"Updated cart item {item_id} quantity to {quantity}")

        # Return the updated cart
        serializer = CartSerializer(cart)
        return Response(serializer.data)
    except Exception as e:
        logger.error(f"Error updating cart item: {str(e)}")
        return Response(
            {'detail': f'Error updating cart item: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def clear_cart(request):
    """
    Clear all items from the user's cart
    """
    logger.info(f"Clearing cart for user {request.user.username}")

    try:
        # Get user's cart
        try:
            cart = Cart.objects.get(user=request.user)
            logger.info(f"Found cart {cart.id} for user {request.user.username}")
        except Cart.DoesNotExist:
            logger.error(f"No cart found for user {request.user.username}")
            return Response(
                {'detail': 'You do not have a cart'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Delete all cart items
        cart.items.all().delete()
        logger.info(f"Cleared all items from cart {cart.id}")

        # Return the updated cart
        serializer = CartSerializer(cart)
        return Response(serializer.data)
    except Exception as e:
        logger.error(f"Error clearing cart: {str(e)}")
        return Response(
            {'detail': f'Error clearing cart: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def cart_token_discount_info(request, cart_id):
    """
    Get token discount information for a specific cart
    """
    try:
        # Get the cart
        cart = get_object_or_404(Cart, id=cart_id, user=request.user)

        # Get user's wallet balance
        from wallet.models import Wallet
        wallet, created = Wallet.objects.get_or_create(user=request.user)

        # Calculate token discount info
        discount_info = cart.calculate_token_discount_info(wallet.balance)

        # Add user wallet info
        discount_info.update({
            'user_wallet_balance': wallet.balance,
            'user_wallet_balance_inr': float(wallet.balance_in_inr)
        })

        return Response(discount_info)

    except Exception as e:
        logger.error(f"Error getting cart token discount info: {str(e)}")
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )