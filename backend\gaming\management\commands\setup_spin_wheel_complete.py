from django.core.management.base import BaseCommand
from gaming.models import SpinWheelReward, SpinWheelSettings


class Command(BaseCommand):
    help = 'Complete setup of Spin Wheel with settings and rewards'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing data and create fresh setup',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🎡 Setting up complete Spin Wheel system...'))
        
        # Reset if requested
        if options['reset']:
            self.stdout.write('🔄 Resetting existing data...')
            SpinWheelReward.objects.all().delete()
            SpinWheelSettings.objects.all().delete()
            self.stdout.write(self.style.WARNING('Existing data cleared'))
        
        # Create settings
        settings_count = SpinWheelSettings.objects.count()
        if settings_count == 0:
            self.stdout.write('⚙️ Creating Spin Wheel settings...')
            settings = SpinWheelSettings.objects.create(
                cooldown_hours=24,
                wheel_segments=8,
                animation_duration=3000,
                min_token_reward=1,
                max_token_reward=50,
                scratch_card_probability=0.2,
                is_active=True,
                maintenance_mode=False,
                maintenance_message=""
            )
            self.stdout.write(self.style.SUCCESS('✅ Settings created'))
        else:
            self.stdout.write(self.style.WARNING(f'⚠️ Settings already exist ({settings_count} records)'))
        
        # Create rewards
        rewards_count = SpinWheelReward.objects.filter(is_active=True).count()
        if rewards_count == 0:
            self.stdout.write('🎁 Creating Spin Wheel rewards...')
            
            rewards_data = [
                {
                    'name': '5 Tokens',
                    'reward_type': 'tokens',
                    'value': 5,
                    'probability': 0.25,
                    'extra_data': {'color': '#FFD700', 'icon': '🪙'}
                },
                {
                    'name': '10 Tokens',
                    'reward_type': 'tokens',
                    'value': 10,
                    'probability': 0.20,
                    'extra_data': {'color': '#FF6B6B', 'icon': '💰'}
                },
                {
                    'name': '15 Tokens',
                    'reward_type': 'tokens',
                    'value': 15,
                    'probability': 0.15,
                    'extra_data': {'color': '#4ECDC4', 'icon': '💎'}
                },
                {
                    'name': '25 Tokens',
                    'reward_type': 'tokens',
                    'value': 25,
                    'probability': 0.10,
                    'extra_data': {'color': '#45B7D1', 'icon': '🎁'}
                },
                {
                    'name': 'Scratch Card',
                    'reward_type': 'scratch_card',
                    'value': 1,
                    'probability': 0.15,
                    'extra_data': {'color': '#96CEB4', 'icon': '🎫'}
                },
                {
                    'name': '5% Discount',
                    'reward_type': 'discount',
                    'value': 5,
                    'probability': 0.08,
                    'extra_data': {'color': '#FFEAA7', 'icon': '🏷️'}
                },
                {
                    'name': '10% Discount',
                    'reward_type': 'discount',
                    'value': 10,
                    'probability': 0.05,
                    'extra_data': {'color': '#DDA0DD', 'icon': '🎟️'}
                },
                {
                    'name': '2 Tokens',
                    'reward_type': 'tokens',
                    'value': 2,
                    'probability': 0.02,
                    'extra_data': {'color': '#F0F0F0', 'icon': '🥉'}
                }
            ]
            
            for reward_data in rewards_data:
                reward = SpinWheelReward.objects.create(
                    name=reward_data['name'],
                    reward_type=reward_data['reward_type'],
                    value=reward_data['value'],
                    probability=reward_data['probability'],
                    extra_data=reward_data['extra_data'],
                    is_active=True
                )
                self.stdout.write(f'  ✅ Created: {reward.name} ({reward.probability*100:.1f}%)')
            
            self.stdout.write(self.style.SUCCESS(f'✅ Created {len(rewards_data)} rewards'))
        else:
            self.stdout.write(self.style.WARNING(f'⚠️ Rewards already exist ({rewards_count} active records)'))
        
        # Verify setup
        final_settings = SpinWheelSettings.objects.count()
        final_rewards = SpinWheelReward.objects.filter(is_active=True).count()
        total_probability = sum(
            reward.probability for reward in SpinWheelReward.objects.filter(is_active=True)
        )
        
        self.stdout.write('\n📊 Setup Summary:')
        self.stdout.write(f'  • Settings: {final_settings}')
        self.stdout.write(f'  • Active Rewards: {final_rewards}')
        self.stdout.write(f'  • Total Probability: {total_probability:.3f}')
        
        if abs(total_probability - 1.0) > 0.01:
            self.stdout.write(self.style.WARNING(f'  ⚠️ Warning: Total probability should be close to 1.0'))
        
        if final_settings > 0 and final_rewards > 0:
            self.stdout.write(self.style.SUCCESS('\n🎉 SUCCESS! Spin Wheel is fully configured!'))
            self.stdout.write('You can now use the Spin Wheel in your frontend.')
        else:
            self.stdout.write(self.style.ERROR('\n❌ SETUP INCOMPLETE!'))
            
        # Show API endpoints
        self.stdout.write('\n🔗 Available API Endpoints:')
        self.stdout.write('  • GET  /api/gaming/spin-wheel/status/')
        self.stdout.write('  • POST /api/gaming/spin-wheel/spin/')
        self.stdout.write('  • POST /api/gaming/spin-wheel/reveal-scratch/')
        self.stdout.write('  • GET  /api/gaming/spin-wheel/history/')
