{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Service Control Dashboard{% endblock %}

{% block extrastyle %}
<style>
    .service-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 20px;
        background-color: #f9f9f9;
    }

    .service-status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 3px;
        font-weight: bold;
        margin-right: 10px;
    }

    .status-on {
        background-color: #dff0d8;
        color: #3c763d;
    }

    .status-off {
        background-color: #f2dede;
        color: #a94442;
    }

    .toggle-button {
        display: inline-block;
        padding: 8px 15px;
        border-radius: 3px;
        color: white;
        text-decoration: none;
        font-weight: bold;
        cursor: pointer;
    }

    .enable-button {
        background-color: #5cb85c;
    }

    .disable-button {
        background-color: #d9534f;
    }

    .service-description {
        margin-top: 10px;
        color: #666;
    }

    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        background-color: #333;
        z-index: 1000;
        display: none;
    }

    .toast-success {
        background-color: #5cb85c;
    }

    .toast-error {
        background-color: #d9534f;
    }
</style>
{% endblock %}

{% block content %}
<div id="content-main">
    <h1>Service Control Dashboard</h1>
    <p>Use this dashboard to enable or disable background services.</p>

    <div class="service-card">
        <h2>Celery Task Queue</h2>
        <div class="service-status {% if service_control.celery_enabled %}status-on{% else %}status-off{% endif %}">
            {% if service_control.celery_enabled %}ON{% else %}OFF{% endif %}
        </div>

        {% if service_control.celery_enabled %}
        <a href="{% url 'service_control_app:toggle-celery' %}" class="toggle-button disable-button" id="toggle-celery">Disable</a>
        {% else %}
        <a href="{% url 'service_control_app:toggle-celery' %}" class="toggle-button enable-button" id="toggle-celery">Enable</a>
        {% endif %}

        <div class="service-description">
            <p>Celery is responsible for processing background tasks such as sending emails, processing orders, and syncing with external services.</p>
            <p><strong>When disabled:</strong> Background tasks will not be processed. This can be useful for maintenance or debugging.</p>
        </div>
    </div>

    <div class="service-card">
        <h2>Redis Cache</h2>
        <div class="service-status {% if service_control.redis_enabled %}status-on{% else %}status-off{% endif %}">
            {% if service_control.redis_enabled %}ON{% else %}OFF{% endif %}
        </div>

        {% if service_control.redis_enabled %}
        <a href="{% url 'service_control_app:toggle-redis' %}" class="toggle-button disable-button" id="toggle-redis">Disable</a>
        {% else %}
        <a href="{% url 'service_control_app:toggle-redis' %}" class="toggle-button enable-button" id="toggle-redis">Enable</a>
        {% endif %}

        <div class="service-description">
            <p>Redis is used for caching, session storage, and as a message broker for Celery.</p>
            <p><strong>When disabled:</strong> Caching will be disabled and performance may be affected. Celery tasks may also be affected.</p>
        </div>
    </div>

    <div class="service-card">
        <h2>Service Status API</h2>
        <p>You can check the current status of services via the API endpoint:</p>
        <code>GET /service-control/status/</code>
        <p>Example response:</p>
        <pre>{
  "celery_enabled": {% if service_control.celery_enabled %}true{% else %}false{% endif %},
  "redis_enabled": {% if service_control.redis_enabled %}true{% else %}false{% endif %},
  "celery_status": "{% if service_control.celery_enabled %}ON{% else %}OFF{% endif %}",
  "redis_status": "{% if service_control.redis_enabled %}ON{% else %}OFF{% endif %}",
  "updated_at": "{{ service_control.updated_at|date:'c' }}"
}</pre>
    </div>
</div>

<div id="toast" class="toast"></div>

<script>
    // Function to show toast message
    function showToast(message, type) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = 'toast';

        if (type === 'success') {
            toast.classList.add('toast-success');
        } else if (type === 'error') {
            toast.classList.add('toast-error');
        }

        toast.style.display = 'block';

        // Hide toast after 3 seconds
        setTimeout(function() {
            toast.style.display = 'none';
        }, 3000);
    }

    // Function to toggle service via AJAX
    function toggleService(serviceType) {
        fetch(`/service-control/toggle-${serviceType}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                // Reload page to update UI
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            } else {
                showToast(data.message || `Failed to toggle ${serviceType}`, 'error');
            }
        })
        .catch(error => {
            showToast(`Error: ${error.message}`, 'error');
        });
    }

    // Add event listeners to toggle buttons
    document.getElementById('toggle-celery').addEventListener('click', function(e) {
        e.preventDefault();
        toggleService('celery');
    });

    document.getElementById('toggle-redis').addEventListener('click', function(e) {
        e.preventDefault();
        toggleService('redis');
    });
</script>
{% endblock %}
