#!/usr/bin/env python
"""
Test script to verify token collection system is working
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, Battle, PlayerStats
from wallet.models import Wallet, WalletTransaction
from django.conf import settings

def test_token_system():
    print("🎮 Testing Token Collection System...")
    print("=" * 50)
    
    # Create or get test user
    user, created = User.objects.get_or_create(
        username='testplayer',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'Player'
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing test user: {user.username}")
    
    # Create or get wallet
    wallet, created = Wallet.objects.get_or_create(user=user)
    initial_balance = wallet.balance
    print(f"💰 Initial wallet balance: {initial_balance} tokens")
    
    # Create game type
    game_type, created = GameType.objects.get_or_create(
        name='rock_paper_scissors',
        defaults={
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic rock paper scissors game',
            'is_active': True
        }
    )
    print(f"🎯 Game type: {game_type.name}")
    
    # Test 1: Create and win a battle
    print("\n🏆 Test 1: Winning a battle...")
    battle = Battle.objects.create(
        game_type=game_type,
        player1=user,
        player2=None,  # AI battle
        is_ai_battle=True,
        status='in_progress'
    )

    # Complete the battle with a win (this will automatically award tokens)
    battle.complete_battle('player1_win')

    print(f"✅ Battle {battle.id} completed - Player won!")
    print(f"💰 Awarded {battle.winner_tokens} tokens")
    
    # Test 2: Create and lose a battle
    print("\n😔 Test 2: Losing a battle...")
    battle2 = Battle.objects.create(
        game_type=game_type,
        player1=user,
        player2=None,  # AI battle
        is_ai_battle=True,
        status='in_progress'
    )

    # Complete the battle with a loss (AI wins)
    battle2.complete_battle('player2_win')

    print(f"✅ Battle {battle2.id} completed - Player lost!")
    print(f"💰 Awarded {battle2.loser_tokens} tokens (participation)")
    
    # Test 3: Create a draw battle
    print("\n🤝 Test 3: Draw battle...")
    battle3 = Battle.objects.create(
        game_type=game_type,
        player1=user,
        player2=None,  # AI battle
        is_ai_battle=True,
        status='in_progress'
    )

    # Complete the battle with a draw
    battle3.complete_battle('draw')

    print(f"✅ Battle {battle3.id} completed - Draw!")
    print(f"💰 Awarded {battle3.winner_tokens} tokens (draw)")
    
    # Check final balance
    wallet.refresh_from_db()
    final_balance = wallet.balance
    total_earned = final_balance - initial_balance
    
    print("\n" + "=" * 50)
    print("📊 FINAL RESULTS:")
    print(f"💰 Initial balance: {initial_balance} tokens")
    print(f"💰 Final balance: {final_balance} tokens")
    print(f"🎯 Total earned: {total_earned} tokens")
    print(f"💵 Token value: ₹{total_earned * 0.1:.2f}")
    
    # Show transaction history
    print("\n📋 TRANSACTION HISTORY:")
    transactions = WalletTransaction.objects.filter(wallet=wallet).order_by('-created_at')
    for i, tx in enumerate(transactions[:10], 1):
        print(f"{i}. {tx.transaction_type.upper()}: +{tx.amount} tokens")
        print(f"   📝 {tx.description}")
        print(f"   📅 {tx.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   💰 Balance after: {tx.balance_after} tokens")
        print()
    
    print("🎉 Token collection system is working perfectly!")
    return True

if __name__ == '__main__':
    test_token_system()
