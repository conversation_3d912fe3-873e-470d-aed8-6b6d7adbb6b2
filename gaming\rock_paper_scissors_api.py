"""
Rock Paper Scissors Game API
===========================

Comprehensive API endpoints for Rock Paper Scissors game with token management
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .game_session_service import GameSessionService
import json


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_rock_paper_scissors_game(request):
    """
    Start a new Rock Paper Scissors game with 2 token participation fee
    
    POST /api/gaming/rock-paper-scissors/start/
    """
    try:
        # Start game session using the comprehensive service
        result = GameSessionService.start_game_session(request.user, 'rock_paper_scissors')
        
        if result['success']:
            return Response({
                'success': True,
                'game_id': result['session_id'],
                'rounds': 3,  # Best of 3
                'can_play': True,
                'balance': result['balance'],
                'message': result['message'],
                'is_resume': result.get('is_resume', False)
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': False,
                'error': result['error'],
                'can_play': False,
                'balance': result.get('balance', 0)
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_rock_paper_scissors_game(request):
    """
    Complete a Rock Paper Scissors game with result and token calculation
    
    POST /api/gaming/rock-paper-scissors/complete/
    {
        "game_id": "uuid",
        "result": "win|loss|draw",
        "rounds": [
            {"player": "rock", "ai": "scissors", "winner": "player"},
            {"player": "paper", "ai": "rock", "winner": "player"},
            {"player": "scissors", "ai": "paper", "winner": "player"}
        ],
        "final_score": {"player": 3, "ai": 0},
        "duration": 45
    }
    """
    try:
        game_id = request.data.get('game_id')
        result = request.data.get('result')
        rounds = request.data.get('rounds', [])
        final_score = request.data.get('final_score', {})
        duration = request.data.get('duration', 0)

        if not game_id:
            return Response({
                'success': False,
                'error': 'game_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not result:
            return Response({
                'success': False,
                'error': 'result is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        valid_results = ['win', 'loss', 'draw']
        if result not in valid_results:
            return Response({
                'success': False,
                'error': f'Invalid result. Must be one of: {", ".join(valid_results)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Prepare game data
        game_data = {
            'rounds': rounds,
            'final_score': final_score,
            'duration': duration,
            'total_rounds': len(rounds)
        }

        # Complete game session
        completion_result = GameSessionService.complete_game_session(game_id, result, game_data)

        if completion_result['success']:
            return Response({
                'success': True,
                'tokens_earned': completion_result['tokens_change'],
                'new_balance': completion_result['new_balance'],
                'balance_in_inr': completion_result['new_balance'] * 0.1,
                'transaction_type': f'rock_paper_scissors_{result}',
                'description': completion_result['message'],
                'can_play_more': completion_result['new_balance'] >= 2,
                'status': completion_result['status'],
                'result': completion_result['result']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': completion_result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_rock_paper_scissors_game(request):
    """
    Forfeit an active Rock Paper Scissors game
    
    POST /api/gaming/rock-paper-scissors/forfeit/
    {
        "game_id": "uuid"
    }
    """
    try:
        game_id = request.data.get('game_id')

        if not game_id:
            return Response({
                'success': False,
                'error': 'game_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Forfeit game session
        result = GameSessionService.forfeit_game_session(game_id)

        if result['success']:
            return Response({
                'success': True,
                'tokens_lost': abs(result['tokens_change']),
                'new_balance': result['new_balance'],
                'message': result['message']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_rock_paper_scissors_stats(request):
    """
    Get Rock Paper Scissors game statistics for the user
    
    GET /api/gaming/rock-paper-scissors/stats/
    """
    try:
        # Get game history for Rock Paper Scissors
        history = GameSessionService.get_user_game_history(
            request.user, 
            game_type='rock_paper_scissors', 
            limit=50
        )

        # Calculate statistics
        total_games = len(history)
        wins = len([h for h in history if h['result'] == 'win'])
        losses = len([h for h in history if h['result'] == 'loss'])
        draws = len([h for h in history if h['result'] == 'draw'])
        forfeits = len([h for h in history if h['result'] == 'forfeit'])

        total_tokens_earned = sum(h['tokens_change'] for h in history if h['tokens_change'] > 0)
        total_tokens_lost = sum(abs(h['tokens_change']) for h in history if h['tokens_change'] < 0)

        # Get current balance
        from wallet.models import Wallet
        try:
            wallet = Wallet.objects.get(user=request.user)
            current_balance = wallet.balance
        except Wallet.DoesNotExist:
            current_balance = 0

        return Response({
            'success': True,
            'stats': {
                'total_games': total_games,
                'wins': wins,
                'losses': losses,
                'draws': draws,
                'forfeits': forfeits,
                'win_rate': round((wins / total_games * 100), 2) if total_games > 0 else 0,
                'total_tokens_earned': total_tokens_earned,
                'total_tokens_lost': total_tokens_lost,
                'net_tokens': total_tokens_earned - total_tokens_lost,
                'current_balance': current_balance,
                'balance_in_inr': current_balance * 0.1
            },
            'recent_games': history[:10]  # Last 10 games
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_rock_paper_scissors_session(request, game_id):
    """
    Get details of a specific Rock Paper Scissors game session
    
    GET /api/gaming/rock-paper-scissors/session/{game_id}/
    """
    try:
        result = GameSessionService.get_session_status(game_id)

        if result['success']:
            # Verify this is a Rock Paper Scissors session
            if result['game_type'] != 'rock_paper_scissors':
                return Response({
                    'success': False,
                    'error': 'Session is not a Rock Paper Scissors game'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
