@echo off
echo 🔧 DEPLOYING PRODUCTION FIXES
echo =============================
echo.

echo 📋 This script will:
echo    1. Push frontend fixes (3 games visible)
echo    2. Push backend fixes (signup bonus, token discounts)
echo    3. Provide instructions for running fixes on production
echo.

REM Check if we're in the right directory
if not exist "backend" (
    echo ❌ ERROR: Backend folder not found!
    echo Please run this script from your project root directory
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ ERROR: Frontend folder not found!
    echo Please run this script from your project root directory
    pause
    exit /b 1
)

echo 🎨 STEP 1: Pushing Frontend Fixes
echo ==================================
cd frontend

echo 📦 Adding frontend changes...
git add .

echo 💾 Committing frontend fixes...
git commit -m "🎮 Fix: Show all 3 games in gaming lobby

✨ Fixes Applied:
- Added Rock Paper Scissors game card
- Added Number Guessing game card  
- All 3 games now visible: T<PERSON>, Rock Paper Scissors, Number Guessing
- Consistent reward display and game rules
- Beautiful gradient designs for each game
- Proper matchmaking integration for battle games

🎯 User Experience:
- Users can now see and access all available games
- Clear reward structure displayed for each game
- Engaging game descriptions and rules
- Consistent modern design across all game cards

🚀 Ready for production deployment!"

echo 🚀 Pushing frontend to GitHub...
git push origin main

if %errorlevel% equ 0 (
    echo ✅ Frontend fixes pushed successfully!
) else (
    echo ❌ Frontend push failed
    pause
    exit /b 1
)

cd ..

echo.
echo 🔧 STEP 2: Pushing Backend Fixes
echo =================================
cd backend

echo 📦 Adding backend changes...
git add .

echo 💾 Committing backend fixes...
git commit -m "🔧 Fix: Production issues - signup bonus, token discounts, games

✨ Fixes Applied:
- Enhanced signup bonus system with error handling
- Added management commands for fixing existing users
- Created production fix script for all issues
- Improved wallet creation with proper error handling
- Added token discount enablement for products

🪙 Signup Bonus Fixes:
- Robust wallet creation for new users
- 100 token signup bonus for all users
- Retroactive bonus for existing users
- Error handling and logging

💰 Token Discount Fixes:
- Management command to enable token discounts
- 20% discount with ₹100 max cap
- Automatic product configuration
- Checkout integration verification

🎮 Gaming System Fixes:
- Ensured all 3 game types exist in database
- Rock Paper Scissors, Number Guessing, Tic Tac Toe
- Proper game configuration and rules

🚀 Production Ready:
- fix_production_issues.py script for deployment
- Management commands for maintenance
- Comprehensive verification system
- Ready for immediate deployment"

echo 🚀 Pushing backend to GitHub...
git push origin main

if %errorlevel% equ 0 (
    echo ✅ Backend fixes pushed successfully!
) else (
    echo ❌ Backend push failed
    pause
    exit /b 1
)

cd ..

echo.
echo 🎉 SUCCESS: All fixes pushed to GitHub!
echo ========================================
echo.
echo 📋 NEXT STEPS FOR PRODUCTION:
echo ==============================
echo.
echo 1. 🌐 FRONTEND DEPLOYMENT:
echo    - Your frontend will auto-deploy from GitHub
echo    - Check: https://your-frontend.onrender.com
echo    - Verify: All 3 games are visible in gaming section
echo.
echo 2. 🔧 BACKEND DEPLOYMENT:
echo    - Your backend will auto-deploy from GitHub
echo    - Check: https://your-backend.onrender.com
echo.
echo 3. 🔧 RUN PRODUCTION FIXES:
echo    After backend deployment, run this command in Render console:
echo.
echo    python fix_production_issues.py
echo.
echo    This will:
echo    - Give 100 tokens to all users (signup bonus)
echo    - Enable token discounts on all products
echo    - Create all 3 game types in database
echo    - Verify everything is working
echo.
echo 4. ✅ VERIFICATION:
echo    - Login to your site
echo    - Check wallet has 100 tokens
echo    - See all 3 games in gaming section
echo    - Try token discount in checkout
echo.
echo 🎮 EXPECTED RESULTS:
echo ===================
echo ✅ Gaming: 3 games visible (Tic Tac Toe, Rock Paper Scissors, Number Guessing)
echo ✅ Tokens: All users have 100 token signup bonus
echo ✅ Shopping: Token discounts work in checkout
echo ✅ Rewards: Proper token rewards for all games
echo.
echo 🚀 Your gaming e-commerce platform is now complete!
echo.
pause
