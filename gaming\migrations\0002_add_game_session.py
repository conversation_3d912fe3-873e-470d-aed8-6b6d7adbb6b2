# Generated manually for GameSession model

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('gaming', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GameSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('game_type', models.CharField(choices=[('tic_tac_toe', 'Tic Tac Toe'), ('color_match', 'Color Match'), ('memory_card', 'Memory Card Match'), ('number_guessing', 'Number Guessing'), ('rock_paper_scissors', 'Rock Paper Scissors')], max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('incomplete', 'Incomplete/Forfeited'), ('pending_replay', 'Pending Replay (Draw)')], default='active', max_length=20)),
                ('result', models.CharField(blank=True, choices=[('win', 'Win'), ('loss', 'Loss'), ('draw', 'Draw'), ('forfeit', 'Forfeit')], max_length=10, null=True)),
                ('participation_tokens_deducted', models.IntegerField(default=2)),
                ('tokens_awarded', models.IntegerField(default=0)),
                ('net_token_change', models.IntegerField(default=-2)),
                ('game_data', models.JSONField(blank=True, default=dict)),
                ('moves_count', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='game_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='gamesession',
            index=models.Index(fields=['user', 'game_type'], name='gaming_game_user_ga_b8e5a8_idx'),
        ),
        migrations.AddIndex(
            model_name='gamesession',
            index=models.Index(fields=['status'], name='gaming_game_status_4b8c9a_idx'),
        ),
        migrations.AddIndex(
            model_name='gamesession',
            index=models.Index(fields=['created_at'], name='gaming_game_created_2f4a1b_idx'),
        ),
    ]
