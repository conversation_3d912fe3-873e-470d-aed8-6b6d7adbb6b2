import React, { useState, useEffect, useCallback } from 'react';
import { gameSessionService } from '../../services/gameSessionService';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';

type Cell = 'X' | 'O' | null;
type Board = Cell[];
type GameStatus = 'waiting' | 'playing' | 'won' | 'lost' | 'draw';

interface GameState {
  board: Board;
  isPlayerTurn: boolean;
  gameStatus: GameStatus;
  sessionId: string | null;
  winningLine: number[];
  isResumedGame: boolean;
}

const ModernTicTacToe: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  
  const [gameState, setGameState] = useState<GameState>({
    board: Array(9).fill(null),
    isPlayerTurn: true,
    gameStatus: 'waiting',
    sessionId: null,
    winningLine: [],
    isResumedGame: false
  });

  const [showResult, setShowResult] = useState(false);
  const [gameResult, setGameResult] = useState<'win' | 'loss' | 'draw' | 'forfeit' | null>(null);
  const [tokensEarned, setTokensEarned] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);

  // Handle page unload/exit warning
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (gameState.gameStatus === 'playing' && gameState.sessionId) {
        e.preventDefault();
        e.returnValue = 'Exiting now will forfeit your participation tokens!';
        return 'Exiting now will forfeit your participation tokens!';
      }
    };

    const handleUnload = () => {
      if (gameState.gameStatus === 'playing' && gameState.sessionId) {
        // Forfeit the game session on unload
        gameSessionService.forfeitGameSession(gameState.sessionId);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [gameState.gameStatus, gameState.sessionId]);

  // Check for winner
  const checkWinner = useCallback((board: Board): { winner: Cell; line: number[] } => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8], // Rows
      [0, 3, 6], [1, 4, 7], [2, 5, 8], // Columns
      [0, 4, 8], [2, 4, 6] // Diagonals
    ];

    for (const line of lines) {
      const [a, b, c] = line;
      if (board[a] && board[a] === board[b] && board[a] === board[c]) {
        return { winner: board[a], line };
      }
    }
    return { winner: null, line: [] };
  }, []);

  // AI move logic (hard mode)
  const getAIMove = useCallback((board: Board): number => {
    // Check for winning move
    for (let i = 0; i < 9; i++) {
      if (!board[i]) {
        const testBoard = [...board];
        testBoard[i] = 'O';
        if (checkWinner(testBoard).winner === 'O') return i;
      }
    }

    // Block player winning move
    for (let i = 0; i < 9; i++) {
      if (!board[i]) {
        const testBoard = [...board];
        testBoard[i] = 'X';
        if (checkWinner(testBoard).winner === 'X') return i;
      }
    }

    // Take center if available
    if (!board[4]) return 4;

    // Take corners
    const corners = [0, 2, 6, 8];
    const availableCorners = corners.filter(i => !board[i]);
    if (availableCorners.length > 0) {
      return availableCorners[Math.floor(Math.random() * availableCorners.length)];
    }

    // Take any available space
    const available = board.map((cell, i) => cell === null ? i : null).filter(i => i !== null);
    return available[Math.floor(Math.random() * available.length)] || 0;
  }, [checkWinner]);

  // Start new game
  const startGame = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await gameSessionService.startGameSession('tic_tac_toe');

      if (result.success) {
        setGameState({
          board: Array(9).fill(null),
          isPlayerTurn: true,
          gameStatus: 'playing',
          sessionId: result.session_id,
          winningLine: [],
          isResumedGame: result.is_resume
        });

        setShowResult(false);
        setShowCelebration(false);

        // Refresh wallet to show updated balance
        await refreshWallet();

        // Show message about token deduction or resume
        if (result.is_resume) {
          console.log('Resumed game:', result.message);
        } else {
          console.log('New game started:', result.message);
        }
      } else {
        alert(result.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      alert('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  };

  // Complete game
  const completeGame = async (result: 'win' | 'loss' | 'draw') => {
    if (!gameState.sessionId) return;

    try {
      // Prepare game data
      const gameData = {
        difficulty: 'hard',
        board_state: gameState.board,
        winning_line: gameState.winningLine,
        total_moves: gameState.board.filter(cell => cell !== null).length
      };

      const submitResult = await gameSessionService.completeGameSession(
        gameState.sessionId,
        result,
        gameData
      );

      if (submitResult.success) {
        setTokensEarned(submitResult.tokens_earned);
        setGameResult(result);
        await refreshWallet();

        // Handle draw case
        if (result === 'draw') {
          gameSessionService.handleDrawContinuation(
            () => {
              // Continue playing - start new round with same session
              setGameState(prev => ({
                ...prev,
                board: Array(9).fill(null),
                isPlayerTurn: true,
                gameStatus: 'playing',
                winningLine: []
              }));
              setShowResult(false);
              return;
            },
            () => {
              // Forfeit the draw game
              gameSessionService.forfeitGameSession(gameState.sessionId!);
              setShowResult(true);
            }
          );
          return; // Don't show result immediately for draws
        }
      } else {
        console.error('Error completing game:', submitResult.error);
      }
    } catch (error) {
      console.error('Error completing game:', error);
    }

    setShowResult(true);
  };

  // Forfeit game
  const forfeitGame = () => {
    if (!gameState.sessionId) return;

    gameSessionService.showExitWarning(
      async () => {
        // User confirmed forfeit
        try {
          const result = await gameSessionService.forfeitGameSession(gameState.sessionId!);
          if (result.success) {
            setTokensEarned(result.tokens_earned);
            setGameResult('forfeit');
            await refreshWallet();
            setShowResult(true);
          }
        } catch (error) {
          console.error('Error forfeiting game:', error);
        }
      },
      () => {
        // User cancelled forfeit - continue playing
        console.log('Forfeit cancelled');
      }
    );
  };

  // Handle player move
  const handleCellClick = (index: number) => {
    if (
      gameState.board[index] || 
      !gameState.isPlayerTurn || 
      gameState.gameStatus !== 'playing' ||
      isLoading
    ) return;

    const newBoard = [...gameState.board];
    newBoard[index] = 'X';
    
    const { winner, line } = checkWinner(newBoard);
    
    if (winner === 'X') {
      setGameState(prev => ({
        ...prev,
        board: newBoard,
        gameStatus: 'won',
        winningLine: line
      }));
      setShowCelebration(true);
      setTimeout(() => setShowCelebration(false), 3000);
      completeGame('win');
      return;
    }

    // Check for draw
    if (newBoard.every(cell => cell !== null)) {
      setGameState(prev => ({
        ...prev,
        board: newBoard,
        gameStatus: 'draw'
      }));
      completeGame('draw');
      return;
    }

    // AI turn
    setGameState(prev => ({
      ...prev,
      board: newBoard,
      isPlayerTurn: false
    }));

    setTimeout(() => {
      const aiMove = getAIMove(newBoard);
      const aiBoard = [...newBoard];
      aiBoard[aiMove] = 'O';

      const { winner: aiWinner, line: aiLine } = checkWinner(aiBoard);
      
      if (aiWinner === 'O') {
        setGameState(prev => ({
          ...prev,
          board: aiBoard,
          gameStatus: 'lost',
          winningLine: aiLine
        }));
        completeGame('loss');
      } else if (aiBoard.every(cell => cell !== null)) {
        setGameState(prev => ({
          ...prev,
          board: aiBoard,
          gameStatus: 'draw'
        }));
        completeGame('draw');
      } else {
        setGameState(prev => ({
          ...prev,
          board: aiBoard,
          isPlayerTurn: true
        }));
      }
    }, 1000);
  };

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h2 className="text-3xl font-bold mb-6">⭕ Game Complete!</h2>
          
          <div className="mb-6">
            {gameResult === 'win' && (
              <div className="text-green-400">
                <div className="text-6xl mb-4">🎉</div>
                <div className="text-2xl font-bold">You Won!</div>
                <div className="text-lg">+{tokensEarned} tokens (Net: +{tokensEarned > 0 ? tokensEarned - 2 : tokensEarned} tokens)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens, Win bonus: +5 tokens</div>
              </div>
            )}
            {gameResult === 'loss' && (
              <div className="text-red-400">
                <div className="text-6xl mb-4">😔</div>
                <div className="text-2xl font-bold">You Lost</div>
                <div className="text-lg">{tokensEarned} tokens (Net: {tokensEarned - 2} tokens)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens, Loss penalty: -1 token</div>
              </div>
            )}
            {gameResult === 'draw' && (
              <div className="text-yellow-400">
                <div className="text-6xl mb-4">🤝</div>
                <div className="text-2xl font-bold">Draw!</div>
                <div className="text-lg">Must replay (No token change)</div>
                <div className="text-sm opacity-75 mt-2">Participation tokens held until win/loss</div>
              </div>
            )}
            {gameResult === 'forfeit' && (
              <div className="text-orange-400">
                <div className="text-6xl mb-4">🚪</div>
                <div className="text-2xl font-bold">Game Forfeited</div>
                <div className="text-lg">{tokensEarned} tokens (Participation fee lost)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens</div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Current Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Play Again'}
          </button>
        </div>
      </div>
    );
  }

  if (gameState.gameStatus === 'waiting') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h1 className="text-4xl font-bold mb-4">⭕ Tic Tac Toe</h1>
          <p className="text-lg mb-6 opacity-90">
            Get 3 in a row to win! Challenge the AI in this classic strategy game.
          </p>
          
          <div className="mb-6">
            <div className="text-sm opacity-75">Your Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <div className="mb-6 text-sm opacity-75">
            <div>Win: +5 tokens</div>
            <div>Draw: +2 tokens</div>
            <div>Lose: -1 token</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Start Game'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-600 p-4">
      <div className="max-w-2xl mx-auto">
        {/* Game Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 text-white text-center">
          <h2 className="text-2xl font-bold mb-2">⭕ Tic Tac Toe</h2>
          <div className="text-lg">
            {gameState.isPlayerTurn ? "Your Turn" : "AI's Turn"}
          </div>
          <div className="text-sm opacity-75 mt-2">Hard Mode - AI plays optimally!</div>
        </div>

        {/* Game Board */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-6">
          <div className="grid grid-cols-3 gap-3 max-w-xs mx-auto">
            {gameState.board.map((cell, index) => (
              <button
                key={index}
                onClick={() => handleCellClick(index)}
                disabled={
                  isLoading || 
                  !gameState.isPlayerTurn || 
                  gameState.gameStatus !== 'playing' || 
                  cell !== null
                }
                className={`
                  aspect-square bg-white/20 backdrop-blur-sm
                  border-2 border-white/30 rounded-xl
                  flex items-center justify-center text-4xl font-bold
                  hover:bg-white/30 hover:border-white/50
                  transition-all duration-200 transform hover:scale-105
                  disabled:cursor-not-allowed disabled:hover:scale-100 disabled:opacity-50
                  ${gameState.winningLine.includes(index) ? 'bg-green-400/30 border-green-400' : ''}
                  ${cell ? 'cursor-not-allowed' : 'cursor-pointer'}
                `}
              >
                <span className={cell === 'X' ? 'text-blue-300' : 'text-pink-300'}>
                  {cell}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Game Status and Controls */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-white text-center">
          <div className="text-lg font-semibold mb-4">
            {gameState.gameStatus === 'won' && '🎉 You Won!'}
            {gameState.gameStatus === 'lost' && '😔 AI Won!'}
            {gameState.gameStatus === 'draw' && '🤝 Draw!'}
            {gameState.gameStatus === 'playing' && (
              gameState.isPlayerTurn ? 'Choose your move...' : 'AI is thinking...'
            )}
          </div>

          {/* Game Controls */}
          {gameState.gameStatus === 'playing' && (
            <div className="flex justify-center space-x-4">
              <button
                onClick={forfeitGame}
                className="px-4 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-200"
              >
                ⚠️ Forfeit Game
              </button>
            </div>
          )}

          {/* Token Information */}
          <div className="mt-4 text-sm opacity-75">
            <div>Current Balance: {wallet?.balance || 0} tokens</div>
            {gameState.isResumedGame && (
              <div className="text-yellow-300 mt-1">📍 Resumed Game - No additional tokens deducted</div>
            )}
          </div>
        </div>

        {/* Celebration Effect */}
        {showCelebration && (
          <div className="fixed inset-0 pointer-events-none flex items-center justify-center z-50">
            <div className="text-8xl animate-bounce">🎉</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernTicTacToe;
