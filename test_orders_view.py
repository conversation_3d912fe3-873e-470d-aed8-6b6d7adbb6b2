#!/usr/bin/env python3
"""
Test orders view directly
"""
import os
import django
from django.test import RequestFactory
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

def test_orders_view():
    print("🔍 Testing Orders View...")
    
    try:
        from orders.views import OrderViewSet
        from orders.models import Order
        from django.contrib.auth.models import AnonymousUser
        
        # Create a test request
        factory = RequestFactory()
        request = factory.get('/api/orders/')
        request.user = AnonymousUser()
        
        # Test the view
        view = OrderViewSet()
        view.request = request
        
        print("✅ Orders view can be instantiated")
        
        # Check if we have any orders
        order_count = Order.objects.count()
        print(f"📦 Total orders in database: {order_count}")
        
        # Check if we have any users
        User = get_user_model()
        user_count = User.objects.count()
        print(f"👥 Total users in database: {user_count}")
        
        if user_count > 0:
            # Test with a real user
            user = User.objects.first()
            request.user = user
            
            user_orders = Order.objects.filter(user=user).count()
            print(f"📦 Orders for user {user.email}: {user_orders}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing orders view: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

def test_jwt_authentication():
    print("\n🔍 Testing JWT Authentication...")
    
    try:
        from rest_framework_simplejwt.tokens import AccessToken
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        if User.objects.count() == 0:
            print("⚠️ No users found. Creating a test user...")
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
            print(f"✅ Created test user: {user.email}")
        else:
            user = User.objects.first()
            print(f"✅ Using existing user: {user.email}")
        
        # Generate a valid token
        token = AccessToken.for_user(user)
        print(f"✅ Generated valid JWT token: {str(token)[:50]}...")
        
        return str(token)
        
    except Exception as e:
        print(f"❌ Error with JWT authentication: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_api_with_valid_token():
    print("\n🔍 Testing API with valid token...")
    
    try:
        import requests
        
        # Get a valid token
        token = test_jwt_authentication()
        if not token:
            print("❌ Could not generate valid token")
            return False
        
        # Test the API
        headers = {
            'Authorization': f'JWT {token}',
            'Content-Type': 'application/json'
        }
        
        response = requests.get('http://localhost:8000/api/orders/', headers=headers, timeout=5)
        
        print(f"📡 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API call successful!")
            print(f"   Response keys: {list(data.keys())}")
            if 'results' in data:
                print(f"   Orders count: {len(data['results'])}")
            return True
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Django server")
        print("   Make sure Django server is running: python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Error testing API: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Orders View Diagnostic Tool")
    print("=" * 50)
    
    # Test orders view
    view_success = test_orders_view()
    
    # Test JWT authentication
    token = test_jwt_authentication()
    
    # Test API with valid token
    api_success = test_api_with_valid_token()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print(f"   Orders View: {'✅ Working' if view_success else '❌ Failed'}")
    print(f"   JWT Auth: {'✅ Working' if token else '❌ Failed'}")
    print(f"   API Test: {'✅ Working' if api_success else '❌ Failed'}")
    
    if api_success:
        print("\n🎉 SOLUTION:")
        print("   The API is working correctly!")
        print("   The issue is likely with the frontend token.")
        print("   Try logging out and logging back in to get a fresh token.")
    else:
        print("\n🔧 NEXT STEPS:")
        print("   1. Check Django server logs for specific errors")
        print("   2. Verify all migrations are applied")
        print("   3. Check if all required packages are installed")
