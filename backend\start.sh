#!/bin/bash

# Start script for Render deployment
# This script changes to the backend directory and runs the start commands

echo "🚀 Starting application..."
echo "📁 Current directory: $(pwd)"

echo "🔄 Changing to backend directory..."
cd backend

echo "📁 Now in directory: $(pwd)"

echo "🗄️ Running migrations..."
python manage.py migrate

echo "🎮 Setting up games..."
python manage.py setup_games

echo "🌐 Starting Gunicorn server..."
gunicorn dropshipping_backend.wsgi:application --bind 0.0.0.0:$PORT --workers 2 --timeout 120
