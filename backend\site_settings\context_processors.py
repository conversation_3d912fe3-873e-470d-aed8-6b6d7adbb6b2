from .models import SiteSettings

def site_settings(request):
    """
    Context processor that adds site settings to all templates.
    
    This makes the site logo and favicon available in all templates.
    """
    try:
        # Get the first (and only) SiteSettings instance
        settings = SiteSettings.objects.first()
        return {
            'site_settings': settings
        }
    except Exception:
        # Return an empty dict if there's an error
        return {
            'site_settings': None
        }
