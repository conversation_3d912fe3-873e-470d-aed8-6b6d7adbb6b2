#!/usr/bin/env python
"""
Sync All Django Apps
====================

This script ensures all Django apps have all their required files
by copying missing files from the backend directory.
"""

import os
import shutil
import subprocess

# List of Django apps to sync
DJANGO_APPS = [
    'accounts',
    'contact', 
    'customer_communication',
    'gaming',
    'order_tracking',
    'orders',
    'printify',
    'products',
    'razorpay_settings',
    'returns',
    'service_control',
    'site_settings',
    'team_members',
    'wallet'
]

def sync_app_files(app_name):
    """Sync all files from backend/app to root/app"""
    backend_app_path = f"backend/{app_name}"
    root_app_path = app_name
    
    if not os.path.exists(backend_app_path):
        print(f"⚠️ Backend app not found: {backend_app_path}")
        return
    
    if not os.path.exists(root_app_path):
        print(f"📁 Creating app directory: {root_app_path}")
        os.makedirs(root_app_path, exist_ok=True)
    
    print(f"🔄 Syncing {app_name}...")
    
    # Get all files in backend app
    for root, dirs, files in os.walk(backend_app_path):
        # Calculate relative path
        rel_path = os.path.relpath(root, backend_app_path)
        
        # Create corresponding directory in root app
        if rel_path != '.':
            target_dir = os.path.join(root_app_path, rel_path)
            os.makedirs(target_dir, exist_ok=True)
        else:
            target_dir = root_app_path
        
        # Copy files
        for file in files:
            source_file = os.path.join(root, file)
            if rel_path != '.':
                target_file = os.path.join(root_app_path, rel_path, file)
            else:
                target_file = os.path.join(root_app_path, file)
            
            # Skip __pycache__ files
            if '__pycache__' in source_file:
                continue
                
            # Copy file if it doesn't exist or is different
            if not os.path.exists(target_file):
                try:
                    shutil.copy2(source_file, target_file)
                    print(f"   ✅ Copied: {file}")
                except Exception as e:
                    print(f"   ❌ Failed to copy {file}: {e}")
            else:
                # Check if files are different
                try:
                    with open(source_file, 'r', encoding='utf-8') as f1:
                        source_content = f1.read()
                    with open(target_file, 'r', encoding='utf-8') as f2:
                        target_content = f2.read()
                    
                    if source_content != target_content:
                        shutil.copy2(source_file, target_file)
                        print(f"   🔄 Updated: {file}")
                except:
                    # If we can't read as text, copy anyway
                    shutil.copy2(source_file, target_file)
                    print(f"   🔄 Updated (binary): {file}")

def main():
    """Sync all Django apps"""
    print("🔄 Syncing All Django Apps")
    print("=" * 40)
    
    for app in DJANGO_APPS:
        sync_app_files(app)
        print()
    
    print("✅ All apps synced!")
    print("\n📋 Next steps:")
    print("1. git add .")
    print("2. git commit -m '🔧 Sync all Django apps with complete files'")
    print("3. git push origin master")

if __name__ == '__main__':
    main()
