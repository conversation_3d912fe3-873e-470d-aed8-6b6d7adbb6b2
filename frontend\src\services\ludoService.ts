import { apiClient } from './api';

export interface LudoGameSettings {
  difficulty: 'easy' | 'medium' | 'hard';
  num_tokens: number;
  mode: 'vs_bot' | 'vs_user';
}

export interface LudoMove {
  token: string;
  from: number;
  to: number;
  type: 'normal' | 'exit_home';
}

export interface LudoGameState {
  board: { [key: string]: string | null };
  current_player: 'player1' | 'player2';
  dice_value: number;
  game_timer: number;
  start_time: string;
  player1_moves: number;
  player2_moves: number;
  player1_tokens: { [key: string]: any };
  player2_tokens: { [key: string]: any };
  status: 'active' | 'completed' | 'draw';
  winner: string | null;
  last_move: any;
  difficulty: string;
  num_tokens: number;
  game_mode: string;
  session_id: string;
}

export interface LudoGameResponse {
  success: boolean;
  game_id: string;
  game_state: LudoGameState;
  difficulty: string;
  num_tokens: number;
  game_mode: string;
  balance: number;
  message: string;
  error?: string;
}

export interface DiceRollResponse {
  success: boolean;
  dice_value: number;
  timestamp: string;
  error?: string;
}

export interface ValidMovesResponse {
  success: boolean;
  valid_moves: LudoMove[];
  player: string;
  error?: string;
}

export interface MakeMoveResponse {
  success: boolean;
  game_state: LudoGameState;
  game_ended: boolean;
  timestamp: string;
  error?: string;
}

export interface BotMoveResponse {
  success: boolean;
  bot_move: LudoMove | null;
  game_state: LudoGameState;
  timestamp: string;
  message?: string;
  error?: string;
}

export interface CompleteGameResponse {
  success: boolean;
  tokens_earned: number;
  new_balance: number;
  message: string;
  game_result: 'win' | 'loss' | 'draw';
  player1_moves: number;
  player2_moves: number;
  error?: string;
}

export interface ForfeitGameResponse {
  success: boolean;
  tokens_earned: number;
  new_balance: number;
  message: string;
  error?: string;
}

class LudoService {
  private baseUrl = '/api/gaming/ludo';

  /**
   * Start a new Ludo game
   */
  async startGame(settings: LudoGameSettings): Promise<LudoGameResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/start/`, settings);
      return response.data;
    } catch (error: any) {
      console.error('Error starting Ludo game:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to start game',
        game_id: '',
        game_state: {} as LudoGameState,
        difficulty: settings.difficulty,
        num_tokens: settings.num_tokens,
        game_mode: settings.mode,
        balance: 0,
        message: ''
      };
    }
  }

  /**
   * Roll dice for the current player
   */
  async rollDice(gameId: string): Promise<DiceRollResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/roll-dice/`, {
        game_id: gameId
      });
      return response.data;
    } catch (error: any) {
      console.error('Error rolling dice:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to roll dice',
        dice_value: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get valid moves for a player
   */
  async getValidMoves(gameState: LudoGameState, player: string): Promise<ValidMovesResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/valid-moves/`, {
        game_state: gameState,
        player: player
      });
      return response.data;
    } catch (error: any) {
      console.error('Error getting valid moves:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get valid moves',
        valid_moves: [],
        player: player
      };
    }
  }

  /**
   * Make a move in the game
   */
  async makeMove(gameId: string, move: LudoMove, gameState: LudoGameState): Promise<MakeMoveResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/make-move/`, {
        game_id: gameId,
        move: move,
        game_state: gameState
      });
      return response.data;
    } catch (error: any) {
      console.error('Error making move:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to make move',
        game_state: gameState,
        game_ended: false,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get bot move
   */
  async getBotMove(gameState: LudoGameState, difficulty: string): Promise<BotMoveResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/bot-move/`, {
        game_state: gameState,
        difficulty: difficulty
      });
      return response.data;
    } catch (error: any) {
      console.error('Error getting bot move:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get bot move',
        bot_move: null,
        game_state: gameState,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Complete a game and handle token transactions
   */
  async completeGame(
    gameId: string, 
    result: 'win' | 'loss' | 'draw', 
    gameState: LudoGameState
  ): Promise<CompleteGameResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/complete/`, {
        game_id: gameId,
        result: result,
        game_state: gameState
      });
      return response.data;
    } catch (error: any) {
      console.error('Error completing game:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to complete game',
        tokens_earned: 0,
        new_balance: 0,
        message: '',
        game_result: result,
        player1_moves: gameState.player1_moves,
        player2_moves: gameState.player2_moves
      };
    }
  }

  /**
   * Forfeit a game
   */
  async forfeitGame(gameId: string): Promise<ForfeitGameResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/forfeit/`, {
        game_id: gameId
      });
      return response.data;
    } catch (error: any) {
      console.error('Error forfeiting game:', error);
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to forfeit game',
        tokens_earned: -2,
        new_balance: 0,
        message: 'Game forfeited'
      };
    }
  }

  /**
   * Calculate game result based on moves
   */
  calculateGameResult(player1Moves: number, player2Moves: number): 'win' | 'loss' | 'draw' {
    if (player1Moves > player2Moves) {
      return 'win';
    } else if (player2Moves > player1Moves) {
      return 'loss';
    } else {
      return 'draw';
    }
  }

  /**
   * Format game duration
   */
  formatGameDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Get token reward text
   */
  getTokenRewardText(result: 'win' | 'loss' | 'draw'): string {
    switch (result) {
      case 'win':
        return '+5 tokens (Win bonus)';
      case 'loss':
        return '-1 token (Loss penalty)';
      case 'draw':
        return 'Replay required (Same moves)';
      default:
        return '';
    }
  }

  /**
   * Validate game settings
   */
  validateGameSettings(settings: LudoGameSettings): { valid: boolean; error?: string } {
    if (![2, 4].includes(settings.num_tokens)) {
      return { valid: false, error: 'Number of tokens must be 2 or 4' };
    }
    
    if (!['easy', 'medium', 'hard'].includes(settings.difficulty)) {
      return { valid: false, error: 'Invalid difficulty level' };
    }
    
    if (!['vs_bot', 'vs_user'].includes(settings.mode)) {
      return { valid: false, error: 'Invalid game mode' };
    }
    
    return { valid: true };
  }
}

export const ludoService = new LudoService();
