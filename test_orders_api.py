#!/usr/bin/env python3
"""
Test script to verify orders API is working correctly
"""
import os
import sys
import django
import requests
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from orders.models import Order
from wallet.models import Wallet

def test_orders_api():
    print("🔍 Testing Orders API...")
    
    # Check database
    User = get_user_model()
    total_users = User.objects.count()
    total_orders = Order.objects.count()
    
    print(f"📊 Database Status:")
    print(f"   - Total users: {total_users}")
    print(f"   - Total orders: {total_orders}")
    
    if total_users == 0:
        print("⚠️  No users found in database")
        return
    
    # Get first user
    first_user = User.objects.first()
    user_orders = Order.objects.filter(user=first_user).count()
    print(f"   - Orders for user {first_user.email}: {user_orders}")
    
    # Check wallet
    try:
        wallet, created = Wallet.objects.get_or_create(user=first_user)
        print(f"   - User wallet balance: {wallet.balance} tokens")
    except Exception as e:
        print(f"   - Wallet error: {e}")
    
    # Test API endpoint (without authentication for now)
    try:
        print(f"\n🌐 Testing API endpoint...")
        response = requests.get('http://localhost:8000/api/orders/', timeout=5)
        print(f"   - Status code: {response.status_code}")
        
        if response.status_code == 401:
            print("   - ✅ API requires authentication (expected)")
        elif response.status_code == 200:
            print("   - ✅ API responding correctly")
            data = response.json()
            print(f"   - Response keys: {list(data.keys()) if isinstance(data, dict) else 'List response'}")
        else:
            print(f"   - ⚠️  Unexpected status code: {response.status_code}")
            print(f"   - Response: {response.text[:200]}...")
            
    except requests.exceptions.ConnectionError:
        print("   - ❌ Cannot connect to Django server")
        print("   - Make sure Django server is running: python manage.py runserver")
    except Exception as e:
        print(f"   - ❌ API test error: {e}")
    
    print(f"\n✅ Orders API test completed!")

if __name__ == "__main__":
    test_orders_api()
