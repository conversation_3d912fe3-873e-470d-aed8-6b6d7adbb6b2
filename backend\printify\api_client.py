import requests
import json
import logging
from django.conf import settings
from .models import PrintifyConfig

logger = logging.getLogger(__name__)

class PrintifyAPIClient:
    """
    Client for interacting with the Printify API
    """
    BASE_URL = 'https://api.printify.com/v1'

    def __init__(self):
        """
        Initialize the Printify API client
        """
        try:
            config = PrintifyConfig.objects.filter(is_active=True).first()
            if config:
                self.api_token = config.api_token
            else:
                self.api_token = getattr(settings, 'PRINTIFY_API_TOKEN', None)

            if not self.api_token:
                logger.error("No Printify API token found")
                raise ValueError("No Printify API token found")

        except Exception as e:
            logger.error(f"Error initializing Printify API client: {str(e)}")
            raise

    def _get_headers(self):
        """
        Get the headers for API requests
        """
        return {
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json',
            'User-Agent': 'PickMeTrend/1.0'
        }

    def _make_request(self, method, endpoint, data=None, params=None, timeout=30, retries=3):
        """
        Make a request to the Printify API with enhanced error handling
        """
        url = f"{self.BASE_URL}/{endpoint}"
        headers = self._get_headers()

        for attempt in range(retries):
            try:
                logger.info(f"Making {method} request to {url} (attempt {attempt + 1}/{retries})")

                if method == 'GET':
                    response = requests.get(url, headers=headers, params=params, timeout=timeout)
                elif method == 'POST':
                    response = requests.post(url, headers=headers, json=data, timeout=timeout)
                elif method == 'PUT':
                    response = requests.put(url, headers=headers, json=data, timeout=timeout)
                elif method == 'DELETE':
                    response = requests.delete(url, headers=headers, timeout=timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Log response details
                logger.info(f"Response status: {response.status_code}")

                response.raise_for_status()

                if response.status_code == 204:  # No content
                    return None

                result = response.json()
                logger.info(f"Request successful: {len(str(result))} characters returned")
                return result

            except requests.exceptions.Timeout as e:
                logger.warning(f"Request timeout on attempt {attempt + 1}: {str(e)}")
                if attempt == retries - 1:
                    raise

            except requests.exceptions.ConnectionError as e:
                logger.warning(f"Connection error on attempt {attempt + 1}: {str(e)}")
                if attempt == retries - 1:
                    raise

            except requests.exceptions.HTTPError as e:
                logger.error(f"HTTP error on attempt {attempt + 1}: {str(e)}")
                if hasattr(e, 'response') and e.response is not None:
                    logger.error(f"Response status: {e.response.status_code}")
                    logger.error(f"Response body: {e.response.text}")

                    # Don't retry on client errors (4xx)
                    if 400 <= e.response.status_code < 500:
                        raise

                if attempt == retries - 1:
                    raise

            except requests.exceptions.RequestException as e:
                logger.error(f"Request error on attempt {attempt + 1}: {str(e)}")
                if attempt == retries - 1:
                    raise

            except Exception as e:
                logger.error(f"Unexpected error on attempt {attempt + 1}: {str(e)}")
                if attempt == retries - 1:
                    raise

    # Shops endpoints
    def get_shops(self):
        """
        Get a list of shops
        """
        return self._make_request('GET', 'shops.json')

    # Catalog endpoints
    def get_blueprints(self):
        """
        Get a list of all available blueprints
        """
        return self._make_request('GET', 'catalog/blueprints.json')

    def get_blueprint(self, blueprint_id):
        """
        Get a specific blueprint
        """
        return self._make_request('GET', f'catalog/blueprints/{blueprint_id}.json')

    def get_blueprint_print_providers(self, blueprint_id):
        """
        Get a list of print providers for a blueprint
        """
        return self._make_request('GET', f'catalog/blueprints/{blueprint_id}/print_providers.json')

    def get_blueprint_variants(self, blueprint_id, print_provider_id):
        """
        Get a list of variants for a blueprint from a specific print provider
        """
        return self._make_request('GET', f'catalog/blueprints/{blueprint_id}/print_providers/{print_provider_id}/variants.json')

    def get_blueprint_shipping(self, blueprint_id, print_provider_id):
        """
        Get shipping information for a blueprint from a specific print provider
        """
        return self._make_request('GET', f'catalog/blueprints/{blueprint_id}/print_providers/{print_provider_id}/shipping.json')

    # Products endpoints
    def get_products(self, shop_id):
        """
        Get a list of products for a shop
        """
        return self._make_request('GET', f'shops/{shop_id}/products.json')

    def get_product(self, shop_id, product_id):
        """
        Get a specific product
        """
        return self._make_request('GET', f'shops/{shop_id}/products/{product_id}.json')

    def create_product(self, shop_id, product_data):
        """
        Create a new product
        """
        return self._make_request('POST', f'shops/{shop_id}/products.json', data=product_data)

    def update_product(self, shop_id, product_id, product_data):
        """
        Update a product
        """
        return self._make_request('PUT', f'shops/{shop_id}/products/{product_id}.json', data=product_data)

    def delete_product(self, shop_id, product_id):
        """
        Delete a product
        """
        return self._make_request('DELETE', f'shops/{shop_id}/products/{product_id}.json')

    def publish_product(self, shop_id, product_id, publish_data):
        """
        Publish a product
        """
        return self._make_request('POST', f'shops/{shop_id}/products/{product_id}/publish.json', data=publish_data)

    # Orders endpoints
    def get_orders(self, shop_id, params=None):
        """
        Get a list of orders for a shop
        """
        return self._make_request('GET', f'shops/{shop_id}/orders.json', params=params)

    def get_order(self, shop_id, order_id):
        """
        Get a specific order

        Args:
            shop_id: The ID of the shop
            order_id: The ID of the order

        Returns:
            dict: The order details including tracking information if available
        """
        return self._make_request('GET', f'shops/{shop_id}/orders/{order_id}.json')

    def get_order_tracking(self, shop_id, order_id):
        """
        Get tracking information for a specific order

        This method fetches the order details and extracts the tracking information.

        Args:
            shop_id: The ID of the shop
            order_id: The ID of the order

        Returns:
            dict: A dictionary containing tracking information:
                - status: The order status
                - tracking_number: The tracking number (if available)
                - carrier: The shipping carrier (if available)
                - carrier_link: The tracking URL (if available)
                - estimated_delivery: The estimated delivery date (if available)
        """
        try:
            # Get the order details
            order_details = self.get_order(shop_id, order_id)

            if not order_details:
                logger.warning(f"No order details found for order {order_id}")
                return None

            # Extract tracking information
            status = order_details.get('status', 'pending')

            # Initialize tracking info
            tracking_info = {
                'status': status,
                'tracking_number': None,
                'carrier': None,
                'carrier_link': None,
                'estimated_delivery': None
            }

            # Extract shipping information if available
            shipping_info = order_details.get('shipments', [])
            if shipping_info and len(shipping_info) > 0:
                # Use the first shipment (most orders will have only one)
                shipment = shipping_info[0]

                tracking_info['tracking_number'] = shipment.get('tracking_number')
                tracking_info['carrier'] = shipment.get('carrier')

                # Some carriers provide a tracking URL
                tracking_info['carrier_link'] = shipment.get('tracking_url')

                # Extract estimated delivery date if available
                if 'delivery_date' in shipment:
                    tracking_info['estimated_delivery'] = shipment.get('delivery_date')

            return tracking_info

        except Exception as e:
            logger.error(f"Error getting tracking information for order {order_id}: {str(e)}")
            return None

    def create_order(self, shop_id, order_data):
        """
        Create a new order
        """
        return self._make_request('POST', f'shops/{shop_id}/orders.json', data=order_data)

    def calculate_shipping(self, shop_id, shipping_data):
        """
        Calculate shipping costs
        """
        return self._make_request('POST', f'shops/{shop_id}/orders/shipping.json', data=shipping_data)

    def send_order_to_production(self, shop_id, order_id):
        """
        Send an order to production
        """
        return self._make_request('POST', f'shops/{shop_id}/orders/{order_id}/send_to_production.json')

    # Uploads endpoints
    def get_uploads(self):
        """
        Get a list of uploaded images
        """
        return self._make_request('GET', 'uploads.json')

    def upload_image_url(self, file_name, url):
        """
        Upload an image from a URL
        """
        data = {
            'file_name': file_name,
            'url': url
        }
        return self._make_request('POST', 'uploads/images.json', data=data)

    def upload_image_base64(self, file_name, contents):
        """
        Upload an image using base64 encoding
        """
        data = {
            'file_name': file_name,
            'contents': contents
        }
        return self._make_request('POST', 'uploads/images.json', data=data)
