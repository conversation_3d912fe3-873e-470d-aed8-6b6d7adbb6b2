import logging
from django.core.management.base import BaseCommand
from printify.models import PrintifyProduct
from products.models import Product, ProductImage, Category
from django.utils.text import slugify
import decimal

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Import Printify products to Django products model'

    def add_arguments(self, parser):
        parser.add_argument('--printify_ids', nargs='+', type=str, help='List of Printify product IDs to import')
        parser.add_argument('--category', type=str, default='Printify Products', help='Category name for the imported products')
        parser.add_argument('--all', action='store_true', help='Import all Printify products')

    def handle(self, *args, **options):
        printify_ids = options['printify_ids']
        category_name = options['category']
        import_all = options['all']

        self.stdout.write(self.style.SUCCESS(f'Starting Printify product import'))

        try:
            # Get or create the category
            category, created = Category.objects.get_or_create(
                name=category_name,
                defaults={'slug': slugify(category_name)}
            )

            if created:
                self.stdout.write(f"Created new category: {category_name}")

            # Get the products to import
            if import_all:
                printify_products = PrintifyProduct.objects.all()
                self.stdout.write(f"Importing all {printify_products.count()} Printify products")
            elif printify_ids:
                printify_products = PrintifyProduct.objects.filter(printify_id__in=printify_ids)
                self.stdout.write(f"Importing {printify_products.count()} specified Printify products")
            else:
                self.stdout.write(self.style.ERROR("No products specified. Use --printify_ids or --all"))
                return

            # Track statistics
            stats = {
                'total': printify_products.count(),
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0
            }

            # Process each product
            for i, printify_product in enumerate(printify_products):
                try:
                    self.stdout.write(f"Processing product {i+1}/{stats['total']}: {printify_product.printify_id}")

                    # Generate a slug from the title
                    slug = slugify(printify_product.title)

                    # Check if the product already exists
                    existing_product = Product.objects.filter(printify_id=printify_product.printify_id).first()

                    if existing_product:
                        # Update existing product
                        existing_product.name = printify_product.title
                        existing_product.description = printify_product.description or ''
                        existing_product.slug = slug

                        # Get the first variant for pricing
                        variants = printify_product.variants_json
                        if variants and len(variants) > 0:
                            variant = variants[0]
                            price = variant.get('price', 0)
                            try:
                                price = decimal.Decimal(price)
                            except (decimal.InvalidOperation, TypeError):
                                price = decimal.Decimal('0.00')

                            existing_product.price = price

                        existing_product.save()

                        # Update category
                        existing_product.categories.add(category)

                        # Update images
                        images = printify_product.images_json
                        if images and len(images) > 0:
                            # Clear existing images
                            existing_product.images.all().delete()

                            # Add new images
                            for i, image_data in enumerate(images):
                                image_url = image_data.get('src', '')
                                if image_url:
                                    ProductImage.objects.create(
                                        product=existing_product,
                                        image=image_url,
                                        image_url=image_url,
                                        is_primary=(i == 0)
                                    )

                        self.stdout.write(self.style.SUCCESS(f"Updated product: {printify_product.title} (ID: {printify_product.printify_id})"))
                        stats['updated'] += 1
                    else:
                        # Create new product
                        new_product = Product(
                            name=printify_product.title,
                            description=printify_product.description or '',
                            slug=slug,
                            printify_id=printify_product.printify_id,
                            stock=100  # Default stock
                        )

                        # Get the first variant for pricing
                        variants = printify_product.variants_json
                        if variants and len(variants) > 0:
                            variant = variants[0]
                            price = variant.get('price', 0)
                            try:
                                price = decimal.Decimal(price)
                            except (decimal.InvalidOperation, TypeError):
                                price = decimal.Decimal('0.00')

                            new_product.price = price

                        new_product.save()

                        # Add category
                        new_product.categories.add(category)

                        # Add images
                        images = printify_product.images_json
                        if images and len(images) > 0:
                            for i, image_data in enumerate(images):
                                image_url = image_data.get('src', '')
                                if image_url:
                                    ProductImage.objects.create(
                                        product=new_product,
                                        image=image_url,
                                        image_url=image_url,
                                        is_primary=(i == 0)
                                    )

                        self.stdout.write(self.style.SUCCESS(f"Created product: {printify_product.title} (ID: {printify_product.printify_id})"))
                        stats['created'] += 1

                except Exception as product_error:
                    self.stdout.write(self.style.ERROR(f"Error processing product {printify_product.printify_id}: {str(product_error)}"))
                    stats['errors'] += 1

            # Print summary
            self.stdout.write(self.style.SUCCESS(
                f"Import completed. Created: {stats['created']}, Updated: {stats['updated']}, "
                f"Skipped: {stats['skipped']}, Errors: {stats['errors']}"
            ))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error importing Printify products: {str(e)}"))
