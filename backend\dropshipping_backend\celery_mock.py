"""
Mock Celery implementation for development/testing without a message broker.
This module provides dummy implementations of Celery functionality.
"""

class DummyTask:
    """A dummy task that executes the function immediately instead of queueing it."""
    
    def __init__(self, func):
        self.func = func
        self.__name__ = func.__name__
    
    def delay(self, *args, **kwargs):
        """Execute the task immediately instead of queueing it."""
        return self.func(*args, **kwargs)
    
    def apply_async(self, args=None, kwargs=None, **options):
        """Execute the task immediately instead of queueing it."""
        args = args or []
        kwargs = kwargs or {}
        return self.func(*args, **kwargs)

class DummyCelery:
    """A dummy Celery app that executes tasks immediately."""
    
    def __init__(self, name):
        self.name = name
        self.tasks = {}
    
    def task(self, *args, **kwargs):
        """Decorator that registers a function as a task."""
        def decorator(func):
            task = DummyTask(func)
            self.tasks[func.__name__] = task
            return task
        
        # Handle both @app.task and @app.task()
        if len(args) == 1 and callable(args[0]):
            return decorator(args[0])
        return decorator
    
    def send_task(self, name, args=None, kwargs=None, **options):
        """Send a task by name."""
        args = args or []
        kwargs = kwargs or {}
        if name in self.tasks:
            return self.tasks[name].delay(*args, **kwargs)
        return None
    
    def autodiscover_tasks(self, packages=None):
        """Mock autodiscover_tasks."""
        return self

# Create a dummy Celery instance
app = DummyCelery('dropshipping_backend')

# For compatibility with code that imports these from celery
shared_task = app.task
