#!/usr/bin/env python
"""
Check current products in the system
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from products.models import Product, Category
from django.contrib.auth.models import User

def check_products():
    print("🛍️ PickMeTrend Product System Status")
    print("=" * 50)
    
    # Check total products
    total_products = Product.objects.count()
    print(f"📦 Total Products: {total_products}")
    
    if total_products > 0:
        print("\n🏷️ Recent Products:")
        for product in Product.objects.all()[:10]:
            status = "✅ Active" if product.is_active else "❌ Inactive"
            token_status = "🪙 Token Eligible" if getattr(product, 'allow_token_discount', False) else "💰 Regular"
            print(f"  - {product.name}")
            print(f"    Price: ₹{product.price} | {status} | {token_status}")
            if hasattr(product, 'printify_id') and product.printify_id:
                print(f"    Source: Printify (ID: {product.printify_id})")
            else:
                print(f"    Source: Manual Entry")
            print()
    
    # Check categories
    total_categories = Category.objects.count()
    print(f"📂 Total Categories: {total_categories}")
    
    if total_categories > 0:
        print("\n📁 Categories:")
        for category in Category.objects.all():
            product_count = category.products.count()
            print(f"  - {category.name}: {product_count} products")
    
    # Check admin users
    admin_users = User.objects.filter(is_superuser=True).count()
    print(f"\n👤 Admin Users: {admin_users}")
    
    if admin_users == 0:
        print("⚠️  No admin users found! Create one with: python manage.py createsuperuser")
    
    # Check Printify integration
    try:
        from printify.models import PrintifyProduct
        printify_products = PrintifyProduct.objects.count()
        print(f"\n🖨️  Printify Products: {printify_products}")
    except:
        print("\n🖨️  Printify integration: Not configured")
    
    print("\n" + "=" * 50)
    
    if total_products == 0:
        print("🚀 Getting Started:")
        print("1. Login to admin: http://localhost:8000/admin/")
        print("2. Add categories first")
        print("3. Add products manually OR sync from Printify")
        print("4. Configure token discounts for selected products")
    else:
        print("✅ Your product system is set up!")
        print("🔗 Admin URL: http://localhost:8000/admin/products/product/")

if __name__ == '__main__':
    check_products()
