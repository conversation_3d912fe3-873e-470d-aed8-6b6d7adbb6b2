<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Production Games Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }
        .game-test {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: rgba(46, 204, 113, 0.2);
            border: 1px solid #2ecc71;
        }
        .error {
            background: rgba(231, 76, 60, 0.2);
            border: 1px solid #e74c3c;
        }
        .info {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid #3498db;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-working {
            border: 2px solid #2ecc71;
        }
        .status-failed {
            border: 2px solid #e74c3c;
        }
        .status-pending {
            border: 2px solid #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Production Games Test Dashboard</h1>
        <p>Test all 5 games in production to verify they're working properly.</p>
        
        <div class="game-test">
            <h3>🔐 Authentication & Wallet</h3>
            <button onclick="testAuth()">Test Authentication</button>
            <button onclick="testWallet()">Test Wallet</button>
            <div id="auth-result"></div>
        </div>

        <div class="game-test">
            <h3>✂️ Rock Paper Scissors</h3>
            <button onclick="testRockPaperScissors()">Test Game</button>
            <div id="rps-result"></div>
        </div>

        <div class="game-test">
            <h3>🔢 Number Guessing</h3>
            <button onclick="testNumberGuessing()">Test Game</button>
            <div id="ng-result"></div>
        </div>

        <div class="game-test">
            <h3>⭕ Tic Tac Toe</h3>
            <button onclick="testTicTacToe()">Test Game</button>
            <div id="ttt-result"></div>
        </div>

        <div class="game-test">
            <h3>🎨 Color Match</h3>
            <button onclick="testColorMatch()">Test Game</button>
            <div id="cm-result"></div>
        </div>

        <div class="game-test">
            <h3>🃏 Memory Card Match</h3>
            <button onclick="testMemoryCard()">Test Game</button>
            <div id="mc-result"></div>
        </div>

        <div class="container">
            <h3>📊 Test Results Summary</h3>
            <button onclick="runAllTests()" style="background: linear-gradient(45deg, #2ecc71, #27ae60);">
                🚀 Run All Tests
            </button>
            <div class="status-grid" id="status-grid">
                <!-- Status cards will be populated here -->
            </div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        let testResults = {};

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function updateStatus() {
            const statusGrid = document.getElementById('status-grid');
            const games = [
                { key: 'auth', name: '🔐 Auth', id: 'auth-result' },
                { key: 'wallet', name: '💰 Wallet', id: 'wallet-result' },
                { key: 'rps', name: '✂️ RPS', id: 'rps-result' },
                { key: 'ng', name: '🔢 Number', id: 'ng-result' },
                { key: 'ttt', name: '⭕ TicTac', id: 'ttt-result' },
                { key: 'cm', name: '🎨 Color', id: 'cm-result' },
                { key: 'mc', name: '🃏 Memory', id: 'mc-result' }
            ];

            statusGrid.innerHTML = games.map(game => {
                const status = testResults[game.key];
                let statusClass = 'status-pending';
                let statusText = 'Pending';
                
                if (status === true) {
                    statusClass = 'status-working';
                    statusText = 'Working';
                } else if (status === false) {
                    statusClass = 'status-failed';
                    statusText = 'Failed';
                }

                return `
                    <div class="status-card ${statusClass}">
                        <div>${game.name}</div>
                        <div><strong>${statusText}</strong></div>
                    </div>
                `;
            }).join('');
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    credentials: 'include',
                    ...options
                });
                
                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function testAuth() {
            showResult('auth-result', 'Testing authentication...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/auth/user/`);
            
            if (result.success) {
                testResults.auth = true;
                showResult('auth-result', `✅ Authentication working - User: ${result.data.username || 'Unknown'}`, 'success');
            } else {
                testResults.auth = false;
                showResult('auth-result', `❌ Authentication failed - Status: ${result.status}`, 'error');
            }
            updateStatus();
        }

        async function testWallet() {
            showResult('wallet-result', 'Testing wallet...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/wallet/balance/`);
            
            if (result.success) {
                testResults.wallet = true;
                showResult('wallet-result', `✅ Wallet working - Balance: ${result.data.balance || 0} tokens`, 'success');
            } else {
                testResults.wallet = false;
                showResult('wallet-result', `❌ Wallet failed - Status: ${result.status}`, 'error');
            }
            updateStatus();
        }

        async function testRockPaperScissors() {
            showResult('rps-result', 'Testing Rock Paper Scissors...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/gaming/create-ai-battle/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ game_type: 'rock_paper_scissors' })
            });
            
            if (result.success && result.data.battle_id) {
                testResults.rps = true;
                showResult('rps-result', `✅ Rock Paper Scissors working - Battle: ${result.data.battle_id}`, 'success');
            } else {
                testResults.rps = false;
                showResult('rps-result', `❌ Rock Paper Scissors failed - ${result.error || 'Unknown error'}`, 'error');
            }
            updateStatus();
        }

        async function testNumberGuessing() {
            showResult('ng-result', 'Testing Number Guessing...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/gaming/create-ai-battle/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ game_type: 'number_guessing' })
            });
            
            if (result.success && result.data.battle_id) {
                testResults.ng = true;
                showResult('ng-result', `✅ Number Guessing working - Battle: ${result.data.battle_id}`, 'success');
            } else {
                testResults.ng = false;
                showResult('ng-result', `❌ Number Guessing failed - ${result.error || 'Unknown error'}`, 'error');
            }
            updateStatus();
        }

        async function testTicTacToe() {
            showResult('ttt-result', 'Testing Tic Tac Toe...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/gaming/tic-tac-toe/start/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ difficulty: 'medium' })
            });
            
            if (result.success && result.data.game_id) {
                testResults.ttt = true;
                showResult('ttt-result', `✅ Tic Tac Toe working - Game: ${result.data.game_id}`, 'success');
            } else {
                testResults.ttt = false;
                showResult('ttt-result', `❌ Tic Tac Toe failed - ${result.error || 'Unknown error'}`, 'error');
            }
            updateStatus();
        }

        async function testColorMatch() {
            showResult('cm-result', 'Testing Color Match...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/gaming/color-match/start/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ difficulty: 'medium' })
            });
            
            if (result.success && result.data.game_id) {
                testResults.cm = true;
                showResult('cm-result', `✅ Color Match working - Game: ${result.data.game_id}`, 'success');
            } else {
                testResults.cm = false;
                showResult('cm-result', `❌ Color Match failed - ${result.error || 'Unknown error'}`, 'error');
            }
            updateStatus();
        }

        async function testMemoryCard() {
            showResult('mc-result', 'Testing Memory Card...', 'info');
            
            const result = await makeRequest(`${API_BASE}/api/gaming/memory-card/start/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ difficulty: 'medium' })
            });
            
            if (result.success && result.data.game_id) {
                testResults.mc = true;
                showResult('mc-result', `✅ Memory Card working - Game: ${result.data.game_id}`, 'success');
            } else {
                testResults.mc = false;
                showResult('mc-result', `❌ Memory Card failed - ${result.error || 'Unknown error'}`, 'error');
            }
            updateStatus();
        }

        async function runAllTests() {
            testResults = {};
            updateStatus();
            
            await testAuth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWallet();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRockPaperScissors();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testNumberGuessing();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTicTacToe();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testColorMatch();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMemoryCard();
            
            // Show final summary
            const working = Object.values(testResults).filter(r => r === true).length;
            const total = Object.keys(testResults).length;
            
            if (working === total) {
                alert(`🎉 ALL TESTS PASSED! ${working}/${total} systems working perfectly!`);
            } else {
                alert(`⚠️ ${working}/${total} tests passed. Check failed tests above.`);
            }
        }

        // Initialize status grid
        updateStatus();
    </script>
</body>
</html>
