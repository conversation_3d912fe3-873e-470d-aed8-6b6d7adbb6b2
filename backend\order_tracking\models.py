from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid

class OrderTracking(models.Model):
    """
    Model to store order tracking information fetched from Printify API.

    This model stores tracking information for orders, including status,
    tracking number, carrier, and estimated delivery date.
    """
    ORDER_STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('shipped', 'Shipped'),
        ('delivered', 'Delivered'),
        ('cancelled', 'Cancelled'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='order_tracking')
    order_id = models.UUI<PERSON>ield(help_text="The ID of the order in our system")
    printify_order_id = models.CharField(max_length=255, blank=True, null=True, help_text="The ID of the order in Printify")
    status = models.Char<PERSON>ield(max_length=50, choices=ORDER_STATUS_CHOICES, default='pending')
    tracking_number = models.CharField(max_length=100, blank=True, null=True)
    carrier = models.CharField(max_length=100, blank=True, null=True)
    carrier_link = models.URLField(blank=True, null=True, help_text="Link to the carrier's tracking page")
    estimated_delivery = models.DateField(blank=True, null=True)
    last_update = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Order Tracking"
        verbose_name_plural = "Order Tracking"
        ordering = ['-last_update']
        unique_together = ['order_id', 'printify_order_id']

    def __str__(self):
        return f"Tracking for Order {self.order_id}"

    def is_delivered(self):
        """Check if the order has been delivered."""
        return self.status == 'delivered'

    def is_shipped(self):
        """Check if the order has been shipped."""
        return self.status == 'shipped'

    def days_since_shipped(self):
        """Calculate days since the order was shipped."""
        if self.status == 'shipped' and self.last_update:
            return (timezone.now().date() - self.last_update.date()).days
        return None

    def days_until_delivery(self):
        """Calculate days until estimated delivery."""
        if self.estimated_delivery and self.estimated_delivery > timezone.now().date():
            return (self.estimated_delivery - timezone.now().date()).days
        return None
