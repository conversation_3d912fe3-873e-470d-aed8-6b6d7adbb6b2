"""
Game logic for different game types
"""
import random
from typing import Dict, <PERSON>, Tu<PERSON>, Optional


class RockPaperScissorsGame:
    """
    Rock Paper Scissors game logic
    """
    CHOICES = ['rock', 'paper', 'scissors']
    
    @staticmethod
    def is_valid_move(move: str) -> bool:
        return move.lower() in RockPaperScissorsGame.CHOICES
    
    @staticmethod
    def determine_winner(move1: str, move2: str) -> str:
        """
        Determine winner between two moves
        Returns: 'player1', 'player2', or 'draw'
        """
        move1, move2 = move1.lower(), move2.lower()
        
        if move1 == move2:
            return 'draw'
        
        winning_combinations = {
            'rock': 'scissors',
            'paper': 'rock',
            'scissors': 'paper'
        }
        
        if winning_combinations[move1] == move2:
            return 'player1'
        else:
            return 'player2'
    
    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        return {
            'round': 1,
            'max_rounds': 3,
            'player1_score': 0,
            'player2_score': 0,
            'player1_move': None,
            'player2_move': None,
            'round_results': []
        }
    
    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, move: str) -> Dict[str, Any]:
        """
        Process a player's move
        """
        if not RockPaperScissorsGame.is_valid_move(move):
            raise ValueError(f"Invalid move: {move}")
        
        game_state = game_state.copy()
        
        if player == 'player1':
            game_state['player1_move'] = move.lower()
        elif player == 'player2':
            game_state['player2_move'] = move.lower()
        
        # Check if both players have moved
        if game_state['player1_move'] and game_state['player2_move']:
            # Determine round winner
            winner = RockPaperScissorsGame.determine_winner(
                game_state['player1_move'], 
                game_state['player2_move']
            )
            
            # Update scores
            if winner == 'player1':
                game_state['player1_score'] += 1
            elif winner == 'player2':
                game_state['player2_score'] += 1
            
            # Record round result
            game_state['round_results'].append({
                'round': game_state['round'],
                'player1_move': game_state['player1_move'],
                'player2_move': game_state['player2_move'],
                'winner': winner
            })
            
            # Reset moves for next round
            game_state['player1_move'] = None
            game_state['player2_move'] = None
            game_state['round'] += 1
        
        return game_state
    
    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        """
        Check if game is over
        """
        return (game_state['round'] > game_state['max_rounds'] or 
                game_state['player1_score'] > game_state['max_rounds'] // 2 or
                game_state['player2_score'] > game_state['max_rounds'] // 2)
    
    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        """
        Get final game result
        Returns: 'player1_win', 'player2_win', or 'draw'
        """
        if game_state['player1_score'] > game_state['player2_score']:
            return 'player1_win'
        elif game_state['player2_score'] > game_state['player1_score']:
            return 'player2_win'
        else:
            return 'draw'


class NumberGuessingGame:
    """
    Number guessing battle game
    """
    @staticmethod
    def get_initial_state() -> Dict[str, Any]:
        return {
            'round': 1,
            'max_rounds': 5,
            'target_number': random.randint(1, 100),
            'player1_score': 0,
            'player2_score': 0,
            'player1_guess': None,
            'player2_guess': None,
            'round_results': []
        }
    
    @staticmethod
    def is_valid_move(move: int) -> bool:
        return isinstance(move, int) and 1 <= move <= 100
    
    @staticmethod
    def process_move(game_state: Dict[str, Any], player: str, guess: int) -> Dict[str, Any]:
        """
        Process a player's guess
        """
        if not NumberGuessingGame.is_valid_move(guess):
            raise ValueError(f"Invalid guess: {guess}")
        
        game_state = game_state.copy()
        
        if player == 'player1':
            game_state['player1_guess'] = guess
        elif player == 'player2':
            game_state['player2_guess'] = guess
        
        # Check if both players have guessed
        if game_state['player1_guess'] is not None and game_state['player2_guess'] is not None:
            target = game_state['target_number']
            
            # Calculate distances
            dist1 = abs(target - game_state['player1_guess'])
            dist2 = abs(target - game_state['player2_guess'])
            
            # Determine winner
            if dist1 < dist2:
                winner = 'player1'
                game_state['player1_score'] += 1
            elif dist2 < dist1:
                winner = 'player2'
                game_state['player2_score'] += 1
            else:
                winner = 'draw'
            
            # Record round result
            game_state['round_results'].append({
                'round': game_state['round'],
                'target_number': target,
                'player1_guess': game_state['player1_guess'],
                'player2_guess': game_state['player2_guess'],
                'player1_distance': dist1,
                'player2_distance': dist2,
                'winner': winner
            })
            
            # Reset for next round
            game_state['player1_guess'] = None
            game_state['player2_guess'] = None
            game_state['target_number'] = random.randint(1, 100)
            game_state['round'] += 1
        
        return game_state
    
    @staticmethod
    def is_game_over(game_state: Dict[str, Any]) -> bool:
        return game_state['round'] > game_state['max_rounds']
    
    @staticmethod
    def get_game_result(game_state: Dict[str, Any]) -> str:
        if game_state['player1_score'] > game_state['player2_score']:
            return 'player1_win'
        elif game_state['player2_score'] > game_state['player1_score']:
            return 'player2_win'
        else:
            return 'draw'


class GameEngine:
    """
    Main game engine that handles different game types
    """
    GAME_CLASSES = {
        'rock_paper_scissors': RockPaperScissorsGame,
        'number_guessing': NumberGuessingGame,
    }
    
    @staticmethod
    def get_game_class(game_type: str):
        # Normalize game type name
        normalized_type = game_type.lower().replace(' ', '_')
        return GameEngine.GAME_CLASSES.get(normalized_type)

    @staticmethod
    def create_initial_state(game_type: str) -> Dict[str, Any]:
        print(f"Creating initial state for game type: {game_type}")
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type: {game_type}, available types: {list(GameEngine.GAME_CLASSES.keys())}")
            # Default to rock_paper_scissors if unknown
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']

        initial_state = game_class.get_initial_state()
        initial_state['game_type'] = game_type
        return initial_state
    
    @staticmethod
    def process_move(game_type: str, game_state: Dict[str, Any], player: str, move: Any) -> Dict[str, Any]:
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type in process_move: {game_type}")
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']
        return game_class.process_move(game_state, player, move)

    @staticmethod
    def is_game_over(game_type: str, game_state: Dict[str, Any]) -> bool:
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type in is_game_over: {game_type}")
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']
        return game_class.is_game_over(game_state)

    @staticmethod
    def get_game_result(game_type: str, game_state: Dict[str, Any]) -> str:
        game_class = GameEngine.get_game_class(game_type)
        if not game_class:
            print(f"Unknown game type in get_game_result: {game_type}")
            game_class = GameEngine.GAME_CLASSES['rock_paper_scissors']
        return game_class.get_game_result(game_state)
