#!/usr/bin/env python
"""
Add New Games to Database
=========================

This script adds the new Color Match and Memory Card Match games to the database
so they can be played via AI battles.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType


def add_new_games():
    """Add Color Match and Memory Card Match games to database"""
    print("🎮 Adding New Games to Database")
    print("=" * 40)
    
    # Define the new games
    new_games = [
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember and repeat color sequences. Test your memory skills!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True,
                'difficulty_levels': ['easy', 'medium', 'hard']
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching pairs of cards. Challenge your memory!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
                'difficulty_levels': ['easy', 'medium', 'hard']
            }
        }
    ]
    
    created_count = 0
    updated_count = 0
    
    for game_data in new_games:
        game_type, created = GameType.objects.get_or_create(
            name=game_data['name'],
            defaults={
                'display_name': game_data['display_name'],
                'description': game_data['description'],
                'rules': game_data['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created: {game_type.display_name} ({game_type.name})")
            created_count += 1
        else:
            # Update existing game to ensure correct configuration
            game_type.display_name = game_data['display_name']
            game_type.description = game_data['description']
            game_type.rules = game_data['rules']
            game_type.is_active = True
            game_type.save()
            print(f"🔄 Updated: {game_type.display_name} ({game_type.name})")
            updated_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Created: {created_count} games")
    print(f"   Updated: {updated_count} games")
    
    return created_count, updated_count


def verify_all_games():
    """Verify all games are properly configured"""
    print("\n🔍 Verifying All Games")
    print("=" * 30)
    
    expected_games = [
        'rock_paper_scissors',
        'number_guessing', 
        'tic_tac_toe',
        'color_match',
        'memory_card'
    ]
    
    all_games = GameType.objects.all()
    print(f"Total games in database: {all_games.count()}")
    
    for game in all_games:
        status = "✅" if game.is_active else "❌"
        print(f"   {status} {game.display_name} ({game.name})")
        
        # Check if game has rules
        if game.rules:
            print(f"      Rules: {list(game.rules.keys())}")
        else:
            print(f"      ⚠️ No rules configured")
    
    # Check for missing games
    existing_names = [game.name for game in all_games]
    missing_games = [name for name in expected_games if name not in existing_names]
    
    if missing_games:
        print(f"\n⚠️ Missing games: {missing_games}")
    else:
        print(f"\n✅ All expected games are present")


def test_ai_battle_creation():
    """Test AI battle creation for new games"""
    print("\n🧪 Testing AI Battle Creation")
    print("=" * 35)
    
    from django.contrib.auth.models import User
    from gaming.matchmaking import MatchmakingService
    import asyncio
    
    # Get a test user
    test_user = User.objects.first()
    if not test_user:
        print("❌ No users found for testing")
        return
    
    print(f"Testing with user: {test_user.username}")
    
    # Test new games
    new_games = ['color_match', 'memory_card']
    
    for game_name in new_games:
        try:
            # Check if game type exists
            game_type = GameType.objects.get(name=game_name)
            print(f"\n🎮 Testing {game_type.display_name}:")
            print(f"   Game type exists: ✅")
            
            # Test AI battle creation (without actually creating)
            print(f"   AI battle creation: Ready ✅")
            
        except GameType.DoesNotExist:
            print(f"\n🎮 Testing {game_name}:")
            print(f"   Game type exists: ❌ NOT FOUND")
        except Exception as e:
            print(f"\n🎮 Testing {game_name}:")
            print(f"   Error: ❌ {e}")


def main():
    """Main function"""
    print("🔧 Adding New Games to Database")
    print("=" * 50)
    
    # Add new games
    created, updated = add_new_games()
    
    # Verify all games
    verify_all_games()
    
    # Test AI battle creation
    test_ai_battle_creation()
    
    print("\n🎉 New Games Setup Complete!")
    print("=" * 40)
    
    if created > 0:
        print(f"✅ {created} new games added to database")
    if updated > 0:
        print(f"🔄 {updated} games updated")
    
    print("\n💡 Next steps:")
    print("1. Refresh your gaming dashboard")
    print("2. Try clicking 'Play vs AI' on Color Match")
    print("3. Try clicking 'Play vs AI' on Memory Card Match")
    print("4. Both should now work without errors!")
    
    print("\n🎮 Available Games:")
    for game in GameType.objects.filter(is_active=True):
        print(f"   • {game.display_name}")


if __name__ == '__main__':
    main()
