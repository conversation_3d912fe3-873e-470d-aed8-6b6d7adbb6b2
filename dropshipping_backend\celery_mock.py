"""
Celery Mock Module
==================

This module provides a mock implementation of Celery for environments
where Celery is not available or Redis is not configured.

It allows the application to run without Celery by providing dummy
implementations of Celery decorators and functions.
"""

import logging

logger = logging.getLogger(__name__)


def shared_task(*args, **kwargs):
    """
    Mock implementation of Celery's shared_task decorator.
    
    This decorator does nothing and allows functions to be called
    directly without going through Celery task queue.
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Log that we're running the task directly
            logger.info(f"Running task directly (no Celery): {func.__name__}")
            return func(*args, **kwargs)
        
        # Add delay method for compatibility
        wrapper.delay = lambda *args, **kwargs: wrapper(*args, **kwargs)
        wrapper.apply_async = lambda *args, **kwargs: wrapper(*args, **kwargs)
        
        return wrapper
    
    # Handle both @shared_task and @shared_task() usage
    if len(args) == 1 and callable(args[0]):
        return decorator(args[0])
    else:
        return decorator


def task(*args, **kwargs):
    """
    Mock implementation of Celery's task decorator.
    
    This decorator does nothing and allows functions to be called
    directly without going through Celery task queue.
    """
    return shared_task(*args, **kwargs)


class MockCeleryApp:
    """
    Mock Celery application for environments without Celery.
    """
    
    def __init__(self):
        self.conf = MockCeleryConfig()
    
    def task(self, *args, **kwargs):
        """Mock task decorator"""
        return task(*args, **kwargs)
    
    def autodiscover_tasks(self, *args, **kwargs):
        """Mock autodiscover_tasks - does nothing"""
        logger.info("Mock Celery: autodiscover_tasks called (no-op)")
        pass


class MockCeleryConfig:
    """
    Mock Celery configuration object.
    """
    
    def update(self, *args, **kwargs):
        """Mock config update - does nothing"""
        logger.info("Mock Celery: config update called (no-op)")
        pass


# Create a mock Celery app instance
app = MockCeleryApp()

# Export commonly used Celery functions
__all__ = ['shared_task', 'task', 'app']
