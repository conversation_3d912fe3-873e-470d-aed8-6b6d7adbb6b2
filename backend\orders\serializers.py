from rest_framework import serializers
from .models import Cart, CartItem, Order, OrderItem
from products.serializers import ProductListSerializer
from products.models import Product


class CartItemSerializer(serializers.ModelSerializer):
    product = ProductListSerializer(read_only=True)
    product_id = serializers.UUIDField(write_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    quantity = serializers.IntegerField(min_value=1, default=1)
    variant_id = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    variant_details = serializers.SerializerMethodField()
    variant_price = serializers.SerializerMethodField()

    class Meta:
        model = CartItem
        fields = ['id', 'product', 'product_id', 'quantity', 'variant_id', 'variant_details', 'variant_price', 'total_price']

    def get_variant_details(self, obj):
        """Get variant details (color, size, title) for this cart item"""
        return obj.get_variant_details()

    def get_variant_price(self, obj):
        """Get the price for this cart item's variant"""
        return obj.get_variant_price()

    def validate_product_id(self, value):
        """
        Validate that the product exists and has stock
        """
        try:
            product = Product.objects.get(id=value)
            if not product.is_active:
                raise serializers.ValidationError("This product is not available.")
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product not found.")

    def validate(self, data):
        """
        Validate variant_id if provided
        """
        variant_id = data.get('variant_id')
        product_id = data.get('product_id')

        if variant_id and product_id:
            try:
                product = Product.objects.get(id=product_id)
                # Check if variant exists for this product
                from products.models import ProductVariant
                if not ProductVariant.objects.filter(
                    product=product,
                    variant_id=variant_id
                ).exists():
                    raise serializers.ValidationError({
                        "variant_id": f"Variant with ID '{variant_id}' not found for this product."
                    })
            except Product.DoesNotExist:
                pass  # Will be caught by product_id validation

        return data

    def create(self, validated_data):
        cart = validated_data.get('cart')
        product_id = validated_data.pop('product_id')

        try:
            product = Product.objects.get(id=product_id)

            # Check if product has enough stock
            quantity = validated_data.get('quantity', 1)
            if product.stock < quantity:
                raise serializers.ValidationError({"quantity": f"Not enough stock. Only {product.stock} available."})

            # Check if cart item exists with the same variant
            variant_id = validated_data.get('variant_id')
            try:
                # Look for existing cart item with same product and variant
                if variant_id:
                    cart_item = CartItem.objects.get(cart=cart, product=product, variant_id=variant_id)
                else:
                    cart_item = CartItem.objects.get(cart=cart, product=product, variant_id__isnull=True)

                # Check if the new total quantity exceeds stock
                new_quantity = cart_item.quantity + quantity
                if product.stock < new_quantity:
                    raise serializers.ValidationError(
                        {"quantity": f"Cannot add {quantity} more. You already have {cart_item.quantity} in cart and only {product.stock} are available."}
                    )

                cart_item.quantity = new_quantity
                cart_item.save()
                return cart_item
            except CartItem.DoesNotExist:
                validated_data['product'] = product
                return super().create(validated_data)

        except Product.DoesNotExist:
            raise serializers.ValidationError({"product_id": "Product not found."})


class CartSerializer(serializers.ModelSerializer):
    items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_items = serializers.IntegerField(read_only=True)

    class Meta:
        model = Cart
        fields = ['id', 'items', 'total_price', 'total_items', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']


class OrderItemSerializer(serializers.ModelSerializer):
    product = ProductListSerializer(read_only=True)

    class Meta:
        model = OrderItem
        fields = ['id', 'product', 'price', 'quantity', 'variant_id', 'printify_order_id', 'total_price']
        read_only_fields = ['id', 'total_price', 'printify_order_id']


class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True, read_only=True)
    can_return = serializers.SerializerMethodField()
    has_return_request = serializers.SerializerMethodField()
    return_request_status = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'full_name', 'email', 'phone', 'address', 'city',
            'state', 'zipcode', 'country', 'total', 'status',
            'payment_status', 'payment_method', 'payment_id', 'notes', 'tracking_number',
            'razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature',
            'items', 'created_at', 'updated_at',
            'can_return', 'has_return_request', 'return_request_status'
        ]
        read_only_fields = ['id', 'total', 'status', 'payment_status', 'tracking_number',
                            'razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature',
                            'created_at', 'updated_at', 'can_return', 'has_return_request', 'return_request_status']

    def get_can_return(self, obj):
        """Check if order can be returned"""
        return obj.status == 'delivered'

    def get_has_return_request(self, obj):
        """Check if order has a return request"""
        return hasattr(obj, 'return_requests') and obj.return_requests.exists()

    def get_return_request_status(self, obj):
        """Get return request status if exists"""
        if hasattr(obj, 'return_requests') and obj.return_requests.exists():
            return obj.return_requests.first().status
        return None


class OrderCreateSerializer(serializers.ModelSerializer):
    # Optional shipping fields
    shipping_first_name = serializers.CharField(required=False, allow_blank=True)
    shipping_last_name = serializers.CharField(required=False, allow_blank=True)
    shipping_address1 = serializers.CharField(required=False, allow_blank=True)
    shipping_address2 = serializers.CharField(required=False, allow_blank=True)
    shipping_city = serializers.CharField(required=False, allow_blank=True)
    shipping_state = serializers.CharField(required=False, allow_blank=True)
    shipping_zip = serializers.CharField(required=False, allow_blank=True)
    shipping_country = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Order
        fields = [
            # Billing address fields
            'full_name', 'email', 'phone', 'address', 'city',
            'state', 'zipcode', 'country', 'payment_method', 'notes',
            # Shipping address fields
            'shipping_first_name', 'shipping_last_name', 'shipping_address1',
            'shipping_address2', 'shipping_city', 'shipping_state',
            'shipping_zip', 'shipping_country',
        ]

    def validate(self, data):
        """
        Custom validation for the entire data set
        """
        print(f"Validating order data: {data}")

        # Check if payment_method is present
        if 'payment_method' not in data:
            raise serializers.ValidationError({"payment_method": "This field is required."})

        # Check if state is present and not empty
        if 'state' not in data or not data['state']:
            data['state'] = 'Default State'  # Provide a default value
            print("Added default state value")

        return data

    def validate_phone(self, value):
        """
        Validate phone number format
        """
        if not value:
            return "0000000000"  # Default value if empty

        # Remove any non-digit characters
        digits_only = ''.join(filter(str.isdigit, value))

        # Check if we have a valid number of digits (assuming Indian phone number)
        if len(digits_only) < 10:
            raise serializers.ValidationError("Phone number must have at least 10 digits.")

        return value

    def validate_payment_method(self, value):
        """
        Validate payment method
        """
        if not value:
            return "cod"  # Default to COD if empty

        valid_methods = ['razorpay', 'cod']

        # Convert value to lowercase for case-insensitive comparison
        value_lower = value.lower() if isinstance(value, str) else value

        # Check if the value is valid
        if value_lower not in valid_methods:
            print(f"Invalid payment method: '{value}'. Using default 'cod'")
            return "cod"  # Default to COD if invalid

        # Return the normalized value
        return value_lower


class OrderStatusUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = ['status', 'tracking_number']


class PaymentUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = [
            'payment_status', 'payment_id',
            'razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature'
        ]