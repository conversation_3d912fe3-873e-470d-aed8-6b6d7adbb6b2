from django.core.management.base import BaseCommand
from gaming.models import GameType


class Command(BaseCommand):
    help = 'Setup initial game types'

    def handle(self, *args, **options):
        game_types = [
            {
                'name': 'rock_paper_scissors',
                'display_name': 'Rock Paper Scissors',
                'description': 'Classic rock, paper, scissors game. Best of 3 rounds wins!',
                'rules': {
                    'max_rounds': 3,
                    'choices': ['rock', 'paper', 'scissors'],
                    'winning_combinations': {
                        'rock': 'scissors',
                        'paper': 'rock',
                        'scissors': 'paper'
                    }
                }
            },
            {
                'name': 'number_guessing',
                'display_name': 'Number Guessing Battle',
                'description': 'Guess the number between 1-100. Closest guess wins the round!',
                'rules': {
                    'max_rounds': 5,
                    'range': [1, 100],
                    'scoring': 'closest_wins'
                }
            }
        ]

        for game_data in game_types:
            game_type, created = GameType.objects.get_or_create(
                name=game_data['name'],
                defaults={
                    'display_name': game_data['display_name'],
                    'description': game_data['description'],
                    'rules': game_data['rules'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created game type: {game_type.display_name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Game type already exists: {game_type.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully setup game types!')
        )
