#!/usr/bin/env python
"""
Check User Details Script
=========================

This script shows detailed information about all users in the database
and helps identify login issues.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from accounts.models import UserProfile
from wallet.models import Wallet, WalletTransaction
from gaming.models import PlayerStats


def show_all_users():
    """Show detailed information about all users"""
    print("👥 All Users in Database")
    print("=" * 60)
    
    users = User.objects.all().order_by('-date_joined')
    
    for i, user in enumerate(users, 1):
        print(f"\n{i}. User: {user.username}")
        print(f"   📧 Email: {user.email}")
        print(f"   🆔 ID: {user.id}")
        print(f"   ✅ Active: {user.is_active}")
        print(f"   👑 Staff: {user.is_staff}")
        print(f"   🔐 Password Hash: {user.password[:30]}..." if user.password else "   🔐 Password: NOT SET")
        print(f"   📅 Joined: {user.date_joined}")
        print(f"   🕐 Last Login: {user.last_login or 'Never'}")
        print(f"   👤 First Name: {user.first_name or 'Not set'}")
        print(f"   👤 Last Name: {user.last_name or 'Not set'}")
        
        # Check related objects
        print(f"   📋 Related Objects:")
        
        # Profile
        try:
            profile = user.profile
            print(f"      ✅ Profile: {profile.user_type}")
        except UserProfile.DoesNotExist:
            print(f"      ❌ Profile: Missing")
        
        # Wallet
        try:
            wallet = user.wallet
            print(f"      ✅ Wallet: {wallet.balance} tokens")
            
            # Recent transactions
            recent_transactions = WalletTransaction.objects.filter(wallet=wallet).order_by('-created_at')[:3]
            if recent_transactions:
                print(f"      💰 Recent Transactions:")
                for tx in recent_transactions:
                    print(f"         - {tx.transaction_type}: {tx.amount} tokens ({tx.created_at.strftime('%Y-%m-%d')})")
            
        except Wallet.DoesNotExist:
            print(f"      ❌ Wallet: Missing")
        
        # Gaming Stats
        try:
            stats = user.gaming_stats
            print(f"      ✅ Gaming: {stats.total_games_played} games, {stats.total_tokens_earned} tokens earned")
        except PlayerStats.DoesNotExist:
            print(f"      ❌ Gaming Stats: Missing")
        
        print("-" * 60)


def test_login_for_user(username):
    """Test login functionality for a specific user"""
    print(f"\n🔐 Testing Login for: {username}")
    print("=" * 40)
    
    try:
        user = User.objects.get(username=username)
        print(f"✅ User found: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Active: {user.is_active}")
        print(f"   Password set: {bool(user.password)}")
        
        # Test common passwords
        test_passwords = [
            "password123",
            "Password123",
            "password",
            "123456",
            "admin123",
            "test123",
            "TestPass123!",
            user.username + "123",  # username + 123
        ]
        
        print(f"\n🧪 Testing common passwords for {username}:")
        for password in test_passwords:
            if user.check_password(password):
                print(f"✅ FOUND WORKING PASSWORD: {password}")
                return password
            else:
                print(f"❌ {password}")
        
        print(f"\n⚠️ None of the common passwords worked for {username}")
        print(f"💡 The user might have used a different password during registration")
        
        return None
        
    except User.DoesNotExist:
        print(f"❌ User '{username}' not found")
        return None


def show_recent_users():
    """Show the most recently created users"""
    print("\n🆕 Most Recent Users (Last 5)")
    print("=" * 40)
    
    recent_users = User.objects.all().order_by('-date_joined')[:5]
    
    for user in recent_users:
        print(f"• {user.username} ({user.email}) - {user.date_joined.strftime('%Y-%m-%d %H:%M')}")


def main():
    """Main function"""
    print("🔍 User Details Check")
    print("=" * 50)
    
    # Show all users
    show_all_users()
    
    # Show recent users
    show_recent_users()
    
    # Test login for recent users
    print("\n🧪 Testing Login for Recent Users")
    print("=" * 40)
    
    recent_users = User.objects.all().order_by('-date_joined')[:3]
    
    for user in recent_users:
        working_password = test_login_for_user(user.username)
        if working_password:
            print(f"\n🎉 You can login with:")
            print(f"   Username: {user.username}")
            print(f"   Password: {working_password}")
            break
    
    print("\n📋 Summary")
    print("=" * 40)
    print(f"Total users: {User.objects.count()}")
    print(f"Active users: {User.objects.filter(is_active=True).count()}")
    print(f"Users with profiles: {User.objects.filter(profile__isnull=False).count()}")
    print(f"Users with wallets: {User.objects.filter(wallet__isnull=False).count()}")
    
    print("\n💡 Next Steps:")
    print("1. Try logging in with the credentials shown above")
    print("2. If no working password found, register a new user")
    print("3. Check browser console for actual API calls during login")
    print("4. Make sure frontend is calling /api/auth/login/ endpoint")


if __name__ == '__main__':
    main()
