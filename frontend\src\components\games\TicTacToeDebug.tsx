import React, { useState, useEffect } from 'react';
import { gameSessionService } from '../../services/gameSessionService';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';

const TicTacToeDebug: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testGameSession = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    try {
      addResult("🔍 Starting comprehensive test...");
      
      // Test 1: Check user authentication
      addResult(`👤 User: ${user ? user.username : 'Not logged in'}`);
      
      // Test 2: Check wallet
      addResult(`💰 Wallet Balance: ${wallet?.balance || 'No wallet'} tokens`);
      
      // Test 3: Check if user can play
      const canPlayResult = await gameSessionService.canPlayGames();
      addResult(`🎮 Can Play: ${canPlayResult.canPlay} - ${canPlayResult.message}`);
      
      // Test 4: Try to start a game session
      addResult("🚀 Attempting to start game session...");
      const startResult = await gameSessionService.startGameSession('tic_tac_toe');
      
      if (startResult.success) {
        addResult(`✅ Game session started: ${startResult.session_id}`);
        addResult(`💰 New balance: ${startResult.balance} tokens`);
        addResult(`📝 Message: ${startResult.message}`);
        
        // Test 5: Try to get session status
        const statusResult = await gameSessionService.getSessionStatus(startResult.session_id);
        if (statusResult.success) {
          addResult(`📊 Session status: ${statusResult.status}`);
          addResult(`🎯 Game type: ${statusResult.game_type}`);
        } else {
          addResult(`❌ Failed to get session status: ${statusResult.error}`);
        }
        
        // Test 6: Try to forfeit the session
        addResult("⚠️ Testing forfeit functionality...");
        const forfeitResult = await gameSessionService.forfeitGameSession(startResult.session_id);
        if (forfeitResult.success) {
          addResult(`✅ Forfeit successful: ${forfeitResult.tokens_earned} tokens`);
          addResult(`💰 Final balance: ${forfeitResult.new_balance} tokens`);
        } else {
          addResult(`❌ Forfeit failed: ${forfeitResult.error}`);
        }
        
      } else {
        addResult(`❌ Failed to start game session: ${startResult.error}`);
      }
      
      // Test 7: Get user stats
      addResult("📈 Getting user stats...");
      const statsResult = await gameSessionService.getUserStats();
      if (statsResult.success) {
        addResult(`📊 Total sessions: ${statsResult.stats.total_sessions}`);
        addResult(`🏆 Wins: ${statsResult.stats.wins}`);
        addResult(`💰 Current balance: ${statsResult.stats.current_balance}`);
      } else {
        addResult(`❌ Failed to get stats: ${statsResult.error}`);
      }
      
      // Refresh wallet
      await refreshWallet();
      addResult("🔄 Wallet refreshed");
      
    } catch (error) {
      addResult(`💥 Test failed with error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-600 via-pink-600 to-red-600 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 text-white">
          <h1 className="text-3xl font-bold mb-6 text-center">🔧 Tic Tac Toe Debug Console</h1>
          
          {/* User Info */}
          <div className="bg-white/10 rounded-2xl p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">👤 User Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="opacity-75">Username:</div>
                <div className="font-bold">{user?.username || 'Not logged in'}</div>
              </div>
              <div>
                <div className="opacity-75">Wallet Balance:</div>
                <div className="font-bold">{wallet?.balance || 0} tokens</div>
              </div>
              <div>
                <div className="opacity-75">Authentication:</div>
                <div className="font-bold">{user ? '✅ Authenticated' : '❌ Not authenticated'}</div>
              </div>
            </div>
          </div>

          {/* Test Controls */}
          <div className="bg-white/10 rounded-2xl p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">🧪 Test Controls</h2>
            <div className="flex space-x-4">
              <button
                onClick={testGameSession}
                disabled={isLoading}
                className="px-6 py-3 bg-green-500 hover:bg-green-600 disabled:bg-gray-500 rounded-xl font-bold transition-all duration-200"
              >
                {isLoading ? '🔄 Testing...' : '🚀 Run Complete Test'}
              </button>
              
              <button
                onClick={clearResults}
                className="px-6 py-3 bg-red-500 hover:bg-red-600 rounded-xl font-bold transition-all duration-200"
              >
                🗑️ Clear Results
              </button>
              
              <button
                onClick={refreshWallet}
                className="px-6 py-3 bg-blue-500 hover:bg-blue-600 rounded-xl font-bold transition-all duration-200"
              >
                🔄 Refresh Wallet
              </button>
            </div>
          </div>

          {/* Test Results */}
          <div className="bg-white/10 rounded-2xl p-6">
            <h2 className="text-xl font-bold mb-4">📋 Test Results</h2>
            <div className="bg-black/20 rounded-xl p-4 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <div className="text-center opacity-50">No test results yet. Click "Run Complete Test" to start.</div>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-sm font-mono">
                      {result}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-6 text-center">
            <div className="text-sm opacity-75 mb-4">
              This debug console helps identify issues with the token system and game sessions.
            </div>
            <a
              href="/gaming/tic-tac-toe"
              className="inline-block px-6 py-3 bg-purple-500 hover:bg-purple-600 rounded-xl font-bold transition-all duration-200"
            >
              🎮 Go to Tic Tac Toe Game
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicTacToeDebug;
