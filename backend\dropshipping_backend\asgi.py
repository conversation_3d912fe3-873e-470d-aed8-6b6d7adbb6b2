"""
ASGI config for dropshipping_backend project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/howto/deployment/asgi/
"""

import os
import json
from django.core.asgi import get_asgi_application
from channels.routing import Protocol<PERSON>ypeRouter, URLRouter
from channels.generic.websocket import AsyncWebsocketConsumer
from django.urls import re_path

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')

# Initialize Django ASGI application early
django_asgi_app = get_asgi_application()

# Simple WebSocket consumer for testing - no authentication required
class TestConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        print("WebSocket connection attempt")
        await self.accept()
        print("WebSocket connection accepted")
        await self.send(text_data=json.dumps({
            'type': 'connected',
            'message': 'WebSocket connection successful!',
            'authenticated': False
        }))

    async def disconnect(self, close_code):
        print(f"WebSocket disconnected with code: {close_code}")

    async def receive(self, text_data):
        print(f"WebSocket received: {text_data}")
        try:
            data = json.loads(text_data)
            await self.send(text_data=json.dumps({
                'type': 'echo',
                'data': data
            }))
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': str(e)
            }))

# Simple WebSocket routing - no middleware
websocket_urlpatterns = [
    re_path(r'ws/matchmaking/$', TestConsumer.as_asgi()),
]

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": URLRouter(websocket_urlpatterns),
})
