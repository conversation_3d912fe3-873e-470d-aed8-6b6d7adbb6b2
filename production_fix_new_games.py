#!/usr/bin/env python
"""
Production Fix for New Games
============================

This script is designed to run on production and fix the AI battle creation
issue by ensuring all required game types exist in the database.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType
from django.db import transaction


def create_all_game_types():
    """Create all required game types with proper error handling"""
    print("🎮 Creating All Required Game Types")
    print("=" * 40)
    
    # Define ALL games that should exist
    all_games = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic battle game - Play vs AI or human opponents!',
            'rules': {
                'choices': ['rock', 'paper', 'scissors'],
                'win_conditions': {
                    'rock': 'scissors',
                    'paper': 'rock',
                    'scissors': 'paper'
                },
                'max_rounds': 3
            }
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number - Play vs AI or human opponents!',
            'rules': {
                'min_number': 1,
                'max_number': 100,
                'max_attempts': 5
            }
        },
        {
            'name': 'tic_tac_toe',
            'display_name': 'Tic Tac Toe',
            'description': 'Classic strategy game against AI. Get 3 in a row to win!',
            'rules': {
                'board_size': 3,
                'win_condition': '3_in_a_row',
                'ai_difficulty': 'hard'
            }
        },
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember color sequences - Play vs AI or human opponents!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True,
                'difficulty_levels': ['easy', 'medium', 'hard']
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs - Play vs AI or human opponents!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
                'difficulty_levels': ['easy', 'medium', 'hard']
            }
        }
    ]
    
    created_count = 0
    updated_count = 0
    error_count = 0
    
    # Use database transaction for safety
    with transaction.atomic():
        for game_data in all_games:
            try:
                game_type, created = GameType.objects.get_or_create(
                    name=game_data['name'],
                    defaults={
                        'display_name': game_data['display_name'],
                        'description': game_data['description'],
                        'rules': game_data['rules'],
                        'is_active': True
                    }
                )
                
                if created:
                    print(f"✅ Created: {game_type.display_name}")
                    created_count += 1
                else:
                    # Update existing game to ensure correct configuration
                    game_type.display_name = game_data['display_name']
                    game_type.description = game_data['description']
                    game_type.rules = game_data['rules']
                    game_type.is_active = True
                    game_type.save()
                    print(f"🔄 Updated: {game_type.display_name}")
                    updated_count += 1
                    
            except Exception as e:
                print(f"❌ Error creating {game_data['name']}: {e}")
                error_count += 1
    
    print(f"\n📊 Summary:")
    print(f"   Created: {created_count} games")
    print(f"   Updated: {updated_count} games")
    print(f"   Errors: {error_count} games")
    print(f"   Total in database: {GameType.objects.filter(is_active=True).count()}")
    
    return created_count, updated_count, error_count


def verify_game_types():
    """Verify all required game types exist"""
    print("\n🔍 Verifying Game Types")
    print("=" * 25)
    
    required_games = ['rock_paper_scissors', 'number_guessing', 'tic_tac_toe', 'color_match', 'memory_card']
    
    all_exist = True
    for game_name in required_games:
        try:
            game_type = GameType.objects.get(name=game_name, is_active=True)
            print(f"✅ {game_type.display_name} - ID: {game_type.id}")
        except GameType.DoesNotExist:
            print(f"❌ Missing: {game_name}")
            all_exist = False
    
    if all_exist:
        print("\n✅ All required game types exist!")
    else:
        print("\n❌ Some game types are missing!")
    
    return all_exist


def test_game_engine_support():
    """Test that GameEngine supports all games"""
    print("\n🔧 Testing GameEngine Support")
    print("=" * 30)
    
    from gaming.game_logic import GameEngine
    
    supported_games = list(GameEngine.GAME_CLASSES.keys())
    print(f"GameEngine supports: {supported_games}")
    
    database_games = list(GameType.objects.filter(is_active=True).values_list('name', flat=True))
    print(f"Database has: {database_games}")
    
    missing_in_engine = [game for game in database_games if game not in supported_games]
    missing_in_db = [game for game in supported_games if game not in database_games]
    
    if missing_in_engine:
        print(f"❌ Missing in GameEngine: {missing_in_engine}")
    
    if missing_in_db:
        print(f"❌ Missing in Database: {missing_in_db}")
    
    if not missing_in_engine and not missing_in_db:
        print("✅ GameEngine and Database are in sync!")
        return True
    
    return False


def test_ai_bot_support():
    """Test that AI bot supports all games"""
    print("\n🤖 Testing AI Bot Support")
    print("=" * 25)
    
    from gaming.ai_bot import AIBot
    
    ai_bot = AIBot(difficulty='medium')
    
    test_cases = {
        'rock_paper_scissors': {'round': 1},
        'number_guessing': {'round': 1, 'target_number': 50},
        'tic_tac_toe': {'board': [['', '', ''], ['', '', ''], ['', '', '']]},
        'color_match': {
            'colors': ['red', 'blue', 'green'],
            'sequence': ['red', 'blue'],
            'player2_sequence': []
        },
        'memory_card': {
            'cards': ['🐶', '🐱', '🐶', '🐱'],
            'revealed': [False, False, False, False],
            'matched': [False, False, False, False],
            'selected_cards': []
        }
    }
    
    all_supported = True
    for game_name, test_state in test_cases.items():
        try:
            move = ai_bot.get_move(game_name, test_state)
            print(f"✅ {game_name}: AI move = {move}")
        except Exception as e:
            print(f"❌ {game_name}: Error = {e}")
            all_supported = False
    
    return all_supported


def main():
    """Main production fix function"""
    print("🚀 PRODUCTION FIX FOR NEW GAMES")
    print("=" * 50)
    print("This script will fix the AI battle creation issue")
    print("by ensuring all game types exist in the database.")
    print("=" * 50)
    
    try:
        # Step 1: Create all game types
        created, updated, errors = create_all_game_types()
        
        # Step 2: Verify all game types exist
        all_exist = verify_game_types()
        
        # Step 3: Test GameEngine support
        engine_ok = test_game_engine_support()
        
        # Step 4: Test AI bot support
        ai_ok = test_ai_bot_support()
        
        print("\n🎉 PRODUCTION FIX COMPLETE")
        print("=" * 35)
        
        if all_exist and engine_ok and ai_ok:
            print("✅ ALL SYSTEMS GO!")
            print("✅ New games should work now!")
            print("\n🎮 Available Games:")
            for game in GameType.objects.filter(is_active=True).order_by('name'):
                print(f"   • {game.display_name}")
        else:
            print("❌ Some issues remain:")
            if not all_exist:
                print("   - Game types missing in database")
            if not engine_ok:
                print("   - GameEngine sync issues")
            if not ai_ok:
                print("   - AI bot support issues")
        
        print(f"\n📊 Changes Made:")
        print(f"   Created: {created} game types")
        print(f"   Updated: {updated} game types")
        print(f"   Errors: {errors} game types")
        
        print(f"\n💡 Next Steps:")
        print("1. Test Color Match 'Play vs AI' on frontend")
        print("2. Test Memory Card Match 'Play vs AI' on frontend")
        print("3. Both should work without errors now!")
        
    except Exception as e:
        print(f"\n💥 PRODUCTION FIX FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
