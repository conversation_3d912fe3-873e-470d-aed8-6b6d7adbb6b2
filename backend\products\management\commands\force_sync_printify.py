from django.core.management.base import BaseCommand
from django.conf import settings
from printify.api_client import PrintifyAPIClient
from products.models import Product, ProductImage, Category, ProductVariant
from django.utils.text import slugify
import decimal
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Force sync products from Printify (bypasses admin)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=10,
            help='Limit number of products to sync (default: 10)'
        )

    def handle(self, *args, **options):
        limit = options['limit']
        
        self.stdout.write('=== FORCE SYNC PRINTIFY PRODUCTS ===')
        
        try:
            # Check configuration
            api_token = getattr(settings, 'PRINTIFY_API_TOKEN', None)
            shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
            
            if not api_token or not shop_id:
                self.stdout.write(self.style.ERROR('❌ Missing Printify configuration'))
                return
            
            self.stdout.write(f'✅ Configuration OK - Shop ID: {shop_id}')
            
            # Initialize API client
            client = PrintifyAPIClient()
            self.stdout.write('✅ API Client initialized')
            
            # Get products from Printify
            self.stdout.write('📡 Fetching products from Printify...')
            printify_products = client.get_products(shop_id)
            
            # Handle response format
            if isinstance(printify_products, dict) and 'data' in printify_products:
                products_list = printify_products['data']
            elif isinstance(printify_products, list):
                products_list = printify_products
            else:
                self.stdout.write(self.style.ERROR(f'❌ Unexpected response format: {type(printify_products)}'))
                return
            
            self.stdout.write(f'✅ Found {len(products_list)} products in Printify')
            
            # Apply limit
            if limit and limit > 0:
                products_list = products_list[:limit]
                self.stdout.write(f'📊 Limited to {limit} products')
            
            synced_count = 0
            
            # Process each product
            for i, product_data in enumerate(products_list, 1):
                try:
                    printify_id = str(product_data.get('id', ''))
                    if not printify_id:
                        self.stdout.write(f'⚠️ Product {i}: Missing ID, skipping')
                        continue
                    
                    self.stdout.write(f'\n🔄 Processing product {i}/{len(products_list)}: {printify_id}')
                    
                    # Get detailed product information
                    printify_product = client.get_product(shop_id, printify_id)
                    
                    # Extract data
                    title = printify_product.get('title', 'Untitled Product')
                    description = printify_product.get('description', '')
                    slug = slugify(title)
                    variants = printify_product.get('variants', [])
                    images = printify_product.get('images', [])
                    
                    self.stdout.write(f'   📝 Title: {title}')
                    self.stdout.write(f'   🎨 Variants: {len(variants)}')
                    self.stdout.write(f'   🖼️ Images: {len(images)}')
                    
                    # Check if product exists
                    existing_product = Product.objects.filter(printify_id=printify_id).first()
                    
                    if existing_product:
                        # Update existing product
                        existing_product.name = title
                        existing_product.description = description
                        existing_product.slug = slug
                        existing_product.variants_json = variants
                        
                        # Update price from first variant
                        if variants:
                            try:
                                price = decimal.Decimal(str(variants[0].get('price', 0)))
                                existing_product.price = price
                            except (decimal.InvalidOperation, TypeError):
                                existing_product.price = decimal.Decimal('0.00')
                        
                        existing_product.save()
                        self.stdout.write(f'   ✅ Updated existing product')
                        
                        # Update images
                        self._sync_images(existing_product, images)
                        
                    else:
                        # Create new product
                        new_product = Product(
                            name=title,
                            description=description,
                            slug=slug,
                            printify_id=printify_id,
                            stock=100,
                            variants_json=variants
                        )
                        
                        # Set price from first variant
                        if variants:
                            try:
                                price = decimal.Decimal(str(variants[0].get('price', 0)))
                                new_product.price = price
                            except (decimal.InvalidOperation, TypeError):
                                new_product.price = decimal.Decimal('0.00')
                        
                        new_product.save()
                        self.stdout.write(f'   ✅ Created new product')
                        
                        # Add to default category
                        category, created = Category.objects.get_or_create(
                            name='Printify Products',
                            defaults={'slug': 'printify-products', 'is_active': True}
                        )
                        new_product.categories.add(category)
                        
                        # Add images
                        self._sync_images(new_product, images)
                    
                    synced_count += 1
                    
                except Exception as e:
                    self.stdout.write(f'   ❌ Error: {str(e)}')
                    continue
            
            # Final stats
            total_products = Product.objects.count()
            printify_products_count = Product.objects.filter(printify_id__isnull=False).count()
            
            self.stdout.write(f'\n🎉 SYNC COMPLETE!')
            self.stdout.write(f'📊 Synced: {synced_count} products')
            self.stdout.write(f'📊 Total products in DB: {total_products}')
            self.stdout.write(f'📊 Printify products in DB: {printify_products_count}')
            
        except Exception as e:
            self.stdout.write(f'\n❌ SYNC FAILED: {str(e)}')
            import traceback
            self.stdout.write(f'Traceback: {traceback.format_exc()}')
    
    def _sync_images(self, product, images):
        """Sync product images"""
        if not images:
            return
        
        # Clear existing images
        ProductImage.objects.filter(product=product).delete()
        
        # Add new images
        for i, image_data in enumerate(images):
            image_url = image_data.get('src', '')
            if image_url:
                ProductImage.objects.create(
                    product=product,
                    image_url=image_url,
                    is_primary=(i == 0),
                    alt_text=f"{product.name} - Image {i+1}"
                )
        
        self.stdout.write(f'   🖼️ Synced {len(images)} images')
