import logging
from django.shortcuts import render, get_object_or_404
from django.conf import settings
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response

from .models import OrderTracking
from .serializers import OrderTrackingSerializer
from orders.models import Order, OrderItem
from printify.api_client import PrintifyAPIClient

logger = logging.getLogger(__name__)

class IsAdminOrOwner(permissions.BasePermission):
    """
    Permission to only allow owners of an object or admins to access it
    """
    def has_object_permission(self, request, view, obj):
        # Check if user is admin
        if request.user.is_staff:
            return True

        # Check if the object has a user attribute and if it's the current user
        if hasattr(obj, 'user'):
            return obj.user == request.user

        return False


class OrderTrackingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for order tracking operations
    """
    serializer_class = OrderTrackingSerializer
    permission_classes = [permissions.IsAuthenticated, IsAdminOrOwner]

    def get_queryset(self):
        """
        Return tracking information for the current user's orders or all orders for admins
        """
        user = self.request.user
        if user.is_staff:
            return OrderTracking.objects.all()
        return OrderTracking.objects.filter(user=user)

    @action(detail=False, methods=['get'])
    def by_order(self, request):
        """
        Get tracking information for a specific order
        """
        order_id = request.query_params.get('order_id')
        if not order_id:
            return Response(
                {'detail': 'order_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get the order
        try:
            order = Order.objects.get(id=order_id)

            # Check permissions
            if not request.user.is_staff and order.user != request.user:
                return Response(
                    {'detail': 'You do not have permission to view this order'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Get or create tracking information
            tracking_info = OrderTracking.objects.filter(order_id=order_id)

            if not tracking_info.exists():
                # Fetch tracking information from Printify
                tracking_info = self.fetch_tracking_from_printify(order)
                if not tracking_info:
                    return Response(
                        {'detail': 'No tracking information available for this order'},
                        status=status.HTTP_404_NOT_FOUND
                    )

            serializer = self.get_serializer(tracking_info, many=True)
            return Response(serializer.data)

        except Order.DoesNotExist:
            return Response(
                {'detail': 'Order not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting tracking information: {str(e)}")
            return Response(
                {'detail': f'Error getting tracking information: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def refresh(self, request):
        """
        Refresh tracking information for a specific order from Printify
        """
        order_id = request.data.get('order_id')
        if not order_id:
            return Response(
                {'detail': 'order_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            order = Order.objects.get(id=order_id)

            # Check permissions
            if not request.user.is_staff and order.user != request.user:
                return Response(
                    {'detail': 'You do not have permission to refresh this order'},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Fetch tracking information from Printify
            tracking_info = self.fetch_tracking_from_printify(order, force_refresh=True)

            if not tracking_info:
                return Response(
                    {'detail': 'No tracking information available for this order'},
                    status=status.HTTP_404_NOT_FOUND
                )

            serializer = self.get_serializer(tracking_info, many=True)
            return Response(serializer.data)

        except Order.DoesNotExist:
            return Response(
                {'detail': 'Order not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error refreshing tracking information: {str(e)}")
            return Response(
                {'detail': f'Error refreshing tracking information: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def fetch_tracking_from_printify(self, order, force_refresh=False):
        """
        Fetch tracking information from Printify for a specific order

        Args:
            order: The Order object
            force_refresh: Whether to force refresh existing tracking information

        Returns:
            list: A list of OrderTracking objects
        """
        # Initialize Printify API client
        client = PrintifyAPIClient()
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)

        if not shop_id:
            logger.error("No Printify shop ID found in settings")
            return None

        # Get order items with Printify order IDs
        order_items = OrderItem.objects.filter(order=order, printify_order_id__isnull=False)

        if not order_items.exists():
            logger.warning(f"No Printify order IDs found for order {order.id}")
            return None

        tracking_objects = []

        # Process each order item with a Printify order ID
        for item in order_items:
            printify_order_id = item.printify_order_id

            # Check if tracking information already exists
            existing_tracking = OrderTracking.objects.filter(
                order_id=order.id,
                printify_order_id=printify_order_id
            ).first()

            # Skip if tracking exists and we're not forcing a refresh
            if existing_tracking and not force_refresh:
                tracking_objects.append(existing_tracking)
                continue

            # Fetch tracking information from Printify
            tracking_info = client.get_order_tracking(shop_id, printify_order_id)

            if not tracking_info:
                logger.warning(f"No tracking information found for Printify order {printify_order_id}")
                continue

            # Update or create tracking information
            if existing_tracking:
                # Update existing tracking
                existing_tracking.status = tracking_info.get('status', existing_tracking.status)
                existing_tracking.tracking_number = tracking_info.get('tracking_number', existing_tracking.tracking_number)
                existing_tracking.carrier = tracking_info.get('carrier', existing_tracking.carrier)
                existing_tracking.carrier_link = tracking_info.get('carrier_link', existing_tracking.carrier_link)

                if tracking_info.get('estimated_delivery'):
                    existing_tracking.estimated_delivery = tracking_info.get('estimated_delivery')

                existing_tracking.save()
                tracking_objects.append(existing_tracking)
            else:
                # Create new tracking
                new_tracking = OrderTracking.objects.create(
                    user=order.user,
                    order_id=order.id,
                    printify_order_id=printify_order_id,
                    status=tracking_info.get('status', 'pending'),
                    tracking_number=tracking_info.get('tracking_number'),
                    carrier=tracking_info.get('carrier'),
                    carrier_link=tracking_info.get('carrier_link'),
                    estimated_delivery=tracking_info.get('estimated_delivery')
                )
                tracking_objects.append(new_tracking)

        return tracking_objects


@login_required
def order_tracking_view(request, order_id):
    """
    View to display order tracking information
    """
    # Get the order
    order = get_object_or_404(Order, id=order_id)

    # Check permissions
    if not request.user.is_staff and order.user != request.user:
        return render(request, 'order_tracking/access_denied.html')

    # Get tracking information
    tracking_info = OrderTracking.objects.filter(order_id=order_id)

    # If no tracking information exists, try to fetch it from Printify
    if not tracking_info.exists():
        viewset = OrderTrackingViewSet()
        viewset.request = request
        tracking_info = viewset.fetch_tracking_from_printify(order)

    context = {
        'order': order,
        'tracking_info': tracking_info
    }

    return render(request, 'order_tracking/order_tracking.html', context)
