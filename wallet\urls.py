from django.urls import path
from . import views

urlpatterns = [
    # Wallet endpoints
    path('', views.WalletDetailView.as_view(), name='wallet-detail'),
    path('transactions/', views.WalletTransactionListView.as_view(), name='wallet-transactions'),
    path('calculate-redemption/', views.calculate_token_redemption, name='calculate-token-redemption'),
    path('redeem/', views.redeem_tokens, name='redeem-tokens'),

    # Token request endpoints (user)
    path('token-requests/', views.TokenRequestListCreateView.as_view(), name='token-requests'),
    path('token-requests/<uuid:pk>/', views.TokenRequestDetailView.as_view(), name='token-request-detail'),
    path('check-refill-eligibility/', views.check_refill_eligibility, name='check-refill-eligibility'),

    # Token request endpoints (admin)
    path('admin/token-requests/', views.TokenRequestAdminListView.as_view(), name='admin-token-requests'),
    path('admin/token-requests/<uuid:request_id>/approve/', views.approve_token_request, name='approve-token-request'),
    path('admin/token-requests/<uuid:request_id>/reject/', views.reject_token_request, name='reject-token-request'),
]
