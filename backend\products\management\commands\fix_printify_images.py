from django.core.management.base import BaseCommand
from products.models import Product, ProductImage
from printify.models import PrintifyProduct

class Command(BaseCommand):
    help = 'Fix images for Printify products'

    def handle(self, *args, **options):
        # Get all Printify products
        printify_products = PrintifyProduct.objects.all()
        self.stdout.write(f"Found {printify_products.count()} Printify products")
        
        for printify_product in printify_products:
            self.stdout.write(f"Processing Printify product: {printify_product.title} (ID: {printify_product.printify_id})")
            
            # Find corresponding Django product
            try:
                product = Product.objects.get(printify_id=printify_product.printify_id)
                self.stdout.write(f"Found corresponding Django product: {product.name} (ID: {product.id})")
                
                # Get images from Printify product
                images = printify_product.images_json
                if not images:
                    self.stdout.write(self.style.WARNING("No images found in Printify product"))
                    continue
                    
                self.stdout.write(f"Found {len(images)} images in Printify product")
                
                # Delete existing product images
                deleted_count = ProductImage.objects.filter(product=product).delete()
                self.stdout.write(f"Deleted {deleted_count[0]} existing product images")
                
                # Create new product images
                for i, image_data in enumerate(images):
                    image_url = image_data.get('src', '')
                    if image_url:
                        ProductImage.objects.create(
                            product=product,
                            image=image_url,
                            image_url=image_url,
                            is_primary=(i == 0),
                            alt_text=f"{product.name} - Image {i+1}"
                        )
                        self.stdout.write(f"Created image {i+1}: {image_url}")
                    else:
                        self.stdout.write(self.style.WARNING(f"Skipped image {i+1}: No URL found"))
                
                self.stdout.write(self.style.SUCCESS(f"Successfully updated images for {product.name}"))
                
            except Product.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"No Django product found with Printify ID: {printify_product.printify_id}"))
