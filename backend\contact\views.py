from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from .serializers import ContactSerializer
from django.core.mail import send_mail
from django.conf import settings

@api_view(['POST'])
@permission_classes([AllowAny])
def contact_view(request):
    """
    Handle contact form submissions
    """
    serializer = ContactSerializer(data=request.data)

    if serializer.is_valid():
        # Save the contact form submission
        contact = serializer.save()

        # Send email notification (if email settings are configured)
        try:
            subject = f"New Contact Form Submission: {contact.subject}"
            message = f"""
            Name: {contact.name}
            Email: {contact.email}
            Subject: {contact.subject}

            Message:
            {contact.message}
            """
            from_email = settings.DEFAULT_FROM_EMAIL
            recipient_list = [settings.ADMIN_EMAIL]

            send_mail(subject, message, from_email, recipient_list, fail_silently=True)
        except Exception as e:
            # Log the error but don't fail the request
            print(f"Error sending email notification: {str(e)}")

        return Response(
            {"detail": "Your message has been sent successfully. We'll get back to you soon."},
            status=status.HTTP_201_CREATED
        )

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
