#!/usr/bin/env python
"""
Test script for PickMeTrend Gaming System
Run this to verify the gaming system is working correctly
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, Battle, PlayerStats
from gaming.game_logic import GameEngine
from gaming.ai_bot import get_ai_bot
from wallet.models import Wallet, WalletTransaction

def test_game_logic():
    """Test game logic functionality"""
    print("🎮 Testing game logic...")
    
    # Test Rock Paper Scissors
    try:
        game_state = GameEngine.create_initial_state('rock_paper_scissors')
        print("✅ Rock Paper Scissors game state created")
        
        # Simulate a move
        game_state = GameEngine.process_move('rock_paper_scissors', game_state, 'player1', 'rock')
        game_state = GameEngine.process_move('rock_paper_scissors', game_state, 'player2', 'scissors')
        
        if GameEngine.is_game_over('rock_paper_scissors', game_state):
            result = GameEngine.get_game_result('rock_paper_scissors', game_state)
            print(f"✅ Game completed with result: {result}")
        else:
            print("✅ Game logic working correctly")
            
    except Exception as e:
        print(f"❌ Rock Paper Scissors test failed: {e}")
        return False
    
    # Test Number Guessing
    try:
        game_state = GameEngine.create_initial_state('number_guessing')
        print("✅ Number Guessing game state created")
        
        # Simulate moves
        game_state = GameEngine.process_move('number_guessing', game_state, 'player1', 50)
        game_state = GameEngine.process_move('number_guessing', game_state, 'player2', 45)
        
        print("✅ Number Guessing logic working correctly")
        
    except Exception as e:
        print(f"❌ Number Guessing test failed: {e}")
        return False
    
    return True

def test_ai_bot():
    """Test AI bot functionality"""
    print("🤖 Testing AI bot...")
    
    try:
        # Test different difficulty levels
        for difficulty in ['easy', 'medium', 'hard']:
            ai_bot = get_ai_bot(difficulty)
            
            # Test Rock Paper Scissors AI
            game_state = GameEngine.create_initial_state('rock_paper_scissors')
            ai_move = ai_bot.make_move('rock_paper_scissors', game_state)
            
            if ai_move in ['rock', 'paper', 'scissors']:
                print(f"✅ AI bot ({difficulty}) made valid RPS move: {ai_move}")
            else:
                print(f"❌ AI bot ({difficulty}) made invalid RPS move: {ai_move}")
                return False
            
            # Test Number Guessing AI
            game_state = GameEngine.create_initial_state('number_guessing')
            ai_move = ai_bot.make_move('number_guessing', game_state)
            
            if isinstance(ai_move, int) and 1 <= ai_move <= 100:
                print(f"✅ AI bot ({difficulty}) made valid number guess: {ai_move}")
            else:
                print(f"❌ AI bot ({difficulty}) made invalid number guess: {ai_move}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ AI bot test failed: {e}")
        return False

def test_wallet_system():
    """Test wallet and token system"""
    print("🪙 Testing wallet system...")
    
    try:
        # Get or create test user
        test_user, created = User.objects.get_or_create(
            username='test_gaming_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        
        # Get or create wallet
        wallet, created = Wallet.objects.get_or_create(user=test_user)
        initial_balance = wallet.balance
        
        # Test adding tokens
        wallet.add_tokens(100, 'game_win', 'Test win reward')
        wallet.refresh_from_db()
        
        if wallet.balance == initial_balance + 100:
            print("✅ Token addition working correctly")
        else:
            print(f"❌ Token addition failed. Expected: {initial_balance + 100}, Got: {wallet.balance}")
            return False
        
        # Test spending tokens
        if wallet.balance >= 50:
            wallet.spend_tokens(50, 'purchase_redemption', 'Test redemption')
            wallet.refresh_from_db()
            
            if wallet.balance == initial_balance + 50:
                print("✅ Token spending working correctly")
            else:
                print(f"❌ Token spending failed. Expected: {initial_balance + 50}, Got: {wallet.balance}")
                return False
        
        # Test transaction history
        transactions = wallet.transactions.all()
        if transactions.count() >= 2:
            print("✅ Transaction history working correctly")
        else:
            print("❌ Transaction history not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Wallet system test failed: {e}")
        return False

def test_battle_creation():
    """Test battle creation and management"""
    print("⚔️ Testing battle system...")
    
    try:
        # Get game type
        game_type = GameType.objects.filter(name='rock_paper_scissors').first()
        if not game_type:
            print("❌ No game types found. Run setup_gaming_system.py first.")
            return False
        
        # Get test user
        test_user = User.objects.filter(username='test_gaming_user').first()
        if not test_user:
            test_user = User.objects.create_user(
                username='test_gaming_user',
                email='<EMAIL>',
                password='testpass123'
            )
        
        # Create AI battle
        battle = Battle.objects.create(
            game_type=game_type,
            player1=test_user,
            is_ai_battle=True,
            status='waiting'
        )
        
        print(f"✅ Battle created: {battle.id}")
        
        # Test battle start
        battle.start_battle()
        if battle.status == 'in_progress':
            print("✅ Battle start working correctly")
        else:
            print(f"❌ Battle start failed. Status: {battle.status}")
            return False
        
        # Test battle completion
        battle.complete_battle('player1_win')
        if battle.status == 'completed':
            print("✅ Battle completion working correctly")
        else:
            print(f"❌ Battle completion failed. Status: {battle.status}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Battle system test failed: {e}")
        return False

def test_player_stats():
    """Test player statistics system"""
    print("📊 Testing player statistics...")
    
    try:
        # Get test user
        test_user = User.objects.filter(username='test_gaming_user').first()
        if not test_user:
            print("❌ Test user not found")
            return False
        
        # Get or create player stats
        stats, created = PlayerStats.objects.get_or_create(user=test_user)
        
        initial_battles = stats.total_battles
        initial_wins = stats.battles_won
        
        # Test stats update
        stats.update_stats('win', 100)
        stats.refresh_from_db()
        
        if (stats.total_battles == initial_battles + 1 and 
            stats.battles_won == initial_wins + 1):
            print("✅ Player statistics working correctly")
        else:
            print("❌ Player statistics update failed")
            return False
        
        # Test win rate calculation
        win_rate = stats.win_rate
        if isinstance(win_rate, (int, float)) and 0 <= win_rate <= 100:
            print(f"✅ Win rate calculation working: {win_rate}%")
        else:
            print(f"❌ Win rate calculation failed: {win_rate}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Player statistics test failed: {e}")
        return False

def run_all_tests():
    """Run all tests and display results"""
    print("🧪 PickMeTrend Gaming System Tests")
    print("="*40)
    
    tests = [
        ("Game Logic", test_game_logic),
        ("AI Bot", test_ai_bot),
        ("Wallet System", test_wallet_system),
        ("Battle System", test_battle_creation),
        ("Player Statistics", test_player_stats),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} tests...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Display results summary
    print("\n" + "="*40)
    print("📋 TEST RESULTS SUMMARY")
    print("="*40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Gaming system is ready to use.")
        print("\nNext steps:")
        print("1. Start your development server: python manage.py runserver")
        print("2. Start Redis: redis-server")
        print("3. Start frontend: cd frontend && npm start")
        print("4. Visit: http://localhost:3000/gaming")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
        print("Make sure you've run setup_gaming_system.py first.")
    
    return passed == total

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
