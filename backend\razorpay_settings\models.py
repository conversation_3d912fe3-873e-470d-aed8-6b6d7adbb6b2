from django.db import models
from django.core.exceptions import ValidationError


class RazorpaySettings(models.Model):
    """
    Model to manage Razorpay payment gateway modes (Test and Live).

    This model stores both test and live credentials for Razorpay and provides
    a method to get the appropriate credentials based on the current mode.
    """
    is_live = models.BooleanField(
        default=False,
        verbose_name="Live Mode",
        help_text="Toggle between Test (unchecked) and Live (checked) mode"
    )
    test_key_id = models.CharField(
        max_length=255,
        verbose_name="Test Key ID",
        help_text="Razorpay Test Key ID"
    )
    test_key_secret = models.CharField(
        max_length=255,
        verbose_name="Test Key Secret",
        help_text="Razorpay Test Key Secret"
    )
    live_key_id = models.Char<PERSON>ield(
        max_length=255,
        verbose_name="Live Key ID",
        help_text="Razorpay Live Key ID"
    )
    live_key_secret = models.CharField(
        max_length=255,
        verbose_name="Live Key Secret",
        help_text="Razorpay Live Key Secret"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Razorpay Settings"
        verbose_name_plural = "Razorpay Settings"

    def __str__(self):
        mode = "Live" if self.is_live else "Test"
        return f"Razorpay Settings ({mode} Mode)"

    def clean(self):
        """
        Ensure only one instance of RazorpaySettings exists.
        """
        if not self.pk and RazorpaySettings.objects.exists():
            raise ValidationError("Only one Razorpay settings instance can exist.")

    def save(self, *args, **kwargs):
        """
        Override save method to ensure only one instance exists.
        """
        self.clean()
        return super().save(*args, **kwargs)

    def get_keys(self):
        """
        Return the appropriate key_id and key_secret based on the is_live field.

        Returns:
            tuple: A tuple containing (key_id, key_secret)
        """
        if self.is_live:
            return self.live_key_id, self.live_key_secret
        return self.test_key_id, self.test_key_secret
