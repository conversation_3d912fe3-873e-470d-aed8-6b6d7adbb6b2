@echo off
echo 🚀 PUSHING BACKEND TO GITHUB
echo ==============================
echo.

REM Check if we're in the right directory
if not exist "backend" (
    echo ❌ ERROR: Backend folder not found!
    echo Please run this script from your project root directory
    echo Expected: C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion
    pause
    exit /b 1
)

echo ✅ Found backend folder
echo 📁 Navigating to backend...
cd backend

echo 🔧 Initializing Git repository...
git init

echo 🔗 Adding GitHub repository...
git remote add origin https://github.com/phinihas30/pickmetrendofficial-render.git

echo 📦 Adding all files...
git add .

echo 💾 Creating commit...
git commit -m "🎮 Gaming System Complete - Production Ready

✨ New Features:
- Tic Tac Toe game with hard mode AI
- Token economy: Win +5, Draw +2, Loss -1 tokens
- Real-time gaming with WebSocket support
- Wallet system with shopping integration
- Complete e-commerce API

🔧 Technical Stack:
- Django REST API with gaming endpoints
- Redis integration with Upstash
- Token transaction system
- Razorpay live payment integration
- Production-ready deployment config

🚀 Ready for Render deployment!"

echo 🌿 Setting main branch...
git branch -M main

echo 🚀 Pushing to GitHub...
echo.
echo ⚠️  AUTHENTICATION REQUIRED:
echo    Username: phinihas30
echo    Password: Use your Personal Access Token (NOT your GitHub password)
echo.
echo 🔑 Need a token? Go to: https://github.com/settings/tokens
echo.

git push -u origin main

if %errorlevel% equ 0 (
    echo.
    echo 🎉 SUCCESS! Backend pushed to GitHub!
    echo 📁 Check your repository: https://github.com/phinihas30/pickmetrendofficial-render
    echo.
    echo 🚀 Next Steps:
    echo    1. Verify files on GitHub
    echo    2. Deploy to Render
    echo    3. Push frontend code
) else (
    echo.
    echo ❌ Push failed. Common solutions:
    echo    1. Check your internet connection
    echo    2. Verify your GitHub credentials
    echo    3. Make sure you have repository access
    echo.
    echo 🔑 Create Personal Access Token:
    echo    - Go to https://github.com/settings/tokens
    echo    - Generate new token with 'repo' permissions
    echo    - Use token as password when prompted
)

echo.
pause
