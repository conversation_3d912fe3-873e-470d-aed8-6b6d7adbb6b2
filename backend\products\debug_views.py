"""
Debug views to help diagnose production issues.
"""

from django.http import JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.views import View
import traceback
import logging

from .models import Product, ProductImage

logger = logging.getLogger(__name__)

@staff_member_required
def debug_product_info(request, product_id):
    """
    Debug view to get detailed product information
    """
    try:
        # Basic product info
        product = get_object_or_404(Product, id=product_id)
        
        info = {
            'product_id': str(product.id),
            'name': product.name,
            'slug': product.slug,
            'created_at': product.created_at.isoformat(),
            'is_active': product.is_active,
            'price': str(product.price),
            'stock': product.stock,
        }
        
        # Safe field access
        try:
            info['description_length'] = len(product.description) if product.description else 0
        except Exception as e:
            info['description_error'] = str(e)
        
        try:
            info['categories_count'] = product.categories.count()
        except Exception as e:
            info['categories_error'] = str(e)
        
        try:
            info['variants_count'] = product.variants.count()
        except Exception as e:
            info['variants_error'] = str(e)
        
        try:
            info['images_count'] = product.images.count()
            
            # Image details
            images_info = []
            for img in product.images.all()[:5]:  # First 5 images
                img_info = {
                    'id': img.id,
                    'is_primary': img.is_primary,
                    'alt_text': img.alt_text,
                }
                
                # Safe image field access
                try:
                    if img.image:
                        img_info['image_field'] = str(img.image)
                        img_info['image_type'] = type(img.image).__name__
                    else:
                        img_info['image_field'] = None
                except Exception as e:
                    img_info['image_field_error'] = str(e)
                
                # Safe image_url field access
                try:
                    img_info['image_url'] = img.image_url
                except Exception as e:
                    img_info['image_url_error'] = str(e)
                
                images_info.append(img_info)
            
            info['images'] = images_info
            
        except Exception as e:
            info['images_error'] = str(e)
        
        return JsonResponse({
            'status': 'success',
            'data': info
        })
        
    except Product.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'error': 'Product not found'
        }, status=404)
    
    except Exception as e:
        logger.error(f"Error in debug_product_info for {product_id}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=500)

@staff_member_required
def debug_admin_form(request, product_id):
    """
    Debug view to test admin form creation
    """
    try:
        from django.contrib.admin.sites import site
        from django.http import HttpRequest
        
        product = get_object_or_404(Product, id=product_id)
        
        # Create a mock admin request
        admin_request = HttpRequest()
        admin_request.method = 'GET'
        admin_request.user = request.user
        admin_request.META = request.META.copy()
        
        # Get the admin class
        admin_class = site._registry[Product]
        
        result = {
            'product_id': str(product.id),
            'admin_class': admin_class.__class__.__name__,
        }
        
        # Test form creation
        try:
            form_class = admin_class.get_form(admin_request, product)
            result['form_class'] = form_class.__name__
            
            # Test form instantiation
            form = form_class(instance=product)
            result['form_fields'] = list(form.fields.keys())
            result['form_creation'] = 'success'
            
        except Exception as e:
            result['form_error'] = str(e)
            result['form_traceback'] = traceback.format_exc()
        
        # Test inline creation
        try:
            inlines = admin_class.get_inline_instances(admin_request, product)
            result['inlines_count'] = len(inlines)
            
            inline_info = []
            for i, inline in enumerate(inlines):
                inline_data = {
                    'index': i,
                    'class': inline.__class__.__name__,
                }
                
                try:
                    formset_class = inline.get_formset(admin_request, product)
                    formset = formset_class(instance=product)
                    inline_data['formset_creation'] = 'success'
                    inline_data['formset_forms_count'] = len(formset.forms)
                except Exception as e:
                    inline_data['formset_error'] = str(e)
                
                inline_info.append(inline_data)
            
            result['inlines'] = inline_info
            
        except Exception as e:
            result['inlines_error'] = str(e)
            result['inlines_traceback'] = traceback.format_exc()
        
        return JsonResponse({
            'status': 'success',
            'data': result
        })
        
    except Product.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'error': 'Product not found'
        }, status=404)
    
    except Exception as e:
        logger.error(f"Error in debug_admin_form for {product_id}: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=500)

@staff_member_required
def debug_system_info(request):
    """
    Debug view to get system information
    """
    try:
        from django.conf import settings
        import sys
        import os
        
        info = {
            'python_version': sys.version,
            'django_version': getattr(settings, 'DJANGO_VERSION', 'unknown'),
            'debug': settings.DEBUG,
            'environment': os.environ.get('ENVIRONMENT', 'unknown'),
            'database_engine': settings.DATABASES['default']['ENGINE'],
        }
        
        # Check environment variables
        env_vars = [
            'SERVE_MEDIA_IN_PRODUCTION',
            'BASE_URL',
            'DEBUG',
            'SECRET_KEY',
        ]
        
        env_info = {}
        for var in env_vars:
            value = os.environ.get(var)
            if var == 'SECRET_KEY' and value:
                env_info[var] = f"Set ({len(value)} chars)"
            else:
                env_info[var] = value
        
        info['environment_variables'] = env_info
        
        # Check media settings
        media_info = {
            'MEDIA_URL': settings.MEDIA_URL,
            'MEDIA_ROOT': settings.MEDIA_ROOT,
            'STATIC_URL': settings.STATIC_URL,
            'STATIC_ROOT': settings.STATIC_ROOT,
        }
        
        info['media_settings'] = media_info
        
        return JsonResponse({
            'status': 'success',
            'data': info
        })
        
    except Exception as e:
        logger.error(f"Error in debug_system_info: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=500)
