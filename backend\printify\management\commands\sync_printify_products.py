import logging
from django.core.management.base import BaseCommand
from printify.api_client import PrintifyAPIClient
from printify.models import PrintifyProduct

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Sync products from Printify to the local database'

    def add_arguments(self, parser):
        parser.add_argument('--shop_id', type=str, required=True, help='Printify shop ID')
        parser.add_argument('--limit', type=int, default=None, help='Limit the number of products to sync')

    def handle(self, *args, **options):
        shop_id = options['shop_id']
        limit = options['limit']
        
        self.stdout.write(self.style.SUCCESS(f'Starting Printify product sync for shop {shop_id}'))
        
        try:
            # Initialize API client
            client = PrintifyAPIClient()
            
            # Get products from Printify
            self.stdout.write('Fetching products from Printify...')
            printify_products = client.get_products(shop_id)
            
            if not isinstance(printify_products, list):
                # Handle case where the response might be paginated or have a different structure
                if isinstance(printify_products, dict) and 'data' in printify_products:
                    printify_products = printify_products.get('data', [])
                else:
                    self.stdout.write(self.style.ERROR(f'Unexpected response format from Printify: {type(printify_products)}'))
                    return
            
            # Apply limit if specified
            if limit and limit > 0:
                printify_products = printify_products[:limit]
                self.stdout.write(f'Limited to {limit} products')
            
            # Track statistics
            stats = {
                'total': len(printify_products),
                'created': 0,
                'updated': 0,
                'skipped': 0,
                'errors': 0
            }
            
            self.stdout.write(f'Found {stats["total"]} products to process')
            
            # Process each product
            for i, product_data in enumerate(printify_products):
                try:
                    printify_id = str(product_data.get('id'))
                    if not printify_id:
                        self.stdout.write(self.style.WARNING(f'Product missing ID: {product_data}'))
                        stats['skipped'] += 1
                        continue
                    
                    self.stdout.write(f'Processing product {i+1}/{stats["total"]}: {printify_id}')
                    
                    # Get detailed product information
                    detailed_product = client.get_product(shop_id, printify_id)
                    
                    # Extract relevant data
                    title = detailed_product.get('title', '')
                    description = detailed_product.get('description', '')
                    blueprint_id = str(detailed_product.get('blueprint_id', ''))
                    print_provider_id = str(detailed_product.get('print_provider_id', ''))
                    
                    # Extract variants and images
                    variants_json = detailed_product.get('variants', [])
                    images_json = detailed_product.get('images', [])
                    
                    # Try to find existing product
                    try:
                        product = PrintifyProduct.objects.get(printify_id=printify_id)
                        # Update existing product
                        product.title = title
                        product.description = description
                        product.blueprint_id = blueprint_id
                        product.print_provider_id = print_provider_id
                        product.variants_json = variants_json
                        product.images_json = images_json
                        product.save()
                        
                        self.stdout.write(self.style.SUCCESS(f'Updated product: {title} (ID: {printify_id})'))
                        stats['updated'] += 1
                    except PrintifyProduct.DoesNotExist:
                        # Create new product
                        product = PrintifyProduct.objects.create(
                            printify_id=printify_id,
                            title=title,
                            description=description,
                            blueprint_id=blueprint_id,
                            print_provider_id=print_provider_id,
                            variants_json=variants_json,
                            images_json=images_json
                        )
                        
                        self.stdout.write(self.style.SUCCESS(f'Created product: {title} (ID: {printify_id})'))
                        stats['created'] += 1
                        
                except Exception as product_error:
                    self.stdout.write(self.style.ERROR(f'Error processing product {product_data.get("id", "unknown")}: {str(product_error)}'))
                    stats['errors'] += 1
            
            # Print summary
            self.stdout.write(self.style.SUCCESS(
                f'Sync completed. Created: {stats["created"]}, Updated: {stats["updated"]}, '
                f'Skipped: {stats["skipped"]}, Errors: {stats["errors"]}'
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error syncing Printify products: {str(e)}'))
