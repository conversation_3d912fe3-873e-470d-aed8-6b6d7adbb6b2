"""
WebSocket consumers for real-time gaming
"""
import json
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Battle, GameType, GameMove, PlayerStats
from .game_logic import GameEngine
from .ai_bot import get_ai_bot


class BattleConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling battle gameplay
    """
    
    async def connect(self):
        self.battle_id = self.scope['url_route']['kwargs']['battle_id']
        self.battle_group_name = f'battle_{self.battle_id}'
        self.user = self.scope['user']
        
        if not self.user.is_authenticated:
            await self.close()
            return
        
        # Join battle group
        await self.channel_layer.group_add(
            self.battle_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send battle state to user
        battle_data = await self.get_battle_data()
        if battle_data:
            await self.send(text_data=json.dumps({
                'type': 'battle_state',
                'data': battle_data
            }))
    
    async def disconnect(self, close_code):
        # Leave battle group
        await self.channel_layer.group_discard(
            self.battle_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'make_move':
                await self.handle_move(data.get('move'))
            elif message_type == 'start_battle':
                await self.handle_start_battle()
            elif message_type == 'get_state':
                await self.send_battle_state()
                
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': str(e)
            }))
    
    async def handle_move(self, move_data):
        """Handle a player's move"""
        try:
            battle = await self.get_battle()
            if not battle or battle.status != 'in_progress':
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Battle is not in progress'
                }))
                return
            
            # Determine player number
            player_num = 'player1' if battle.player1_id == self.user.id else 'player2'
            
            # Process the move
            updated_state = await self.process_move(battle, player_num, move_data)
            
            # Check if it's an AI battle and AI needs to move
            if battle.is_ai_battle and player_num == 'player1':
                ai_move = await self.get_ai_move(battle, updated_state)
                if ai_move:
                    updated_state = await self.process_move(battle, 'player2', ai_move)
            
            # Broadcast updated state to all players
            await self.channel_layer.group_send(
                self.battle_group_name,
                {
                    'type': 'battle_update',
                    'data': await self.get_battle_data()
                }
            )
            
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': str(e)
            }))
    
    async def handle_start_battle(self):
        """Start the battle"""
        try:
            battle = await self.get_battle()
            if not battle:
                return
            
            if battle.status == 'waiting' and (battle.player2 or battle.is_ai_battle):
                await self.start_battle(battle)
                
                # Broadcast to all players
                await self.channel_layer.group_send(
                    self.battle_group_name,
                    {
                        'type': 'battle_started',
                        'data': await self.get_battle_data()
                    }
                )
                
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': str(e)
            }))
    
    async def send_battle_state(self):
        """Send current battle state to client"""
        battle_data = await self.get_battle_data()
        if battle_data:
            await self.send(text_data=json.dumps({
                'type': 'battle_state',
                'data': battle_data
            }))
    
    # WebSocket message handlers
    async def battle_update(self, event):
        await self.send(text_data=json.dumps({
            'type': 'battle_update',
            'data': event['data']
        }))
    
    async def battle_started(self, event):
        await self.send(text_data=json.dumps({
            'type': 'battle_started',
            'data': event['data']
        }))
    
    async def battle_completed(self, event):
        await self.send(text_data=json.dumps({
            'type': 'battle_completed',
            'data': event['data']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_battle(self):
        try:
            return Battle.objects.select_related('game_type', 'player1', 'player2').get(id=self.battle_id)
        except Battle.DoesNotExist:
            return None
    
    @database_sync_to_async
    def get_battle_data(self):
        try:
            battle = Battle.objects.select_related('game_type', 'player1', 'player2').get(id=self.battle_id)
            return {
                'id': str(battle.id),
                'game_type': battle.game_type.name,
                'game_display_name': battle.game_type.display_name,
                'status': battle.status,
                'result': battle.result,
                'player1': {
                    'id': battle.player1.id,
                    'username': battle.player1.username
                },
                'player2': {
                    'id': battle.player2.id if battle.player2 else None,
                    'username': battle.player2.username if battle.player2 else 'AI Bot'
                } if battle.player2 or battle.is_ai_battle else None,
                'is_ai_battle': battle.is_ai_battle,
                'game_state': battle.game_state,
                'moves_history': battle.moves_history,
                'created_at': battle.created_at.isoformat(),
                'started_at': battle.started_at.isoformat() if battle.started_at else None,
                'completed_at': battle.completed_at.isoformat() if battle.completed_at else None,
            }
        except Battle.DoesNotExist:
            return None
    
    @database_sync_to_async
    def start_battle(self, battle):
        battle.start_battle()
        # Initialize game state
        battle.game_state = GameEngine.create_initial_state(battle.game_type.name)
        battle.save()
    
    @database_sync_to_async
    def process_move(self, battle, player_num, move_data):
        # Create move record
        move_number = len(battle.moves_history) + 1
        GameMove.objects.create(
            battle=battle,
            player=battle.player1 if player_num == 'player1' else battle.player2,
            move_data=move_data,
            move_number=move_number
        )
        
        # Update game state
        battle.game_state = GameEngine.process_move(
            battle.game_type.name,
            battle.game_state,
            player_num,
            move_data.get('move')
        )
        
        # Add to moves history
        battle.moves_history.append({
            'player': player_num,
            'move': move_data,
            'timestamp': timezone.now().isoformat()
        })
        
        # Check if game is over
        if GameEngine.is_game_over(battle.game_type.name, battle.game_state):
            result = GameEngine.get_game_result(battle.game_type.name, battle.game_state)
            battle.complete_battle(result)
            
            # Update player stats
            self.update_player_stats(battle, result)
        
        battle.save()
        return battle.game_state
    
    @database_sync_to_async
    def get_ai_move(self, battle, game_state):
        if not battle.is_ai_battle:
            return None
        
        ai_bot = get_ai_bot()
        move = ai_bot.make_move(battle.game_type.name, game_state)
        
        return {'move': move}
    
    @database_sync_to_async
    def update_player_stats(self, battle, result):
        # Update player1 stats
        stats1, _ = PlayerStats.objects.get_or_create(user=battle.player1)
        
        if result == 'player1_win':
            stats1.update_stats('win', battle.winner_tokens, battle.duration)
        elif result == 'player2_win':
            stats1.update_stats('loss', battle.loser_tokens, battle.duration)
        else:
            stats1.update_stats('draw', battle.winner_tokens, battle.duration)
        
        # Update player2 stats (if not AI)
        if battle.player2 and not battle.is_ai_battle:
            stats2, _ = PlayerStats.objects.get_or_create(user=battle.player2)
            
            if result == 'player2_win':
                stats2.update_stats('win', battle.winner_tokens, battle.duration)
            elif result == 'player1_win':
                stats2.update_stats('loss', battle.loser_tokens, battle.duration)
            else:
                stats2.update_stats('draw', battle.loser_tokens, battle.duration)


class MatchmakingConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for matchmaking
    """

    async def connect(self):
        self.user = self.scope['user']

        # Accept connection first, then check authentication for specific actions
        await self.accept()

        if self.user.is_authenticated:
            self.matchmaking_group = f'matchmaking_{self.user.id}'

            # Join personal matchmaking group
            await self.channel_layer.group_add(
                self.matchmaking_group,
                self.channel_name
            )

            # Send connection success
            await self.send(text_data=json.dumps({
                'type': 'connected',
                'authenticated': True,
                'user': self.user.username
            }))
        else:
            # Send connection success but not authenticated
            await self.send(text_data=json.dumps({
                'type': 'connected',
                'authenticated': False,
                'message': 'Please log in to play games'
            }))

    async def disconnect(self, close_code):
        # Import here to avoid circular import
        from .matchmaking import MatchmakingService

        # Remove from matchmaking queue
        await MatchmakingService.cancel_matchmaking(self.user)

        # Leave group
        await self.channel_layer.group_discard(
            self.matchmaking_group,
            self.channel_name
        )

    async def receive(self, text_data):
        try:
            # Import here to avoid circular import
            from .matchmaking import MatchmakingService

            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'find_match':
                await self.handle_find_match(data.get('game_type'))
            elif message_type == 'create_ai_battle':
                await self.handle_create_ai_battle(data.get('game_type'))
            elif message_type == 'cancel_matchmaking':
                await self.handle_cancel_matchmaking()
            elif message_type == 'get_queue_status':
                await self.handle_get_queue_status()

        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': str(e)
            }))

    async def handle_find_match(self, game_type):
        """Handle finding a match"""
        from .matchmaking import MatchmakingService

        battle = await MatchmakingService.find_match(self.user, game_type)

        if battle:
            # Match found!
            await self.send(text_data=json.dumps({
                'type': 'match_found',
                'battle_id': str(battle.id),
                'opponent': battle.player1.username if battle.player1.id != self.user.id else battle.player2.username
            }))
        else:
            # Added to queue
            await self.send(text_data=json.dumps({
                'type': 'added_to_queue',
                'game_type': game_type
            }))

    async def handle_create_ai_battle(self, game_type):
        """Handle creating AI battle"""
        if not self.user.is_authenticated:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Authentication required to play games'
            }))
            return

        from .matchmaking import MatchmakingService

        battle = await MatchmakingService.create_ai_battle(self.user, game_type)

        await self.send(text_data=json.dumps({
            'type': 'ai_battle_created',
            'battle_id': str(battle.id)
        }))

    async def handle_cancel_matchmaking(self):
        """Handle canceling matchmaking"""
        from .matchmaking import MatchmakingService

        await MatchmakingService.cancel_matchmaking(self.user)

        await self.send(text_data=json.dumps({
            'type': 'matchmaking_cancelled'
        }))

    async def handle_get_queue_status(self):
        """Handle getting queue status"""
        from .matchmaking import MatchmakingService

        status = await MatchmakingService.get_queue_status(self.user)

        await self.send(text_data=json.dumps({
            'type': 'queue_status',
            'data': status
        }))
