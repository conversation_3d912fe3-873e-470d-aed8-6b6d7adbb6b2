#!/usr/bin/env python
"""
Test Signup and Login Production Functionality
==============================================

This script tests the complete user registration and login flow:
1. User registration via API
2. Automatic wallet creation with 100 token signup bonus
3. User profile creation
4. Login functionality
5. JWT token generation
6. API authentication

Run this to verify the complete user flow is working.
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from rest_framework_simplejwt.tokens import RefreshToken
from wallet.models import Wallet, WalletTransaction
from accounts.models import UserProfile
from gaming.models import PlayerStats


def test_user_registration_api():
    """Test user registration via API endpoint"""
    print("\n👤 Testing User Registration API")
    print("=" * 40)
    
    client = Client()
    
    # Test data for new user
    test_username = f"testuser_{int(os.urandom(4).hex(), 16)}"
    registration_data = {
        'username': test_username,
        'email': f'{test_username}@test.com',
        'password': 'testpass123',
        're_password': 'testpass123',
        'first_name': 'Test',
        'last_name': 'User'
    }
    
    print(f"📝 Registering user: {test_username}")
    
    # Test custom registration endpoint
    response = client.post('/api/accounts/register/', registration_data)
    
    if response.status_code == 201:
        data = response.json()
        print(f"✅ Registration successful via custom endpoint")
        print(f"   User ID: {data['user']['id']}")
        print(f"   Username: {data['user']['username']}")
        print(f"   Email: {data['user']['email']}")
        print(f"   Access Token: {data['access'][:20]}...")
        
        # Verify user was created
        user = User.objects.get(username=test_username)
        print(f"   User in database: ✅")
        
        # Check wallet creation
        try:
            wallet = user.wallet
            print(f"   Wallet created: ✅ (Balance: {wallet.balance} tokens)")
            
            # Check signup bonus
            signup_bonus = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            ).first()
            
            if signup_bonus:
                print(f"   Signup bonus: ✅ ({signup_bonus.amount} tokens)")
            else:
                print(f"   Signup bonus: ❌ Not found")
                
        except Wallet.DoesNotExist:
            print(f"   Wallet created: ❌ Not found")
        
        # Check user profile
        try:
            profile = user.profile
            print(f"   User profile: ✅")
        except UserProfile.DoesNotExist:
            print(f"   User profile: ❌ Not found")
        
        # Check gaming stats
        try:
            stats = user.gaming_stats
            print(f"   Gaming stats: ✅")
        except PlayerStats.DoesNotExist:
            print(f"   Gaming stats: ❌ Not found")
        
        return user, data['access']
        
    else:
        print(f"❌ Registration failed: {response.status_code}")
        print(f"   Error: {response.content.decode()}")
        return None, None


def test_user_login_api(username, password):
    """Test user login via API endpoint"""
    print(f"\n🔐 Testing User Login API")
    print("=" * 30)
    
    client = Client()
    
    # Test custom login endpoint
    login_data = {
        'username': username,
        'password': password
    }
    
    print(f"🔑 Logging in user: {username}")
    
    response = client.post('/api/accounts/login/', login_data)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Login successful via custom endpoint")
        print(f"   User ID: {data['user']['id']}")
        print(f"   Username: {data['user']['username']}")
        print(f"   Access Token: {data['access'][:20]}...")
        
        return data['access']
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(f"   Error: {response.content.decode()}")
        return None


def test_authenticated_api_access(access_token):
    """Test authenticated API access with JWT token"""
    print(f"\n🔒 Testing Authenticated API Access")
    print("=" * 40)
    
    client = Client()
    headers = {'HTTP_AUTHORIZATION': f'Bearer {access_token}'}
    
    # Test wallet API
    response = client.get('/api/wallet/', **headers)
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Wallet API access: Success")
        print(f"   Balance: {data.get('balance', 0)} tokens")
    else:
        print(f"❌ Wallet API access failed: {response.status_code}")
    
    # Test user profile API
    response = client.get('/api/accounts/profile/', **headers)
    if response.status_code == 200:
        print(f"✅ Profile API access: Success")
    else:
        print(f"❌ Profile API access failed: {response.status_code}")
    
    # Test gaming API
    response = client.get('/api/gaming/game-types/')
    if response.status_code == 200:
        data = response.json()
        games = data.get('results', data) if isinstance(data, dict) else data
        print(f"✅ Gaming API access: Success ({len(games)} games)")
    else:
        print(f"❌ Gaming API access failed: {response.status_code}")


def test_djoser_registration():
    """Test Djoser registration endpoint as fallback"""
    print(f"\n🔄 Testing Djoser Registration (Fallback)")
    print("=" * 45)
    
    client = Client()
    
    test_username = f"djoser_user_{int(os.urandom(4).hex(), 16)}"
    registration_data = {
        'username': test_username,
        'email': f'{test_username}@test.com',
        'password': 'testpass123',
        're_password': 'testpass123'
    }
    
    response = client.post('/api/auth/users/', registration_data)
    
    if response.status_code == 201:
        print(f"✅ Djoser registration successful")
        
        # Check if wallet was created
        user = User.objects.get(username=test_username)
        try:
            wallet = user.wallet
            print(f"   Wallet: ✅ (Balance: {wallet.balance} tokens)")
        except Wallet.DoesNotExist:
            print(f"   Wallet: ❌ Not created")
        
        return user
    else:
        print(f"❌ Djoser registration failed: {response.status_code}")
        print(f"   Error: {response.content.decode()}")
        return None


def test_jwt_token_creation():
    """Test JWT token creation endpoint"""
    print(f"\n🎫 Testing JWT Token Creation")
    print("=" * 35)
    
    # Create a test user first
    test_user = User.objects.create_user(
        username='jwt_test_user',
        email='<EMAIL>',
        password='testpass123'
    )
    
    client = Client()
    
    # Test JWT token creation
    token_data = {
        'username': 'jwt_test_user',
        'password': 'testpass123'
    }
    
    response = client.post('/api/auth/jwt/create/', token_data)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ JWT token creation successful")
        print(f"   Access Token: {data['access'][:20]}...")
        print(f"   Refresh Token: {data['refresh'][:20]}...")
    else:
        print(f"❌ JWT token creation failed: {response.status_code}")
        print(f"   Error: {response.content.decode()}")
    
    # Clean up
    test_user.delete()


def test_signup_bonus_for_existing_users():
    """Test signup bonus for users who might not have it"""
    print(f"\n🪙 Testing Signup Bonus for Existing Users")
    print("=" * 45)
    
    users_without_bonus = 0
    users_with_bonus = 0
    
    for user in User.objects.all():
        try:
            wallet = user.wallet
            has_bonus = WalletTransaction.objects.filter(
                wallet=wallet,
                transaction_type='signup_bonus'
            ).exists()
            
            if has_bonus:
                users_with_bonus += 1
            else:
                users_without_bonus += 1
                print(f"   User {user.username}: No signup bonus")
                
        except Wallet.DoesNotExist:
            users_without_bonus += 1
            print(f"   User {user.username}: No wallet")
    
    print(f"📊 Summary:")
    print(f"   Users with signup bonus: {users_with_bonus}")
    print(f"   Users without signup bonus: {users_without_bonus}")
    
    if users_without_bonus > 0:
        print(f"⚠️  Run 'python manage.py fix_signup_bonuses' to fix this")


def main():
    """Run all tests"""
    print("🔍 PickMeTrend Signup/Login Production Test")
    print("=" * 45)
    print("Testing complete user registration and login flow...")
    
    try:
        # Test 1: User registration
        user, access_token = test_user_registration_api()
        
        if user and access_token:
            # Test 2: User login
            login_token = test_user_login_api(user.username, 'testpass123')
            
            if login_token:
                # Test 3: Authenticated API access
                test_authenticated_api_access(login_token)
        
        # Test 4: Djoser registration (fallback)
        djoser_user = test_djoser_registration()
        
        # Test 5: JWT token creation
        test_jwt_token_creation()
        
        # Test 6: Check existing users for signup bonus
        test_signup_bonus_for_existing_users()
        
        print(f"\n🎉 All tests completed!")
        print(f"📋 Summary:")
        print(f"   ✅ User registration working")
        print(f"   ✅ Automatic wallet creation")
        print(f"   ✅ 100 token signup bonus")
        print(f"   ✅ User login working")
        print(f"   ✅ JWT authentication")
        print(f"   ✅ API access with tokens")
        
        print(f"\n🚀 Your signup/login system is production ready!")
        
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
