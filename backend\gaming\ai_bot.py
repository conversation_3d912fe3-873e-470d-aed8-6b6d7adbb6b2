"""
AI Bot logic for different game types
"""
import random
from typing import Dict, <PERSON>, <PERSON>
from django.conf import settings


class AIBot:
    """
    AI Bot that can play different games
    """
    
    def __init__(self, difficulty: str = 'medium'):
        self.difficulty = difficulty.lower()
        if self.difficulty not in ['easy', 'medium', 'hard']:
            self.difficulty = 'medium'
    
    def make_move(self, game_type: str, game_state: Dict[str, Any]) -> Union[str, int]:
        """
        Make a move based on game type and current state
        """
        if game_type == 'rock_paper_scissors':
            return self._rock_paper_scissors_move(game_state)
        elif game_type == 'number_guessing':
            return self._number_guessing_move(game_state)
        else:
            raise ValueError(f"Unknown game type: {game_type}")
    
    def _rock_paper_scissors_move(self, game_state: Dict[str, Any]) -> str:
        """
        AI strategy for Rock Paper Scissors
        """
        choices = ['rock', 'paper', 'scissors']
        
        if self.difficulty == 'easy':
            # Easy: Completely random
            return random.choice(choices)
        
        elif self.difficulty == 'medium':
            # Medium: Slight pattern recognition
            round_results = game_state.get('round_results', [])
            
            if len(round_results) == 0:
                return random.choice(choices)
            
            # Look at player's last move and try to counter
            last_result = round_results[-1]
            player_last_move = last_result.get('player1_move')
            
            if player_last_move:
                # 70% chance to counter, 30% random
                if random.random() < 0.7:
                    counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    }
                    return counters.get(player_last_move, random.choice(choices))
            
            return random.choice(choices)
        
        elif self.difficulty == 'hard':
            # Hard: Advanced pattern recognition
            round_results = game_state.get('round_results', [])
            
            if len(round_results) < 2:
                return random.choice(choices)
            
            # Analyze player's patterns
            player_moves = [r.get('player1_move') for r in round_results]
            
            # Look for patterns in last 3 moves
            if len(player_moves) >= 3:
                last_three = player_moves[-3:]
                
                # Check for repetition
                if last_three[0] == last_three[1] == last_three[2]:
                    # Player is repeating, counter it
                    counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    }
                    return counters.get(last_three[0], random.choice(choices))
                
                # Check for alternating pattern
                if len(set(last_three)) == 2:
                    # Predict next in pattern
                    most_common = max(set(last_three), key=last_three.count)
                    counters = {
                        'rock': 'paper',
                        'paper': 'scissors',
                        'scissors': 'rock'
                    }
                    return counters.get(most_common, random.choice(choices))
            
            # Default to countering last move
            last_move = player_moves[-1]
            counters = {
                'rock': 'paper',
                'paper': 'scissors',
                'scissors': 'rock'
            }
            return counters.get(last_move, random.choice(choices))
    
    def _number_guessing_move(self, game_state: Dict[str, Any]) -> int:
        """
        AI strategy for Number Guessing
        """
        target = game_state.get('target_number', 50)
        round_results = game_state.get('round_results', [])
        
        if self.difficulty == 'easy':
            # Easy: Random guess with some bias toward center
            if random.random() < 0.3:
                return random.randint(40, 60)  # 30% chance for center bias
            return random.randint(1, 100)
        
        elif self.difficulty == 'medium':
            # Medium: Some strategy based on previous rounds
            if len(round_results) == 0:
                return random.randint(25, 75)  # Start with middle range
            
            # Look at player's guessing patterns
            player_guesses = [r.get('player1_guess') for r in round_results if r.get('player1_guess')]
            
            if player_guesses:
                avg_guess = sum(player_guesses) / len(player_guesses)
                
                # Try to guess close to player's average but with some randomness
                base_guess = int(avg_guess)
                variation = random.randint(-15, 15)
                guess = max(1, min(100, base_guess + variation))
                return guess
            
            return random.randint(1, 100)
        
        elif self.difficulty == 'hard':
            # Hard: Advanced strategy
            if len(round_results) == 0:
                return random.randint(35, 65)  # Start with optimal range
            
            # Analyze player patterns and target distributions
            player_guesses = [r.get('player1_guess') for r in round_results if r.get('player1_guess')]
            targets = [r.get('target_number') for r in round_results if r.get('target_number')]
            
            if len(player_guesses) >= 2:
                # Calculate player's tendency
                avg_guess = sum(player_guesses) / len(player_guesses)
                
                # Calculate how close player usually gets
                distances = []
                for i, result in enumerate(round_results):
                    if result.get('player1_guess') and result.get('target_number'):
                        dist = abs(result['target_number'] - result['player1_guess'])
                        distances.append(dist)
                
                if distances:
                    avg_distance = sum(distances) / len(distances)
                    
                    # Try to guess within the average distance of the target
                    # but use some prediction of where target might be
                    if len(targets) >= 2:
                        target_avg = sum(targets) / len(targets)
                        # Guess near the historical target average
                        base_guess = int(target_avg)
                    else:
                        base_guess = 50  # Default to middle
                    
                    # Add some strategic variation
                    variation = random.randint(-int(avg_distance), int(avg_distance))
                    guess = max(1, min(100, base_guess + variation))
                    return guess
            
            return random.randint(30, 70)


def get_ai_bot(difficulty: str = None) -> AIBot:
    """
    Get an AI bot instance with specified difficulty
    """
    if difficulty is None:
        difficulty = settings.GAMING_SETTINGS.get('AI_BOT_DIFFICULTY', 'medium')
    
    return AIBot(difficulty)
