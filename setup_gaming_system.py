#!/usr/bin/env python
"""
Quick setup script for PickMeTrend Gaming System
Run this after installing dependencies and running migrations
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, PlayerStats
from wallet.models import Wallet

def setup_game_types():
    """Setup initial game types"""
    print("🎮 Setting up game types...")
    
    game_types = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic rock, paper, scissors game. Best of 3 rounds wins!',
            'rules': {
                'max_rounds': 3,
                'choices': ['rock', 'paper', 'scissors'],
                'winning_combinations': {
                    'rock': 'scissors',
                    'paper': 'rock',
                    'scissors': 'paper'
                }
            }
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number between 1-100. Closest guess wins the round!',
            'rules': {
                'max_rounds': 5,
                'range': [1, 100],
                'scoring': 'closest_wins'
            }
        }
    ]

    for game_data in game_types:
        game_type, created = GameType.objects.get_or_create(
            name=game_data['name'],
            defaults={
                'display_name': game_data['display_name'],
                'description': game_data['description'],
                'rules': game_data['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created game type: {game_type.display_name}")
        else:
            print(f"⚠️  Game type already exists: {game_type.display_name}")

def setup_user_wallets():
    """Create wallets for existing users"""
    print("🪙 Setting up user wallets...")
    
    users_without_wallets = User.objects.filter(wallet__isnull=True)
    count = 0
    
    for user in users_without_wallets:
        Wallet.objects.create(user=user)
        count += 1
    
    if count > 0:
        print(f"✅ Created wallets for {count} users")
    else:
        print("ℹ️  All users already have wallets")

def setup_player_stats():
    """Create player stats for existing users"""
    print("📊 Setting up player statistics...")
    
    users_without_stats = User.objects.filter(gaming_stats__isnull=True)
    count = 0
    
    for user in users_without_stats:
        PlayerStats.objects.create(user=user)
        count += 1
    
    if count > 0:
        print(f"✅ Created player stats for {count} users")
    else:
        print("ℹ️  All users already have player stats")

def create_demo_data():
    """Create demo data for testing"""
    print("🧪 Creating demo data...")
    
    # Create a demo user if it doesn't exist
    demo_user, created = User.objects.get_or_create(
        username='demo_player',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Demo',
            'last_name': 'Player',
            'is_active': True
        }
    )
    
    if created:
        demo_user.set_password('demo123')
        demo_user.save()
        print("✅ Created demo user: demo_player (password: demo123)")
        
        # Add some demo tokens
        if hasattr(demo_user, 'wallet'):
            demo_user.wallet.add_tokens(
                amount=500,
                transaction_type='bonus',
                description='Welcome bonus for demo user'
            )
            print("✅ Added 500 demo tokens to demo user")
    else:
        print("ℹ️  Demo user already exists")

def check_redis_connection():
    """Check if Redis is available"""
    print("🔗 Checking Redis connection...")
    
    try:
        import redis
        from django.conf import settings
        
        # Try to connect to Redis
        redis_url = getattr(settings, 'CELERY_BROKER_URL', 'redis://localhost:6379/0')
        r = redis.from_url(redis_url)
        r.ping()
        print("✅ Redis connection successful")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        print("⚠️  WebSocket features may not work without Redis")
        return False

def display_setup_summary():
    """Display setup summary and next steps"""
    print("\n" + "="*60)
    print("🎉 GAMING SYSTEM SETUP COMPLETE!")
    print("="*60)
    
    print("\n📋 SUMMARY:")
    print("✅ Game types configured")
    print("✅ User wallets created")
    print("✅ Player statistics initialized")
    print("✅ Demo data created")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Start your Django development server:")
    print("   python manage.py runserver")
    
    print("\n2. Start Redis server (for WebSocket support):")
    print("   redis-server")
    
    print("\n3. Start your React frontend:")
    print("   cd frontend && npm start")
    
    print("\n4. Test the gaming system:")
    print("   - Visit http://localhost:3000/gaming")
    print("   - Login with demo user: demo_player / demo123")
    print("   - Try playing against AI")
    
    print("\n🎮 AVAILABLE GAMES:")
    for game_type in GameType.objects.filter(is_active=True):
        print(f"   - {game_type.display_name}: {game_type.description}")
    
    print("\n🪙 TOKEN ECONOMY:")
    print("   - Win: 100 tokens")
    print("   - Draw: 50 tokens") 
    print("   - Participation: 10 tokens")
    print("   - 1 token = ₹0.10 discount")
    
    print("\n📚 DOCUMENTATION:")
    print("   - See GAMING_SYSTEM_SETUP.md for detailed documentation")
    print("   - API endpoints: /api/gaming/ and /api/wallet/")
    print("   - WebSocket endpoints: /ws/battle/ and /ws/matchmaking/")
    
    print("\n" + "="*60)

def main():
    """Main setup function"""
    print("🎮 PickMeTrend Gaming System Setup")
    print("="*40)
    
    try:
        # Run setup steps
        setup_game_types()
        setup_user_wallets()
        setup_player_stats()
        create_demo_data()
        check_redis_connection()
        
        # Display summary
        display_setup_summary()
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print("Please check your Django configuration and try again.")
        sys.exit(1)

if __name__ == '__main__':
    main()
