from django.core.management.base import BaseCommand
from django.contrib.auth.models import User

class Command(BaseCommand):
    help = 'Force create the phinihas superuser account'

    def handle(self, *args, **options):
        # Delete all existing users
        User.objects.all().delete()
        self.stdout.write('Deleted all existing users')
        
        # Create your specific user
        user = User.objects.create_superuser(
            username='phinihas',
            email='<EMAIL>',
            password='15Sixteen@'
        )
        user.is_staff = True
        user.is_superuser = True
        user.is_active = True
        user.save()
        
        self.stdout.write(self.style.SUCCESS(f'Created superuser: {user.username}'))
        self.stdout.write(f'is_staff: {user.is_staff}')
        self.stdout.write(f'is_superuser: {user.is_superuser}')
        self.stdout.write(f'is_active: {user.is_active}')
        self.stdout.write(self.style.SUCCESS('User creation completed!'))
