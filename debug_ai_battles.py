#!/usr/bin/env python
"""
Debug AI Battles
================

This script helps debug AI battle creation issues by testing all components.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, Battle
from gaming.game_logic import GameEngine
from gaming.ai_bot import AIBot
from wallet.models import Wallet
import json


def check_game_types():
    """Check all game types in database"""
    print("🎮 Checking Game Types")
    print("=" * 30)
    
    game_types = GameType.objects.all()
    print(f"Total game types: {game_types.count()}")
    
    for game in game_types:
        print(f"\n📋 {game.display_name}")
        print(f"   Name: {game.name}")
        print(f"   Active: {game.is_active}")
        print(f"   Description: {game.description}")
        print(f"   Rules: {game.rules}")
        print(f"   Created: {game.created_at}")


def check_game_engine():
    """Check if GameEngine supports all games"""
    print("\n🔧 Checking Game Engine")
    print("=" * 25)
    
    engine = GameEngine()
    supported_games = list(engine.GAME_CLASSES.keys())
    
    print(f"Supported games in GameEngine: {supported_games}")
    
    # Check each game type in database
    for game_type in GameType.objects.filter(is_active=True):
        if game_type.name in supported_games:
            print(f"✅ {game_type.display_name}: Supported")
            
            # Test game initialization
            try:
                game_class = engine.GAME_CLASSES[game_type.name]
                initial_state = game_class.get_initial_state()
                print(f"   Initial state keys: {list(initial_state.keys())}")
            except Exception as e:
                print(f"   ❌ Error initializing: {e}")
        else:
            print(f"❌ {game_type.display_name}: NOT SUPPORTED")


def check_ai_bot():
    """Check if AI bot supports all games"""
    print("\n🤖 Checking AI Bot")
    print("=" * 20)
    
    ai_bot = AIBot()
    
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing {game_type.display_name}:")
        
        try:
            # Test AI move generation
            if game_type.name == 'color_match':
                test_state = {
                    'colors': ['red', 'blue', 'green'],
                    'sequence': ['red', 'blue'],
                    'player2_sequence': []
                }
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
            elif game_type.name == 'memory_card':
                test_state = {
                    'cards': ['🐶', '🐱', '🐶', '🐱'],
                    'revealed': [False, False, False, False],
                    'matched': [False, False, False, False],
                    'selected_cards': []
                }
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
            elif game_type.name == 'rock_paper_scissors':
                test_state = {'round': 1}
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
            elif game_type.name == 'number_guessing':
                test_state = {'round': 1, 'target_number': 50}
                move = ai_bot.get_move(game_type.name, test_state)
                print(f"   ✅ AI move: {move}")
                
            else:
                print(f"   ⚠️ No test case for {game_type.name}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")


def test_ai_battle_creation():
    """Test actual AI battle creation"""
    print("\n🧪 Testing AI Battle Creation")
    print("=" * 35)
    
    # Get test user
    test_user = User.objects.first()
    if not test_user:
        print("❌ No users found for testing")
        return
    
    print(f"Testing with user: {test_user.username}")
    
    # Check user's wallet
    try:
        wallet = test_user.wallet
        print(f"User wallet balance: {wallet.balance} tokens")
        
        if wallet.balance <= 0:
            print("⚠️ User has no tokens, adding test tokens...")
            wallet.add_tokens(50, 'test', 'Test tokens for AI battle')
            print(f"Added tokens, new balance: {wallet.balance}")
            
    except Wallet.DoesNotExist:
        print("❌ User has no wallet")
        return
    
    # Test each game type
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing {game_type.display_name}:")
        
        try:
            # Test battle creation (without async)
            battle = Battle.objects.create(
                game_type=game_type,
                player1=test_user,
                is_ai_battle=True,
                status='waiting'
            )
            
            print(f"   ✅ Battle created: {battle.id}")
            print(f"   Game type: {battle.game_type.name}")
            print(f"   Player1: {battle.player1.username}")
            print(f"   AI battle: {battle.is_ai_battle}")
            print(f"   Status: {battle.status}")
            
            # Clean up test battle
            battle.delete()
            print(f"   🗑️ Test battle cleaned up")
            
        except Exception as e:
            print(f"   ❌ Error creating battle: {e}")


def test_api_endpoint():
    """Test the AI battle creation API endpoint"""
    print("\n🌐 Testing API Endpoint")
    print("=" * 25)
    
    from django.test import Client
    from django.contrib.auth.models import User
    import json
    
    client = Client()
    
    # Get test user
    test_user = User.objects.first()
    if not test_user:
        print("❌ No users found for testing")
        return
    
    # Login as test user
    client.force_login(test_user)
    print(f"Logged in as: {test_user.username}")
    
    # Test each game type
    for game_type in GameType.objects.filter(is_active=True):
        print(f"\n🎮 Testing API for {game_type.display_name}:")
        
        try:
            response = client.post('/api/gaming/create-ai-battle/', 
                                 data=json.dumps({'game_type': game_type.name}),
                                 content_type='application/json')
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                response_data = json.loads(response.content.decode())
                print(f"   ✅ Success: {response_data}")
                
                # Clean up created battle
                if 'battle_id' in response_data:
                    try:
                        battle = Battle.objects.get(id=response_data['battle_id'])
                        battle.delete()
                        print(f"   🗑️ Cleaned up battle")
                    except:
                        pass
            else:
                print(f"   ❌ Error: {response.content.decode()}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")


def main():
    """Main debug function"""
    print("🔧 AI Battle Debug Script")
    print("=" * 50)
    
    # Check all components
    check_game_types()
    check_game_engine()
    check_ai_bot()
    test_ai_battle_creation()
    test_api_endpoint()
    
    print("\n📋 Debug Summary")
    print("=" * 20)
    
    # Count issues
    game_types_count = GameType.objects.filter(is_active=True).count()
    engine_games_count = len(GameEngine.GAME_CLASSES)
    
    print(f"Active game types: {game_types_count}")
    print(f"Engine supported games: {engine_games_count}")
    
    if game_types_count == engine_games_count:
        print("✅ Game types and engine are in sync")
    else:
        print("❌ Mismatch between game types and engine")
    
    print("\n💡 If you're still getting errors:")
    print("1. Run: python add_new_games.py")
    print("2. Check Django logs for detailed error messages")
    print("3. Verify your database has the new game types")
    print("4. Test with a user who has tokens")


if __name__ == '__main__':
    main()
