from django.shortcuts import render
from rest_framework import status, generics, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth.models import User
from .serializers import UserSerializer, ChangePasswordSerializer, UserCreateSerializer
from .models import UserProfile
from rest_framework_simplejwt.tokens import RefreshToken


class UserProfileView(generics.RetrieveUpdateAPIView):
    """
    View for retrieving and updating user profile
    """
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

    def update(self, request, *args, **kwargs):
        print(f"Profile update request data: {request.data}")
        response = super().update(request, *args, **kwargs)
        print(f"Profile update response: {response.data if hasattr(response, 'data') else 'No data'}")
        return response


class ChangePasswordView(APIView):
    """
    View for changing password
    """
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)

        if serializer.is_valid():
            user = request.user

            # Check old password
            if not user.check_password(serializer.validated_data['old_password']):
                return Response({'error': 'Wrong password.'}, status=status.HTTP_400_BAD_REQUEST)

            # Set new password
            user.set_password(serializer.validated_data['new_password'])
            user.save()

            return Response({'status': 'password changed'}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RegisterView(APIView):
    """
    Custom view for user registration without email confirmation
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        print(f"Registration attempt with data: {request.data}")

        serializer = UserCreateSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            user.is_active = True  # Make user active immediately
            user.save()

            print(f"User created: {user.username} (ID: {user.id})")

            # Ensure all related objects are created
            try:
                # Create UserProfile if it doesn't exist
                if not hasattr(user, 'profile'):
                    from .models import UserProfile
                    UserProfile.objects.create(user=user)
                    print(f"✅ Created profile for {user.username}")

                # Create Wallet if it doesn't exist
                try:
                    from wallet.models import Wallet
                    if not hasattr(user, 'wallet'):
                        Wallet.give_signup_bonus(user)
                        print(f"✅ Created wallet and gave signup bonus to {user.username}")
                except Exception as e:
                    print(f"❌ Error creating wallet for {user.username}: {e}")

                # Create PlayerStats if it doesn't exist
                try:
                    from gaming.models import PlayerStats
                    if not hasattr(user, 'gaming_stats'):
                        PlayerStats.objects.create(user=user)
                        print(f"✅ Created gaming stats for {user.username}")
                except Exception as e:
                    print(f"❌ Error creating gaming stats for {user.username}: {e}")

            except Exception as e:
                print(f"❌ Error in post-registration setup for {user.username}: {e}")

            # Generate token for the user
            refresh = RefreshToken.for_user(user)

            # Refresh user from database to get all related objects
            user.refresh_from_db()

            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data,
                'message': 'Registration successful! You received 100 welcome tokens.'
            }, status=status.HTTP_201_CREATED)

        print(f"Registration failed with errors: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LoginView(APIView):
    """
    Custom login view for better error handling
    Supports login with either username or email
    """
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        print(f"Login attempt with data: {request.data}")

        email = request.data.get('email')
        username = request.data.get('username')
        password = request.data.get('password')

        print(f"Login credentials - Email: {email}, Username: {username}, Password: {'*' * len(password) if password else None}")

        if not password:
            print("Login failed: Password is required")
            return Response({'error': 'Password is required'},
                            status=status.HTTP_400_BAD_REQUEST)

        if not email and not username:
            print("Login failed: No email or username provided")
            return Response({'error': 'Please provide either email or username'},
                            status=status.HTTP_400_BAD_REQUEST)

        # Try to find the user by email or username
        try:
            if email:
                print(f"Looking up user by email: {email}")
                user = User.objects.get(email=email)
                print(f"User found: {user.username} (ID: {user.id})")
            else:
                print(f"Looking up user by username: {username}")
                user = User.objects.get(username=username)
                print(f"User found: {user.username} (ID: {user.id})")
        except User.DoesNotExist:
            print("Login failed: User does not exist")
            return Response({'error': 'Invalid credentials'},
                            status=status.HTTP_400_BAD_REQUEST)

        # Check password
        if not user.check_password(password):
            print("Login failed: Invalid password")
            return Response({'error': 'Invalid credentials'},
                            status=status.HTTP_400_BAD_REQUEST)

        # Check if user is active
        if not user.is_active:
            print(f"Login failed: User {user.username} is not active")
            return Response({'error': 'Account is disabled'},
                            status=status.HTTP_400_BAD_REQUEST)

        # Generate tokens
        refresh = RefreshToken.for_user(user)
        access_token = str(refresh.access_token)

        print(f"Login successful for user: {user.username}")
        print(f"Generated access token: {access_token[:10]}...")

        return Response({
            'refresh': str(refresh),
            'access': access_token,
            'user': UserSerializer(user).data
        }, status=status.HTTP_200_OK)
