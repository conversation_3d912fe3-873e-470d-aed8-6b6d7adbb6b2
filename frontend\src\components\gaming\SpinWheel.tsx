import React, { useState, useEffect, useRef } from 'react';
import { spinWheelService, SpinWheelStatus, SpinResult, SpinWheelReward } from '../../services/spinWheelService';
import ScratchCard from './ScratchCard';

interface SpinWheelProps {
  onRewardWon?: (reward: any) => void;
  onBalanceUpdate?: (newBalance: number) => void;
}

const SpinWheel: React.FC<SpinWheelProps> = ({ onRewardWon, onBalanceUpdate }) => {
  const [status, setStatus] = useState<SpinWheelStatus | null>(null);
  const [isSpinning, setIsSpinning] = useState(false);
  const [spinResult, setSpinResult] = useState<SpinResult | null>(null);
  const [showScratchCard, setShowScratchCard] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  
  const wheelRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadStatus();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (status && !status.can_spin && status.next_spin_time) {
      updateTimeRemaining();
      intervalRef.current = setInterval(updateTimeRemaining, 1000);
    } else if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [status]);

  const loadStatus = async () => {
    try {
      setLoading(true);
      const statusData = await spinWheelService.getStatus();
      setStatus(statusData);
      
      if (!statusData.success) {
        setError(statusData.error || 'Failed to load spin wheel');
      }
    } catch (err) {
      setError('Failed to load spin wheel');
    } finally {
      setLoading(false);
    }
  };

  const updateTimeRemaining = () => {
    if (status?.next_spin_time) {
      const remaining = spinWheelService.formatTimeRemaining(status.next_spin_time);
      setTimeRemaining(remaining);
      
      // Check if cooldown is over
      const { totalMs } = spinWheelService.getTimeUntilNextSpin(status.next_spin_time);
      if (totalMs <= 0) {
        loadStatus(); // Refresh status
      }
    }
  };

  const handleSpin = async () => {
    if (!status?.can_spin || isSpinning) return;

    try {
      setIsSpinning(true);
      setError('');
      
      const result = await spinWheelService.spin();
      setSpinResult(result);
      
      if (result.success && result.wheel_position !== undefined) {
        // Animate the wheel
        animateWheel(result.wheel_position, result.animation_duration || 3000);
        
        // Wait for animation to complete
        setTimeout(() => {
          setIsSpinning(false);
          
          if (result.result?.requires_action && result.result.type === 'scratch_card') {
            setShowScratchCard(true);
          } else {
            // Handle immediate rewards
            handleRewardReceived(result);
          }
          
          // Refresh status
          loadStatus();
        }, result.animation_duration || 3000);
      } else {
        setIsSpinning(false);
        setError(result.error || 'Spin failed');
      }
    } catch (err) {
      setIsSpinning(false);
      setError('Failed to spin wheel');
    }
  };

  const animateWheel = (targetPosition: number, duration: number) => {
    if (!wheelRef.current) return;

    // Add multiple rotations for effect
    const totalRotation = 360 * 5 + targetPosition;
    
    wheelRef.current.style.transition = `transform ${duration}ms cubic-bezier(0.23, 1, 0.32, 1)`;
    wheelRef.current.style.transform = `rotate(${totalRotation}deg)`;
  };

  const handleRewardReceived = (result: SpinResult) => {
    if (result.result && onRewardWon) {
      onRewardWon(result.result);
    }
    
    if (result.result?.new_balance && onBalanceUpdate) {
      onBalanceUpdate(result.result.new_balance);
    }
  };

  const handleScratchCardComplete = (reward: any) => {
    setShowScratchCard(false);
    handleRewardReceived({ result: reward } as SpinResult);
  };

  const renderWheel = () => {
    if (!status?.available_rewards.length) return null;

    const segments = status.wheel_segments;
    const segmentAngle = 360 / segments;
    
    return (
      <div className="relative">
        {/* Wheel Container */}
        <div className="relative w-80 h-80 mx-auto">
          {/* Wheel */}
          <div
            ref={wheelRef}
            className="w-full h-full rounded-full border-8 border-gray-300 shadow-2xl relative overflow-hidden"
            style={{ transition: 'none' }}
          >
            {status.available_rewards.slice(0, segments).map((reward, index) => {
              const rotation = index * segmentAngle;
              const color = spinWheelService.getRewardColor(reward);
              const icon = spinWheelService.getRewardIcon(reward);
              
              return (
                <div
                  key={reward.id}
                  className="absolute w-full h-full"
                  style={{
                    transform: `rotate(${rotation}deg)`,
                    clipPath: `polygon(50% 50%, 50% 0%, ${50 + 50 * Math.sin((segmentAngle * Math.PI) / 180)}% ${50 - 50 * Math.cos((segmentAngle * Math.PI) / 180)}%)`
                  }}
                >
                  <div
                    className="w-full h-full flex items-center justify-center"
                    style={{ backgroundColor: color }}
                  >
                    <div
                      className="text-center text-white font-bold"
                      style={{ transform: `rotate(${segmentAngle / 2}deg)` }}
                    >
                      <div className="text-2xl mb-1">{icon}</div>
                      <div className="text-xs">{reward.name}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          
          {/* Center Circle */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
            <span className="text-2xl">🎡</span>
          </div>
          
          {/* Pointer */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
            <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-500"></div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <div className="text-red-600 mb-4">❌ {error}</div>
        <button
          onClick={loadStatus}
          className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-xl p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
          🎡 Daily Spin Wheel
        </h2>
        <p className="text-gray-600">Spin once per day for amazing rewards!</p>
      </div>

      {/* Current Balance */}
      <div className="text-center mb-6">
        <div className="inline-flex items-center bg-gradient-to-r from-yellow-100 to-orange-100 rounded-full px-6 py-3">
          <span className="text-2xl mr-2">🪙</span>
          <span className="text-xl font-bold text-orange-700">
            {status?.current_balance || 0} Tokens
          </span>
        </div>
      </div>

      {/* Wheel */}
      <div className="mb-8">
        {renderWheel()}
      </div>

      {/* Spin Button or Cooldown */}
      <div className="text-center">
        {status?.can_spin ? (
          <button
            onClick={handleSpin}
            disabled={isSpinning}
            className={`px-8 py-4 rounded-full text-white font-bold text-xl transition-all duration-300 ${
              isSpinning
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 transform hover:scale-105 shadow-lg'
            }`}
          >
            {isSpinning ? (
              <span className="flex items-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
                Spinning...
              </span>
            ) : (
              '🎯 SPIN NOW!'
            )}
          </button>
        ) : (
          <div className="text-center">
            <div className="bg-gradient-to-r from-gray-100 to-gray-200 rounded-full px-8 py-4 mb-4">
              <div className="text-gray-600 font-semibold">⏰ Next spin available in:</div>
              <div className="text-2xl font-bold text-gray-800">{timeRemaining}</div>
            </div>
            <p className="text-sm text-gray-500">Come back tomorrow for another spin!</p>
          </div>
        )}
      </div>

      {/* Spin Result */}
      {spinResult && spinResult.success && !showScratchCard && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg text-center">
          <div className="text-green-600 font-semibold mb-2">🎉 Congratulations!</div>
          <div className="text-lg">{spinResult.result?.description}</div>
        </div>
      )}

      {/* Scratch Card Modal */}
      {showScratchCard && spinResult?.result?.scratch_card_id && (
        <ScratchCard
          spinId={spinResult.spin_id!}
          scratchCardId={spinResult.result.scratch_card_id}
          onComplete={handleScratchCardComplete}
          onClose={() => setShowScratchCard(false)}
        />
      )}

      {/* Today's Spins */}
      {status && (
        <div className="mt-6 text-center text-sm text-gray-500">
          Spins today: {status.total_spins_today} / 1
        </div>
      )}
    </div>
  );
};

export default SpinWheel;
