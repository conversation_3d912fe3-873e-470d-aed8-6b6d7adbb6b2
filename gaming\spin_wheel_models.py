from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import uuid
import json


class SpinWheelReward(models.Model):
    """
    Define available rewards for the spin wheel
    """
    REWARD_TYPES = [
        ('tokens', 'Tokens'),
        ('scratch_card', 'Scratch Card'),
        ('discount', 'Discount'),
        ('bonus_spin', 'Bonus Spin'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, help_text="Display name for the reward")
    reward_type = models.CharField(max_length=20, choices=REWARD_TYPES)
    value = models.PositiveIntegerField(help_text="Token amount, discount percentage, etc.")
    probability = models.FloatField(help_text="Probability of getting this reward (0.0 to 1.0)")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Additional data for complex rewards
    extra_data = models.JSONField(default=dict, blank=True, help_text="Additional reward configuration")
    
    class Meta:
        ordering = ['-probability']
    
    def __str__(self):
        return f"{self.name} ({self.reward_type})"


class SpinWheelHistory(models.Model):
    """
    Track user spin history and enforce cooldowns
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='spin_history')
    reward = models.ForeignKey(SpinWheelReward, on_delete=models.CASCADE)
    
    # Spin details
    spin_timestamp = models.DateTimeField(auto_now_add=True)
    reward_claimed = models.BooleanField(default=False)
    reward_claimed_at = models.DateTimeField(null=True, blank=True)
    
    # For scratch cards
    scratch_card_revealed = models.BooleanField(default=False)
    scratch_card_data = models.JSONField(default=dict, blank=True)
    
    # Tracking
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        ordering = ['-spin_timestamp']
        indexes = [
            models.Index(fields=['user', 'spin_timestamp']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.reward.name} at {self.spin_timestamp}"
    
    @classmethod
    def can_user_spin(cls, user):
        """
        Check if user can spin (once per day limit)
        """
        today = timezone.now().date()
        today_spins = cls.objects.filter(
            user=user,
            spin_timestamp__date=today
        ).count()
        return today_spins == 0
    
    @classmethod
    def get_user_last_spin(cls, user):
        """
        Get user's last spin time
        """
        last_spin = cls.objects.filter(user=user).first()
        return last_spin.spin_timestamp if last_spin else None
    
    @classmethod
    def get_next_spin_time(cls, user):
        """
        Get when user can spin next (next day at midnight)
        """
        last_spin = cls.get_user_last_spin(user)
        if not last_spin:
            return timezone.now()  # Can spin now
        
        # Next spin available at midnight of next day
        next_day = last_spin.date() + timedelta(days=1)
        return timezone.datetime.combine(next_day, timezone.datetime.min.time())


class ScratchCard(models.Model):
    """
    Scratch card rewards with hidden prizes
    """
    SCRATCH_TYPES = [
        ('token_reveal', 'Token Reveal'),
        ('discount_reveal', 'Discount Reveal'),
        ('mystery_box', 'Mystery Box'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    spin_history = models.OneToOneField(SpinWheelHistory, on_delete=models.CASCADE, related_name='scratch_card')
    
    # Scratch card details
    card_type = models.CharField(max_length=20, choices=SCRATCH_TYPES, default='token_reveal')
    hidden_reward = models.JSONField(help_text="The actual reward hidden under the scratch")
    revealed = models.BooleanField(default=False)
    revealed_at = models.DateTimeField(null=True, blank=True)
    
    # Visual configuration
    card_design = models.CharField(max_length=50, default='default')
    scratch_areas = models.JSONField(default=list, help_text="Areas that need to be scratched")
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Scratch Card for {self.spin_history.user.username}"
    
    def reveal_card(self):
        """
        Reveal the scratch card and process rewards
        """
        if self.revealed:
            return False
        
        self.revealed = True
        self.revealed_at = timezone.now()
        self.save()
        
        # Process the hidden reward
        return self._process_hidden_reward()
    
    def _process_hidden_reward(self):
        """
        Process the hidden reward (tokens, discounts, etc.)
        """
        from wallet.models import Wallet, WalletTransaction
        
        reward_data = self.hidden_reward
        user = self.spin_history.user
        
        if reward_data.get('type') == 'tokens':
            # Award tokens
            wallet, _ = Wallet.objects.get_or_create(user=user)
            token_amount = reward_data.get('amount', 0)
            
            wallet.add_tokens(
                amount=token_amount,
                transaction_type='spin_wheel_scratch',
                description=f"Scratch card reward: {token_amount} tokens"
            )
            
            return {
                'success': True,
                'reward_type': 'tokens',
                'amount': token_amount,
                'message': f"You won {token_amount} tokens!"
            }
        
        elif reward_data.get('type') == 'discount':
            # Create discount code or add to user profile
            discount_percent = reward_data.get('amount', 0)
            return {
                'success': True,
                'reward_type': 'discount',
                'amount': discount_percent,
                'message': f"You won {discount_percent}% discount on your next purchase!"
            }
        
        return {
            'success': False,
            'message': 'Unknown reward type'
        }


class SpinWheelSettings(models.Model):
    """
    Global settings for the spin wheel
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Cooldown settings
    cooldown_hours = models.PositiveIntegerField(default=24, help_text="Hours between spins")
    
    # Wheel configuration
    wheel_segments = models.PositiveIntegerField(default=8, help_text="Number of wheel segments")
    animation_duration = models.PositiveIntegerField(default=3000, help_text="Spin animation duration in ms")
    
    # Reward settings
    min_token_reward = models.PositiveIntegerField(default=1)
    max_token_reward = models.PositiveIntegerField(default=50)
    scratch_card_probability = models.FloatField(default=0.2, help_text="Probability of getting scratch card")
    
    # Feature flags
    is_active = models.BooleanField(default=True)
    maintenance_mode = models.BooleanField(default=False)
    maintenance_message = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Spin Wheel Settings"
        verbose_name_plural = "Spin Wheel Settings"
    
    def __str__(self):
        return f"Spin Wheel Settings (Updated: {self.updated_at})"
    
    @classmethod
    def get_settings(cls):
        """
        Get current spin wheel settings (singleton pattern)
        """
        settings, created = cls.objects.get_or_create(
            id='00000000-0000-0000-0000-000000000001',
            defaults={
                'cooldown_hours': 24,
                'wheel_segments': 8,
                'animation_duration': 3000,
                'min_token_reward': 1,
                'max_token_reward': 50,
                'scratch_card_probability': 0.2,
                'is_active': True,
            }
        )
        return settings
