"""
Image synchronization service for syncing product images from Printify API
"""
import logging
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from .models import Product, ProductImage
from printify.api_client import PrintifyAPIClient

logger = logging.getLogger(__name__)


class ImageSyncService:
    """Service for synchronizing product images from Printify API"""
    
    def __init__(self):
        self.client = PrintifyAPIClient()
        self.shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', '22150635')
    
    def sync_product_images(self, product):
        """
        Sync images for a single product from Printify API
        
        Args:
            product: Product instance to sync images for
            
        Returns:
            dict: Result containing success status, counts, and messages
        """
        result = {
            'success': False,
            'product_id': str(product.id),
            'product_name': product.name,
            'images_added': 0,
            'images_updated': 0,
            'images_removed': 0,
            'errors': [],
            'messages': []
        }
        
        try:
            # Check if product has Printify ID
            if not product.printify_id:
                result['errors'].append(f"Product '{product.name}' has no Printify ID")
                return result
            
            logger.info(f"Starting image sync for product: {product.name} (Printify ID: {product.printify_id})")
            
            # Fetch product data from Printify API
            printify_product = self.client.get_product(self.shop_id, product.printify_id)
            
            if not printify_product:
                result['errors'].append(f"Could not fetch product data from Printify API")
                return result
            
            # Extract images from Printify response
            printify_images = printify_product.get('images', [])
            
            if not printify_images:
                result['messages'].append(f"No images found in Printify for product '{product.name}'")
                result['success'] = True
                return result
            
            # Get existing images for this product
            existing_images = list(ProductImage.objects.filter(product=product))
            existing_urls = {img.image_url for img in existing_images if img.image_url}
            
            # Track which images we've processed
            processed_urls = set()
            
            # Process each image from Printify
            for i, image_data in enumerate(printify_images):
                image_url = image_data.get('src')
                if not image_url:
                    continue
                
                processed_urls.add(image_url)
                
                # Check if image already exists
                existing_image = next(
                    (img for img in existing_images if img.image_url == image_url), 
                    None
                )
                
                if existing_image:
                    # Image exists, check if we need to update anything
                    updated = False
                    
                    # Update is_primary if this is the first image
                    if i == 0 and not existing_image.is_primary:
                        existing_image.is_primary = True
                        updated = True
                    elif i > 0 and existing_image.is_primary:
                        existing_image.is_primary = False
                        updated = True
                    
                    # Update alt_text if it's empty or generic
                    new_alt_text = f"{product.name} - Image {i+1}"
                    if not existing_image.alt_text or existing_image.alt_text.startswith(product.name):
                        if existing_image.alt_text != new_alt_text:
                            existing_image.alt_text = new_alt_text
                            updated = True
                    
                    if updated:
                        existing_image.save()
                        result['images_updated'] += 1
                        logger.info(f"Updated existing image {i+1} for product {product.name}")
                
                else:
                    # Create new image
                    try:
                        # Ensure alt_text is not too long
                        alt_text = f"{product.name} - Image {i+1}"
                        if len(alt_text) > 100:
                            alt_text = alt_text[:97] + "..."
                        
                        ProductImage.objects.create(
                            product=product,
                            image=None,  # Don't download the image file
                            image_url=image_url,
                            alt_text=alt_text,
                            is_primary=(i == 0)  # First image is primary
                        )
                        result['images_added'] += 1
                        logger.info(f"Added new image {i+1} for product {product.name}: {image_url}")
                        
                    except Exception as e:
                        error_msg = f"Error creating image {i+1}: {str(e)}"
                        result['errors'].append(error_msg)
                        logger.error(f"Error creating image for product {product.name}: {str(e)}")
            
            # Remove images that are no longer in Printify (optional - can be disabled)
            # This is commented out to preserve custom images that might have been added manually
            # for existing_image in existing_images:
            #     if existing_image.image_url and existing_image.image_url not in processed_urls:
            #         existing_image.delete()
            #         result['images_removed'] += 1
            #         logger.info(f"Removed obsolete image for product {product.name}: {existing_image.image_url}")
            
            # Update the last sync timestamp
            product.last_image_sync = timezone.now()
            product.save(update_fields=['last_image_sync'])
            
            result['success'] = True
            result['messages'].append(
                f"Successfully synced images for '{product.name}': "
                f"{result['images_added']} added, {result['images_updated']} updated"
            )
            
            logger.info(f"Completed image sync for product: {product.name}")
            
        except Exception as e:
            error_msg = f"Unexpected error syncing images for product '{product.name}': {str(e)}"
            result['errors'].append(error_msg)
            logger.error(error_msg, exc_info=True)
        
        return result
    
    def sync_multiple_products(self, products):
        """
        Sync images for multiple products
        
        Args:
            products: QuerySet or list of Product instances
            
        Returns:
            dict: Summary of sync results
        """
        summary = {
            'total_products': len(products),
            'successful_syncs': 0,
            'failed_syncs': 0,
            'total_images_added': 0,
            'total_images_updated': 0,
            'total_images_removed': 0,
            'results': [],
            'errors': []
        }
        
        for product in products:
            result = self.sync_product_images(product)
            summary['results'].append(result)
            
            if result['success']:
                summary['successful_syncs'] += 1
                summary['total_images_added'] += result['images_added']
                summary['total_images_updated'] += result['images_updated']
                summary['total_images_removed'] += result['images_removed']
            else:
                summary['failed_syncs'] += 1
                summary['errors'].extend(result['errors'])
        
        return summary
