"""
Management command to fix cart items with invalid variant IDs
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from orders.models import CartItem
from products.models import ProductVariant


class Command(BaseCommand):
    help = 'Fix cart items with invalid variant IDs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be fixed without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        self.stdout.write('🔍 Checking for cart items with invalid variant IDs...')
        
        # Get all cart items with variant_id
        cart_items_with_variants = CartItem.objects.filter(
            variant_id__isnull=False
        ).exclude(variant_id='')
        
        total_items = cart_items_with_variants.count()
        self.stdout.write(f'Found {total_items} cart items with variant IDs')
        
        invalid_items = []
        fixed_items = []
        
        for cart_item in cart_items_with_variants:
            # Check if variant exists
            variant_exists = ProductVariant.objects.filter(
                product=cart_item.product,
                variant_id=cart_item.variant_id
            ).exists()
            
            if not variant_exists:
                invalid_items.append(cart_item)
                
                # Try to find a valid variant for this product
                valid_variant = ProductVariant.objects.filter(
                    product=cart_item.product
                ).first()
                
                if valid_variant:
                    self.stdout.write(
                        f'❌ Invalid variant ID {cart_item.variant_id} for product {cart_item.product.name}'
                    )
                    self.stdout.write(
                        f'✅ Will fix with variant ID {valid_variant.variant_id} ({valid_variant.title})'
                    )
                    
                    if not dry_run:
                        with transaction.atomic():
                            cart_item.variant_id = valid_variant.variant_id
                            cart_item.save()
                        fixed_items.append(cart_item)
                else:
                    self.stdout.write(
                        f'❌ No valid variants found for product {cart_item.product.name}'
                    )
                    if not dry_run:
                        # Remove variant_id if no valid variants exist
                        cart_item.variant_id = None
                        cart_item.save()
        
        # Summary
        self.stdout.write('\n' + '='*60)
        self.stdout.write(f'📊 SUMMARY:')
        self.stdout.write(f'   Total cart items checked: {total_items}')
        self.stdout.write(f'   Invalid variant IDs found: {len(invalid_items)}')
        
        if not dry_run:
            self.stdout.write(f'   Items fixed: {len(fixed_items)}')
            self.stdout.write(
                self.style.SUCCESS(f'✅ Successfully fixed {len(fixed_items)} cart items')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠️ Run without --dry-run to apply fixes')
            )
        
        # Test a fixed item
        if fixed_items and not dry_run:
            test_item = fixed_items[0]
            variant_details = test_item.get_variant_details()
            if variant_details:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Test: Fixed item now has variant details: {variant_details["title"]}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'❌ Test: Fixed item still has no variant details')
                )
