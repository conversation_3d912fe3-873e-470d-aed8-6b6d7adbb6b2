#!/usr/bin/env python
"""
Quick Spin Wheel setup for production
Copy and paste this entire script into your production console
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import SpinWheelReward, SpinWheelSettings

print("🎡 Quick Spin Wheel Setup")
print("=" * 30)

# Create settings
if SpinWheelSettings.objects.count() == 0:
    settings = SpinWheelSettings.objects.create(
        cooldown_hours=24,
        wheel_segments=8,
        animation_duration=3000,
        min_token_reward=1,
        max_token_reward=50,
        scratch_card_probability=0.2,
        is_active=True,
        maintenance_mode=False,
    )
    print("✅ Settings created")
else:
    print("✅ Settings already exist")

# Create rewards
if SpinWheelReward.objects.filter(is_active=True).count() == 0:
    rewards = [
        {'name': '5 Tokens', 'reward_type': 'tokens', 'value': 5, 'probability': 0.25, 'extra_data': {'color': '#FFD700', 'icon': '🪙'}},
        {'name': '10 Tokens', 'reward_type': 'tokens', 'value': 10, 'probability': 0.20, 'extra_data': {'color': '#FF6B6B', 'icon': '💰'}},
        {'name': '15 Tokens', 'reward_type': 'tokens', 'value': 15, 'probability': 0.15, 'extra_data': {'color': '#4ECDC4', 'icon': '💎'}},
        {'name': '25 Tokens', 'reward_type': 'tokens', 'value': 25, 'probability': 0.10, 'extra_data': {'color': '#45B7D1', 'icon': '🎁'}},
        {'name': 'Scratch Card', 'reward_type': 'scratch_card', 'value': 1, 'probability': 0.15, 'extra_data': {'color': '#96CEB4', 'icon': '🎫'}},
        {'name': '5% Discount', 'reward_type': 'discount', 'value': 5, 'probability': 0.08, 'extra_data': {'color': '#FFEAA7', 'icon': '🏷️'}},
        {'name': '10% Discount', 'reward_type': 'discount', 'value': 10, 'probability': 0.05, 'extra_data': {'color': '#DDA0DD', 'icon': '🎟️'}},
        {'name': '2 Tokens', 'reward_type': 'tokens', 'value': 2, 'probability': 0.02, 'extra_data': {'color': '#F0F0F0', 'icon': '🥉'}},
    ]
    
    for reward_data in rewards:
        SpinWheelReward.objects.create(
            name=reward_data['name'],
            reward_type=reward_data['reward_type'],
            value=reward_data['value'],
            probability=reward_data['probability'],
            extra_data=reward_data['extra_data'],
            is_active=True
        )
    
    print(f"✅ Created {len(rewards)} rewards")
else:
    print("✅ Rewards already exist")

# Verify
settings_count = SpinWheelSettings.objects.count()
rewards_count = SpinWheelReward.objects.filter(is_active=True).count()

print(f"\n📊 Final Status:")
print(f"Settings: {settings_count}")
print(f"Active Rewards: {rewards_count}")

if settings_count > 0 and rewards_count > 0:
    print("\n🎉 SUCCESS! Spin Wheel is ready!")
else:
    print("\n❌ Setup incomplete")

print("\n🔗 API Endpoints:")
print("GET  /api/gaming/spin-wheel/status/")
print("POST /api/gaming/spin-wheel/spin/")
print("POST /api/gaming/spin-wheel/reveal-scratch/")
print("GET  /api/gaming/spin-wheel/history/")
