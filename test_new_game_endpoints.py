#!/usr/bin/env python
"""
Test New Game Endpoints
=======================

Test the new dedicated endpoints for Color Match and Memory Card games
"""

import os
import django
import sys

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from wallet.models import Wallet
from rest_framework_simplejwt.tokens import RefreshToken
import json


def test_new_game_endpoints():
    """Test the new dedicated game endpoints"""
    print("🧪 TESTING NEW GAME ENDPOINTS")
    print("=" * 40)
    
    # Get or create test user
    test_user, created = User.objects.get_or_create(
        username='test_games_user',
        defaults={'email': '<EMAIL>'}
    )
    
    if created:
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using test user: {test_user.username}")
    
    # Ensure user has wallet with tokens
    wallet, created = Wallet.objects.get_or_create(user=test_user)
    if wallet.balance < 10:
        wallet.add_tokens(50, 'test', 'Test tokens for new games')
        print(f"✅ Added tokens, balance: {wallet.balance}")
    
    # Generate JWT token
    refresh = RefreshToken.for_user(test_user)
    access_token = str(refresh.access_token)
    
    client = Client()
    
    print(f"\n🎮 Testing Color Match Endpoints:")
    print("-" * 35)
    
    # Test Color Match start
    try:
        response = client.post(
            '/api/gaming/color-match/start/',
            data=json.dumps({'difficulty': 'medium'}),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'JWT {access_token}'
        )
        
        print(f"Start Color Match: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS! Game ID: {data.get('game_id')}")
            print(f"   Balance: {data.get('balance')} tokens")
            
            # Test Color Match complete
            game_id = data.get('game_id')
            if game_id:
                complete_response = client.post(
                    '/api/gaming/color-match/complete/',
                    data=json.dumps({
                        'game_id': game_id,
                        'result': 'win',
                        'difficulty': 'medium'
                    }),
                    content_type='application/json',
                    HTTP_AUTHORIZATION=f'JWT {access_token}'
                )
                
                print(f"Complete Color Match: {complete_response.status_code}")
                if complete_response.status_code == 200:
                    complete_data = complete_response.json()
                    print(f"   ✅ Game completed! Tokens earned: {complete_data.get('tokens_earned')}")
                    print(f"   New balance: {complete_data.get('new_balance')}")
                else:
                    print(f"   ❌ Complete failed: {complete_response.content.decode()}")
        else:
            print(f"   ❌ Start failed: {response.content.decode()}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test Color Match stats
    try:
        stats_response = client.get(
            '/api/gaming/color-match/stats/',
            HTTP_AUTHORIZATION=f'JWT {access_token}'
        )
        
        print(f"Color Match Stats: {stats_response.status_code}")
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print(f"   ✅ Stats retrieved! Total games: {stats_data.get('stats', {}).get('total_games', 0)}")
        else:
            print(f"   ❌ Stats failed: {stats_response.content.decode()}")
            
    except Exception as e:
        print(f"   ❌ Stats exception: {e}")
    
    print(f"\n🃏 Testing Memory Card Endpoints:")
    print("-" * 35)
    
    # Test Memory Card start
    try:
        response = client.post(
            '/api/gaming/memory-card/start/',
            data=json.dumps({'difficulty': 'medium'}),
            content_type='application/json',
            HTTP_AUTHORIZATION=f'JWT {access_token}'
        )
        
        print(f"Start Memory Card: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ SUCCESS! Game ID: {data.get('game_id')}")
            print(f"   Balance: {data.get('balance')} tokens")
            
            # Test Memory Card complete
            game_id = data.get('game_id')
            if game_id:
                complete_response = client.post(
                    '/api/gaming/memory-card/complete/',
                    data=json.dumps({
                        'game_id': game_id,
                        'result': 'win',
                        'difficulty': 'medium'
                    }),
                    content_type='application/json',
                    HTTP_AUTHORIZATION=f'JWT {access_token}'
                )
                
                print(f"Complete Memory Card: {complete_response.status_code}")
                if complete_response.status_code == 200:
                    complete_data = complete_response.json()
                    print(f"   ✅ Game completed! Tokens earned: {complete_data.get('tokens_earned')}")
                    print(f"   New balance: {complete_data.get('new_balance')}")
                else:
                    print(f"   ❌ Complete failed: {complete_response.content.decode()}")
        else:
            print(f"   ❌ Start failed: {response.content.decode()}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test Memory Card stats
    try:
        stats_response = client.get(
            '/api/gaming/memory-card/stats/',
            HTTP_AUTHORIZATION=f'JWT {access_token}'
        )
        
        print(f"Memory Card Stats: {stats_response.status_code}")
        if stats_response.status_code == 200:
            stats_data = stats_response.json()
            print(f"   ✅ Stats retrieved! Total games: {stats_data.get('stats', {}).get('total_games', 0)}")
        else:
            print(f"   ❌ Stats failed: {stats_response.content.decode()}")
            
    except Exception as e:
        print(f"   ❌ Stats exception: {e}")
    
    print(f"\n🎉 NEW GAME ENDPOINTS TEST COMPLETE")
    print("=" * 45)
    
    # Final wallet check
    wallet.refresh_from_db()
    print(f"Final wallet balance: {wallet.balance} tokens")
    
    print(f"\n💡 Next Steps:")
    print("1. Deploy these changes to production")
    print("2. Test the new endpoints on frontend")
    print("3. Create actual game components for Color Match and Memory Card")
    print("4. The backend API is ready and working!")


if __name__ == '__main__':
    test_new_game_endpoints()
