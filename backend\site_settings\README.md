# Site Settings App

This Django app allows you to manage site-wide settings like the logo and favicon through the Django admin panel.

## Features

- Upload and manage site logo and favicon
- Automatically resize and validate images
- Context processor to inject settings into all templates
- Admin interface with image previews

## Installation

1. Add `'site_settings'` to your `INSTALLED_APPS` in `settings.py`:

```python
INSTALLED_APPS = [
    # ...
    'site_settings',
    # ...
]
```

2. Add the context processor to your `TEMPLATES` setting:

```python
TEMPLATES = [
    {
        # ...
        'OPTIONS': {
            'context_processors': [
                # ...
                'site_settings.context_processors.site_settings',
            ],
        },
    },
]
```

3. Make sure your `MEDIA_URL` and `MEDIA_ROOT` are configured:

```python
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

4. Configure your URLs to serve media files in development:

```python
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # Your URL patterns...
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

5. Run migrations:

```bash
python manage.py migrate site_settings
```

## Usage

### Admin Interface

1. Go to the Django admin panel (`/admin/`)
2. Navigate to "Site Settings"
3. Create a new Site Settings instance (only one is allowed)
4. Upload your logo and favicon
5. Save the settings

### In Templates

The site settings are automatically available in all templates through the context processor:

```html
<!-- Display the logo -->
{% if site_settings and site_settings.site_logo %}
    <img src="{{ site_settings.site_logo.url }}" alt="Site Logo">
{% endif %}

<!-- Add the favicon -->
{% if site_settings and site_settings.favicon %}
    <link rel="icon" href="{{ site_settings.favicon.url }}" type="image/x-icon">
{% endif %}
```

## Testing

You can test the site settings by visiting `/site-settings/test-template/` after setting up the app.

## Image Requirements

- Maximum file size: 2MB
- Maximum dimensions: 1200x1200 pixels
- Supported favicon formats: ico, png, jpg, jpeg, gif

## Notes

- Only one instance of SiteSettings is allowed
- Images are stored in `site/logo/` and `site/favicon/` directories within your media root
- The app automatically handles file naming and organization
