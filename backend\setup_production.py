#!/usr/bin/env python
"""
Production setup script for Spin Wheel
Run this after deployment to ensure database is properly configured
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import SpinWheelReward, SpinWheelSettings

def setup_spin_wheel_production():
    """
    Set up spin wheel for production environment
    """
    print("🎡 Setting up Spin Wheel for production...")
    
    try:
        # Check if settings exist
        settings_count = SpinWheelSettings.objects.count()
        print(f"Current settings count: {settings_count}")
        
        if settings_count == 0:
            print("Creating default settings...")
            settings = SpinWheelSettings.objects.create(
                cooldown_hours=24,
                wheel_segments=8,
                animation_duration=3000,
                min_token_reward=1,
                max_token_reward=50,
                scratch_card_probability=0.2,
                is_active=True,
                maintenance_mode=False,
            )
            print("✅ Settings created")
        else:
            settings = SpinWheelSettings.objects.first()
            print("✅ Settings already exist")
        
        # Check if rewards exist
        rewards_count = SpinWheelReward.objects.count()
        print(f"Current rewards count: {rewards_count}")
        
        if rewards_count == 0:
            print("Creating default rewards...")
            
            default_rewards = [
                {
                    'name': '5 Tokens',
                    'reward_type': 'tokens',
                    'value': 5,
                    'probability': 0.25,
                    'extra_data': {'color': '#FFD700', 'icon': '🪙'}
                },
                {
                    'name': '10 Tokens',
                    'reward_type': 'tokens',
                    'value': 10,
                    'probability': 0.20,
                    'extra_data': {'color': '#FF6B6B', 'icon': '💰'}
                },
                {
                    'name': '15 Tokens',
                    'reward_type': 'tokens',
                    'value': 15,
                    'probability': 0.15,
                    'extra_data': {'color': '#4ECDC4', 'icon': '💎'}
                },
                {
                    'name': '25 Tokens',
                    'reward_type': 'tokens',
                    'value': 25,
                    'probability': 0.10,
                    'extra_data': {'color': '#45B7D1', 'icon': '🎁'}
                },
                {
                    'name': 'Scratch Card',
                    'reward_type': 'scratch_card',
                    'value': 1,
                    'probability': 0.15,
                    'extra_data': {'color': '#96CEB4', 'icon': '🎫'}
                },
                {
                    'name': '5% Discount',
                    'reward_type': 'discount',
                    'value': 5,
                    'probability': 0.08,
                    'extra_data': {'color': '#FFEAA7', 'icon': '🏷️'}
                },
                {
                    'name': '10% Discount',
                    'reward_type': 'discount',
                    'value': 10,
                    'probability': 0.05,
                    'extra_data': {'color': '#DDA0DD', 'icon': '🎟️'}
                },
                {
                    'name': '2 Tokens',
                    'reward_type': 'tokens',
                    'value': 2,
                    'probability': 0.02,
                    'extra_data': {'color': '#F0F0F0', 'icon': '🥉'}
                }
            ]
            
            for reward_data in default_rewards:
                reward = SpinWheelReward.objects.create(
                    name=reward_data['name'],
                    reward_type=reward_data['reward_type'],
                    value=reward_data['value'],
                    probability=reward_data['probability'],
                    extra_data=reward_data['extra_data'],
                    is_active=True
                )
                print(f"✅ Created reward: {reward.name}")
            
            print(f"✅ Created {len(default_rewards)} rewards")
        else:
            print("✅ Rewards already exist")
        
        # Verify setup
        total_probability = sum(
            reward.probability for reward in SpinWheelReward.objects.filter(is_active=True)
        )
        
        print(f"\n📊 Setup Summary:")
        print(f"  • Settings: {SpinWheelSettings.objects.count()}")
        print(f"  • Rewards: {SpinWheelReward.objects.count()}")
        print(f"  • Total probability: {total_probability:.2f}")
        print(f"  • Wheel active: {settings.is_active}")
        
        if abs(total_probability - 1.0) > 0.01:
            print(f"⚠️ Warning: Total probability should be close to 1.0")
        
        print("\n🎉 Spin Wheel production setup complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error during setup: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = setup_spin_wheel_production()
    sys.exit(0 if success else 1)
