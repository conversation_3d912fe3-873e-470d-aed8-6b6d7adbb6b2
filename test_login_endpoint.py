#!/usr/bin/env python
"""
Test Login Endpoint
===================

This script tests the custom login endpoint directly to see what's wrong.
"""

import os
import django
import json
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User


def test_custom_login_endpoint_internal():
    """Test custom login endpoint using Django test client"""
    print("🧪 Testing Custom Login Endpoint (Internal)")
    print("=" * 50)
    
    client = Client()
    
    # Get a user from database
    users = User.objects.filter(is_active=True)[:3]
    
    if not users:
        print("❌ No active users found in database")
        return
    
    for user in users:
        print(f"\n👤 Testing with user: {user.username}")
        print(f"   Email: {user.email}")
        print(f"   Active: {user.is_active}")
        
        # Test common passwords
        test_passwords = ["password123", "Password123", "admin123", "test123", "TestPass123!"]
        
        for password in test_passwords:
            print(f"\n🔐 Testing password: {password}")
            
            # Test with username
            login_data = {
                'username': user.username,
                'password': password
            }
            
            try:
                response = client.post('/api/auth/login/',
                                     data=json.dumps(login_data),
                                     content_type='application/json')
                
                print(f"   Username login - Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS! Username: {user.username}, Password: {password}")
                    response_data = json.loads(response.content.decode())
                    print(f"   Response keys: {list(response_data.keys())}")
                    return user.username, password
                else:
                    print(f"   ❌ Failed: {response.content.decode()}")
                
                # Test with email
                login_data = {
                    'email': user.email,
                    'password': password
                }
                
                response = client.post('/api/auth/login/',
                                     data=json.dumps(login_data),
                                     content_type='application/json')
                
                print(f"   Email login - Status: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS! Email: {user.email}, Password: {password}")
                    response_data = json.loads(response.content.decode())
                    print(f"   Response keys: {list(response_data.keys())}")
                    return user.email, password
                else:
                    print(f"   ❌ Failed: {response.content.decode()}")
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
    
    print("\n❌ No working credentials found")
    return None, None


def test_custom_login_endpoint_external(base_url):
    """Test custom login endpoint using external HTTP request"""
    print(f"\n🌐 Testing Custom Login Endpoint (External)")
    print(f"Base URL: {base_url}")
    print("=" * 50)
    
    # Get a user from database
    users = User.objects.filter(is_active=True)[:2]
    
    if not users:
        print("❌ No active users found in database")
        return
    
    for user in users:
        print(f"\n👤 Testing with user: {user.username}")
        
        # Test common passwords
        test_passwords = ["password123", "Password123", "admin123", "test123", "TestPass123!"]
        
        for password in test_passwords:
            print(f"\n🔐 Testing password: {password}")
            
            # Test with username
            login_data = {
                'username': user.username,
                'password': password
            }
            
            try:
                url = f"{base_url}/api/auth/login/"
                print(f"   Calling: {url}")
                
                response = requests.post(url, json=login_data, timeout=10)
                
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
                if response.status_code == 200:
                    print(f"   ✅ SUCCESS! Username: {user.username}, Password: {password}")
                    return user.username, password
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
    
    print("\n❌ No working credentials found via external request")
    return None, None


def create_test_user_and_login():
    """Create a test user and immediately test login"""
    print("\n👤 Creating Test User and Testing Login")
    print("=" * 50)
    
    test_username = "test_login_user"
    test_email = "<EMAIL>"
    test_password = "TestPass123!"
    
    # Clean up existing test user
    User.objects.filter(username=test_username).delete()
    User.objects.filter(email=test_email).delete()
    
    try:
        # Create user
        user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password=test_password
        )
        user.is_active = True
        user.save()
        
        print(f"✅ Created test user: {user.username}")
        
        # Test login immediately
        client = Client()
        
        login_data = {
            'username': test_username,
            'password': test_password
        }
        
        response = client.post('/api/auth/login/',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        print(f"Login test - Status: {response.status_code}")
        print(f"Login test - Response: {response.content.decode()}")
        
        if response.status_code == 200:
            print(f"✅ SUCCESS! Test user login works!")
            print(f"   Username: {test_username}")
            print(f"   Password: {test_password}")
            
            # Clean up
            user.delete()
            return test_username, test_password
        else:
            print(f"❌ Test user login failed")
            user.delete()
            return None, None
            
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        return None, None


def main():
    """Main test function"""
    print("🔧 Login Endpoint Test")
    print("=" * 50)
    
    # Test 1: Internal Django test
    working_username, working_password = test_custom_login_endpoint_internal()
    
    if working_username:
        print(f"\n🎉 FOUND WORKING CREDENTIALS!")
        print(f"Username: {working_username}")
        print(f"Password: {working_password}")
        print(f"\nTry logging in with these credentials on your frontend!")
    else:
        # Test 2: Create test user
        print(f"\n⚠️ No existing users work. Creating test user...")
        working_username, working_password = create_test_user_and_login()
        
        if working_username:
            print(f"\n🎉 TEST USER WORKS!")
            print(f"Username: {working_username}")
            print(f"Password: {working_password}")
        else:
            print(f"\n❌ Even test user doesn't work. There's an issue with the login endpoint.")
    
    # Test 3: External test (if you provide base URL)
    print(f"\n💡 To test external access, run:")
    print(f"python test_login_endpoint.py https://your-backend.onrender.com")
    
    print(f"\n📋 Summary")
    print("=" * 40)
    print(f"Total users in database: {User.objects.count()}")
    print(f"Active users: {User.objects.filter(is_active=True).count()}")
    
    if working_username:
        print(f"✅ Login endpoint works with: {working_username} / {working_password}")
        print(f"\n🎯 Next steps:")
        print(f"1. Try these credentials on your frontend")
        print(f"2. Check browser console for which endpoint is being called")
        print(f"3. Make sure frontend API base URL is correct")
    else:
        print(f"❌ Login endpoint has issues")
        print(f"\n🎯 Next steps:")
        print(f"1. Check Django logs for errors")
        print(f"2. Verify login view is working correctly")
        print(f"3. Check URL routing")


if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
        test_custom_login_endpoint_external(base_url)
    else:
        main()
