from django.core.management.base import BaseCommand
from django.db import connection
from django.core.management import call_command

class Command(BaseCommand):
    help = 'Fix database schema issues for ProductImage model'

    def handle(self, *args, **options):
        self.stdout.write('=== FIXING DATABASE SCHEMA ===')

        try:
            # First, run migrations to ensure we're up to date
            self.stdout.write('Running migrations...')
            call_command('migrate', verbosity=1)
            self.stdout.write('✅ Migrations completed')

            # Now test the ProductImage model
            from products.models import ProductImage

            # Test basic model access
            try:
                count = ProductImage.objects.count()
                self.stdout.write(f'✅ ProductImage model is working. Found {count} images.')
            except Exception as e:
                self.stdout.write(f'❌ ProductImage model issue: {str(e)}')

            # Test image_url field specifically
            try:
                test_images = ProductImage.objects.filter(image_url__isnull=False)[:1]
                list(test_images)  # Force evaluation
                self.stdout.write('✅ image_url field is accessible')
            except Exception as e:
                self.stdout.write(f'❌ image_url field issue: {str(e)}')

                # Try to add the column if it's missing
                with connection.cursor() as cursor:
                    try:
                        cursor.execute("ALTER TABLE products_productimage ADD COLUMN IF NOT EXISTS image_url VARCHAR(200);")
                        self.stdout.write('✅ Added missing image_url column')
                    except Exception as add_error:
                        self.stdout.write(f'Column add failed: {str(add_error)}')

        except Exception as e:
            self.stdout.write(f'❌ Critical error: {str(e)}')
            import traceback
            self.stdout.write(traceback.format_exc())

        self.stdout.write('\n✅ Database schema check completed!')
