#!/usr/bin/env python3
"""
Test Upstash Redis Connection
Quick script to verify Redis connectivity with TLS
"""

import os
import redis

def test_upstash_redis():
    """Test connection to Upstash Redis"""
    print("🔴 Testing Upstash Redis Connection...")
    
    # Your Upstash Redis URL
    redis_url = "redis://default:<EMAIL>:6379"
    
    try:
        # Create Redis connection with TLS support
        r = redis.Redis.from_url(
            redis_url,
            ssl_cert_reqs=None,  # Don't verify SSL certificates
            decode_responses=True
        )
        
        # Test connection
        print("   🔄 Attempting to connect...")
        response = r.ping()
        print(f"   ✅ Ping successful: {response}")
        
        # Test basic operations
        print("   🔄 Testing basic operations...")
        
        # Set a test key
        r.set('test_gaming_key', 'PickMeTrend Gaming System', ex=60)
        print("   ✅ SET operation successful")
        
        # Get the test key
        value = r.get('test_gaming_key')
        print(f"   ✅ GET operation successful: {value}")
        
        # Test list operations (for gaming queues)
        r.lpush('test_game_queue', 'player1', 'player2')
        queue_length = r.llen('test_game_queue')
        print(f"   ✅ LIST operations successful: queue length = {queue_length}")
        
        # Clean up test data
        r.delete('test_gaming_key', 'test_game_queue')
        print("   ✅ Cleanup successful")
        
        print("\n🎉 Upstash Redis is ready for production!")
        print("   ✅ Connection established")
        print("   ✅ Basic operations working")
        print("   ✅ Gaming system compatible")
        
        return True
        
    except redis.ConnectionError as e:
        print(f"   ❌ Connection failed: {e}")
        print("   💡 Check your Redis URL and network connectivity")
        return False
        
    except redis.AuthenticationError as e:
        print(f"   ❌ Authentication failed: {e}")
        print("   💡 Check your Redis password/credentials")
        return False
        
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        print(f"   💡 Error type: {type(e).__name__}")
        return False

def test_django_channels_compatibility():
    """Test if Redis works with Django Channels"""
    print("\n📡 Testing Django Channels Compatibility...")
    
    redis_url = "redis://default:<EMAIL>:6379"
    
    try:
        # Test channel-like operations
        r = redis.Redis.from_url(
            redis_url,
            ssl_cert_reqs=None,
            decode_responses=True
        )
        
        # Test pub/sub (used by Django Channels)
        pubsub = r.pubsub()
        pubsub.subscribe('test_gaming_channel')
        print("   ✅ Pub/Sub subscription successful")
        
        # Test channel group operations
        r.sadd('test_game_room_players', 'player1', 'player2')
        players = r.smembers('test_game_room_players')
        print(f"   ✅ Set operations successful: {len(players)} players")
        
        # Clean up
        pubsub.close()
        r.delete('test_game_room_players')
        print("   ✅ Django Channels compatible operations working")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Channels compatibility test failed: {e}")
        return False

def main():
    """Run all Redis tests"""
    print("🚀 PickMeTrend Redis Production Test")
    print("=" * 50)
    
    # Test basic Redis connection
    basic_test = test_upstash_redis()
    
    # Test Django Channels compatibility
    channels_test = test_django_channels_compatibility()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Basic Redis: {'✅ PASS' if basic_test else '❌ FAIL'}")
    print(f"   Channels Compatible: {'✅ PASS' if channels_test else '❌ FAIL'}")
    
    if basic_test and channels_test:
        print("\n🎉 All tests passed! Redis is ready for production deployment!")
        print("\n🎮 Your gaming system will have:")
        print("   ✅ Real-time WebSocket support")
        print("   ✅ Gaming session management")
        print("   ✅ Player matchmaking queues")
        print("   ✅ Token transaction caching")
        
        print("\n🚀 Ready to deploy to Render!")
    else:
        print("\n⚠️ Some tests failed. Please check the Redis configuration.")
    
    return basic_test and channels_test

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
