from django import forms
from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import redirect, get_object_or_404
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import reverse
from .models import Category, Product, ProductImage, Review, ProductVariant
from printify.api_client import PrintifyAPIClient
from .utils import clean_description, clean_description_with_ai
from .image_sync_service import ImageSyncService


class ProductImageForm(forms.ModelForm):
    image_url = forms.URLField(
        required=False,
        help_text="Enter a URL to download an image instead of uploading one"
    )

    class Meta:
        model = ProductImage
        fields = ['image', 'image_url', 'alt_text', 'is_primary']

    def clean(self):
        cleaned_data = super().clean()
        image = cleaned_data.get('image')
        image_url = cleaned_data.get('image_url')

        # If URL is provided but no image, use the URL as the image
        if image_url:
            # Store the URL in image_url field
            cleaned_data['image_url'] = image_url

            # If this is a Printify URL, don't try to download it
            if 'printify' in image_url.lower() or 'print-provider' in image_url.lower():
                # Set image to None to avoid download attempts
                cleaned_data['image'] = None
            # Otherwise, only set image to URL if it's a new record and no image is provided
            elif not image and not self.instance.pk:
                cleaned_data['image'] = image_url

        return cleaned_data

    def is_valid(self):
        valid = super().is_valid()
        if not valid:
            print(f"ProductImageForm errors: {self.errors}")
        return valid


class ProductImageInline(admin.TabularInline):
    model = ProductImage
    form = ProductImageForm
    extra = 1
    fields = ['image', 'image_url', 'alt_text', 'is_primary']

    def get_formset(self, request, obj=None, **kwargs):
        """
        Override to add custom initialization
        """
        formset = super().get_formset(request, obj, **kwargs)

        # Store original init
        original_init = formset.form.__init__

        # Define custom init
        def custom_init(self, *args, **kwargs):
            original_init(self, *args, **kwargs)

            # If this is an existing instance with a URL in the image field
            instance = kwargs.get('instance')
            if instance and instance.pk and isinstance(instance.image, str) and instance.image.startswith(('http://', 'https://')):
                # Move the URL to image_url field and clear image field
                if not instance.image_url:
                    instance.image_url = instance.image
                instance.image = None

        # Replace init
        formset.form.__init__ = custom_init

        return formset


class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 0
    fields = ['title', 'color', 'size', 'gender', 'price', 'is_available']

    # Limit the number of variants shown in the inline form
    max_num = 20

    # Show a link to view all variants instead of loading them all in the form
    template = 'admin/products/product/edit_inline/tabular_variants.html'


class ReviewInline(admin.TabularInline):
    model = Review
    extra = 0
    readonly_fields = ['user', 'rating', 'comment', 'created_at']
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}


class ProductAdminForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = '__all__'

    def clean(self):
        cleaned_data = super().clean()
        # Add any custom validation here
        return cleaned_data

    def is_valid(self):
        valid = super().is_valid()
        if not valid:
            # Print form errors for debugging
            print(f"Form errors: {self.errors}")
        return valid

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    form = ProductAdminForm
    list_display = ['name', 'price', 'compare_price', 'discount_percentage_display', 'stock', 'get_categories', 'is_featured', 'is_active', 'cleaned', 'created_at']
    list_filter = ['is_active', 'is_featured', 'cleaned', 'categories']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ProductVariantInline, ProductImageInline, ReviewInline]
    readonly_fields = ['id', 'created_at', 'updated_at', 'discount_percentage_display', 'printify_id', 'cleaned', 'last_image_sync', 'variants_json']
    actions = ['sync_images_action']
    change_list_template = 'admin/products/product/change_list.html'
    change_form_template = 'admin/products/product/change_form.html'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('sync-products/', self.admin_site.admin_view(self.sync_products_view), name='sync-products'),
            path('sync-product-variants/<uuid:product_id>/', self.admin_site.admin_view(self.sync_product_variants_view), name='sync-product-variants'),
            path('clean-description/<uuid:product_id>/', self.admin_site.admin_view(self.clean_description_view), name='clean-description'),
            path('sync-images/<uuid:product_id>/', self.admin_site.admin_view(self.sync_images_view), name='sync-images'),
        ]
        return custom_urls + urls

    def sync_products_view(self, request):
        """
        View to handle the sync products button click - Production-ready with timeout handling
        """
        import time
        import threading
        from django.core.cache import cache

        # Check if a sync is already running
        sync_status = cache.get('printify_sync_status')
        if sync_status and sync_status.get('status') == 'running':
            self.message_user(
                request,
                "A sync operation is already in progress. Please wait for it to complete.",
                messages.WARNING
            )
            return HttpResponseRedirect(reverse('admin:products_product_changelist'))

        # Check if user wants to force a quick sync (sync only first few products)
        quick_sync = request.GET.get('quick', 'false').lower() == 'true'

        if quick_sync:
            # Quick sync - process only first 2 products to avoid timeouts
            try:
                start_time = time.time()
                synced_count = self.sync_products_from_printify_quick(request)
                duration = time.time() - start_time

                self.message_user(
                    request,
                    f"Quick sync completed: {synced_count} products synced in {duration:.1f} seconds.",
                    messages.SUCCESS
                )
            except Exception as e:
                self.message_user(
                    request,
                    f"Quick sync failed: {str(e)}",
                    messages.ERROR
                )
        else:
            # Full sync - run in background to avoid timeouts
            try:
                # Set sync status in cache
                cache.set('printify_sync_status', {
                    'status': 'running',
                    'started_at': time.time(),
                    'user': request.user.username
                }, timeout=3600)  # 1 hour timeout

                # Start background sync
                sync_thread = threading.Thread(
                    target=self._background_sync_products,
                    args=(request.user.id,)
                )
                sync_thread.daemon = True
                sync_thread.start()

                self.message_user(
                    request,
                    "Full sync started in background. This may take several minutes. Check back later for results.",
                    messages.INFO
                )

            except Exception as e:
                cache.delete('printify_sync_status')
                self.message_user(
                    request,
                    f"Failed to start sync: {str(e)}",
                    messages.ERROR
                )

        # Redirect back to the product list
        return HttpResponseRedirect(reverse('admin:products_product_changelist'))

    def sync_product_variants_view(self, request, product_id):
        """
        View to handle the sync variants button click for a specific product
        """
        try:
            # Get the product
            product = Product.objects.get(pk=product_id)

            # Check if product has a Printify ID
            if not product.printify_id:
                self.message_user(
                    request,
                    f"Product '{product.name}' does not have a Printify ID.",
                    messages.ERROR
                )
                return HttpResponseRedirect(reverse('admin:products_product_change', args=[product_id]))

            # Sync variants for this product
            variant_count = self.sync_single_product_variants(product)

            self.message_user(
                request,
                f"Successfully synced {variant_count} variants for product '{product.name}'.",
                messages.SUCCESS
            )
        except Product.DoesNotExist:
            self.message_user(
                request,
                f"Product with ID {product_id} not found.",
                messages.ERROR
            )
        except Exception as e:
            self.message_user(
                request,
                f"Error syncing variants: {str(e)}",
                messages.ERROR
            )

        # Redirect back to the product detail page
        return HttpResponseRedirect(reverse('admin:products_product_change', args=[product_id]))

    def sync_products_from_printify(self, _request=None):
        """
        Sync products from Printify API

        Args:
            _request: Optional request object (not used but included for consistency)
        """
        from django.conf import settings
        from django.utils.text import slugify
        import decimal
        import logging

        logger = logging.getLogger(__name__)

        # Initialize API client
        client = PrintifyAPIClient()

        # Get shop ID from settings
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
        if not shop_id:
            raise ValueError("Printify shop ID not found in settings")

        # Get products from Printify
        printify_products = client.get_products(shop_id)

        # Check if the response is a list or a dict with a 'data' key
        if isinstance(printify_products, dict) and 'data' in printify_products:
            printify_products = printify_products.get('data', [])

        synced_count = 0

        # Process each product
        for product_data in printify_products:
            if isinstance(product_data, str):
                continue

            printify_id = str(product_data.get('id', ''))
            if not printify_id:
                continue

            # Get detailed product information
            printify_product = client.get_product(shop_id, printify_id)

            # Extract relevant data
            title = printify_product.get('title', '')
            description = printify_product.get('description', '')

            # Create a slug from the title
            slug = slugify(title)

            # Extract images
            images = printify_product.get('images', [])

            # Check if product already exists
            try:
                existing_product = Product.objects.get(printify_id=printify_id)

                # Update existing product
                existing_product.name = title
                existing_product.description = description
                existing_product.slug = slug
                existing_product.variants_json = printify_product.get('variants', [])

                # Get the first variant for pricing
                variants = printify_product.get('variants', [])
                if variants and len(variants) > 0:
                    variant = variants[0]
                    price = variant.get('price', 0)
                    try:
                        price = decimal.Decimal(price)
                    except (decimal.InvalidOperation, TypeError):
                        price = decimal.Decimal('0.00')

                    existing_product.price = price

                existing_product.save()

                # Sync variants
                self._sync_product_variants(existing_product, variants)

                # Sync images for existing product
                self._sync_product_images(existing_product, images)

                synced_count += 1

            except Product.DoesNotExist:
                # Create new product
                new_product = Product(
                    name=title,
                    description=description,
                    slug=slug,
                    printify_id=printify_id,
                    stock=100,  # Default stock
                    variants_json=printify_product.get('variants', [])
                )

                # Get the first variant for pricing
                variants = printify_product.get('variants', [])
                if variants and len(variants) > 0:
                    variant = variants[0]
                    price = variant.get('price', 0)
                    try:
                        price = decimal.Decimal(price)
                    except (decimal.InvalidOperation, TypeError):
                        price = decimal.Decimal('0.00')

                    new_product.price = price

                new_product.save()

                # Sync variants
                self._sync_product_variants(new_product, variants)

                # Sync images for new product
                self._sync_product_images(new_product, images)

                synced_count += 1

                # Add to default category if exists
                try:
                    default_category = Category.objects.get(name='Uncategorized')
                    new_product.categories.add(default_category)
                except Category.DoesNotExist:
                    # Create default category if it doesn't exist
                    default_category = Category.objects.create(
                        name='Uncategorized',
                        slug='uncategorized',
                        is_active=True
                    )
                    new_product.categories.add(default_category)

        return synced_count

    def sync_products_from_printify_quick(self, _request=None, limit=2):
        """
        Quick sync - only sync first few products to avoid timeouts

        Args:
            _request: Optional request object
            limit: Maximum number of products to sync
        """
        from django.conf import settings
        from django.utils.text import slugify
        import decimal
        import logging

        logger = logging.getLogger(__name__)

        # Initialize API client
        client = PrintifyAPIClient()

        # Get shop ID from settings
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
        if not shop_id:
            raise ValueError("Printify shop ID not found in settings")

        # Get products from Printify (limited)
        printify_products = client.get_products(shop_id)

        # Check if the response is a list or a dict with a 'data' key
        if isinstance(printify_products, dict) and 'data' in printify_products:
            printify_products = printify_products.get('data', [])

        # Limit the number of products to process
        printify_products = printify_products[:limit]

        synced_count = 0

        # Process each product (limited)
        for product_data in printify_products:
            if isinstance(product_data, str):
                continue

            printify_id = str(product_data.get('id', ''))
            if not printify_id:
                continue

            try:
                # Get detailed product information
                printify_product = client.get_product(shop_id, printify_id)

                # Extract relevant data
                title = printify_product.get('title', '')
                description = printify_product.get('description', '')
                slug = slugify(title)
                images = printify_product.get('images', [])
                variants = printify_product.get('variants', [])

                # Check if product already exists
                try:
                    existing_product = Product.objects.get(printify_id=printify_id)

                    # Update existing product
                    existing_product.name = title
                    existing_product.description = description
                    existing_product.slug = slug
                    existing_product.variants_json = variants

                    # Get the first variant for pricing
                    if variants and len(variants) > 0:
                        variant = variants[0]
                        price = variant.get('price', 0)
                        try:
                            price = decimal.Decimal(price)
                        except (decimal.InvalidOperation, TypeError):
                            price = decimal.Decimal('0.00')
                        existing_product.price = price

                    existing_product.save()

                    # Sync variants and images
                    self._sync_product_variants(existing_product, variants)
                    self._sync_product_images(existing_product, images)

                    synced_count += 1

                except Product.DoesNotExist:
                    # Create new product
                    new_product = Product(
                        name=title,
                        description=description,
                        slug=slug,
                        printify_id=printify_id,
                        stock=100,
                        variants_json=variants
                    )

                    # Get the first variant for pricing
                    if variants and len(variants) > 0:
                        variant = variants[0]
                        price = variant.get('price', 0)
                        try:
                            price = decimal.Decimal(price)
                        except (decimal.InvalidOperation, TypeError):
                            price = decimal.Decimal('0.00')
                        new_product.price = price

                    new_product.save()

                    # Sync variants and images
                    self._sync_product_variants(new_product, variants)
                    self._sync_product_images(new_product, images)

                    synced_count += 1

                    # Add to default category
                    try:
                        default_category = Category.objects.get(name='Uncategorized')
                        new_product.categories.add(default_category)
                    except Category.DoesNotExist:
                        default_category = Category.objects.create(
                            name='Uncategorized',
                            slug='uncategorized',
                            is_active=True
                        )
                        new_product.categories.add(default_category)

            except Exception as e:
                logger.error(f"Error syncing product {printify_id}: {str(e)}")
                continue

        return synced_count

    def _background_sync_products(self, user_id):
        """
        Background sync method that runs in a separate thread
        """
        import time
        from django.core.cache import cache
        from django.contrib.auth import get_user_model

        User = get_user_model()

        try:
            start_time = time.time()

            # Update status
            cache.set('printify_sync_status', {
                'status': 'running',
                'started_at': start_time,
                'user_id': user_id,
                'progress': 'Starting sync...'
            }, timeout=3600)

            # Run the full sync
            synced_count = self.sync_products_from_printify()

            end_time = time.time()
            duration = end_time - start_time

            # Update final status
            cache.set('printify_sync_status', {
                'status': 'completed',
                'started_at': start_time,
                'completed_at': end_time,
                'duration': duration,
                'user_id': user_id,
                'synced_count': synced_count,
                'message': f'Successfully synced {synced_count} products in {duration:.1f} seconds'
            }, timeout=3600)

        except Exception as e:
            # Update error status
            cache.set('printify_sync_status', {
                'status': 'error',
                'started_at': time.time(),
                'user_id': user_id,
                'error': str(e),
                'message': f'Sync failed: {str(e)}'
            }, timeout=3600)

    def _sync_product_variants(self, product, variants):
        """
        Sync product variants from Printify

        Args:
            product: Product instance
            variants: List of variant data from Printify API
        """
        import decimal

        # If no variants, return early
        if not variants:
            return

        # Keep track of processed variant IDs
        processed_variant_ids = []

        # Process each variant
        for variant in variants:
            variant_id = str(variant.get('id', ''))
            if not variant_id:
                continue

            title = variant.get('title', '')
            price = variant.get('price', 0)
            is_enabled = variant.get('is_enabled', True)

            # Convert price to Decimal
            try:
                price = decimal.Decimal(price)
            except (decimal.InvalidOperation, TypeError):
                price = decimal.Decimal('0.00')

            # Extract color and size from title
            color, size = self._extract_color_size(title)

            # Determine gender targeting based on product title or description
            gender = self._determine_gender(product.name, product.description)

            # Create or update the variant
            try:
                product_variant = ProductVariant.objects.get(product=product, variant_id=variant_id)

                # Update existing variant
                product_variant.title = title
                product_variant.color = color
                product_variant.size = size
                product_variant.gender = gender
                product_variant.price = price
                product_variant.is_available = is_enabled

                product_variant.save()
                print(f"Updated variant: {title} for product {product.name}")

            except ProductVariant.DoesNotExist:
                # Create new variant
                ProductVariant.objects.create(
                    product=product,
                    variant_id=variant_id,
                    title=title,
                    color=color,
                    size=size,
                    gender=gender,
                    price=price,
                    is_available=is_enabled
                )
                print(f"Created variant: {title} for product {product.name}")

            processed_variant_ids.append(variant_id)

        # Delete variants that no longer exist
        deleted_count = ProductVariant.objects.filter(
            product=product
        ).exclude(
            variant_id__in=processed_variant_ids
        ).delete()[0]

        if deleted_count > 0:
            print(f"Deleted {deleted_count} variants that no longer exist for product {product.name}")

    def _extract_color_size(self, title):
        """Extract color and size from variant title"""
        # Default values
        color = None
        size = None

        # Common pattern: "Color / Size"
        if ' / ' in title:
            parts = title.split(' / ')
            if len(parts) >= 2:
                color = parts[0].strip()
                size = parts[1].strip()
        # Size only pattern (common for some products)
        elif title.strip() in ['XS', 'S', 'M', 'L', 'XL', '2XL', '3XL', '4XL', '5XL']:
            size = title.strip()

        return color, size

    def sync_single_product_variants(self, product):
        """
        Sync variants for a single product from Printify

        Args:
            product: Product instance to sync variants for

        Returns:
            int: Number of variants synced
        """
        from django.conf import settings

        # Initialize API client
        client = PrintifyAPIClient()

        # Get shop ID from settings
        shop_id = getattr(settings, 'PRINTIFY_SHOP_ID', None)
        if not shop_id:
            raise ValueError("Printify shop ID not found in settings")

        # Get detailed product information from Printify
        printify_product = client.get_product(shop_id, product.printify_id)

        # Extract variants
        variants = printify_product.get('variants', [])
        if not variants:
            return 0

        # Update the variants_json field
        product.variants_json = variants
        product.save(update_fields=['variants_json'])

        # Sync variants
        self._sync_product_variants(product, variants)

        return len(variants)

    def _determine_gender(self, title, description):
        """Determine gender targeting based on product title or description"""
        import re
        title_desc = (title + ' ' + description).lower()

        if re.search(r'\bwomen\b|\bwoman\b|\bladies\b|\bfemale\b', title_desc):
            return 'Women'
        elif re.search(r'\bmen\b|\bman\b|\bmale\b', title_desc):
            return 'Men'
        elif re.search(r'\bkids\b|\bchildren\b|\bchild\b|\bboy\b|\bgirl\b', title_desc):
            return 'Kids'
        else:
            return 'Unisex'

    def _sync_product_images(self, product, images):
        """
        Sync product images from Printify

        Args:
            product: Product instance
            images: List of image data from Printify API
        """
        # If no images, return early
        if not images:
            return

        # Get existing images for this product
        existing_images = ProductImage.objects.filter(product=product)

        # If there are existing images but no new images, keep the existing ones
        if existing_images.exists() and not images:
            return

        # If we're updating with new images, remove old ones
        if images:
            existing_images.delete()

        # Add new images
        for i, image_data in enumerate(images):
            image_url = image_data.get('src')
            if not image_url:
                continue

            try:
                # Create product image with just the URL, don't try to download
                ProductImage.objects.create(
                    product=product,
                    image=None,  # Don't set the image field directly
                    image_url=image_url,  # Just store the URL
                    alt_text=f"{product.name} - Image {i+1}",
                    is_primary=(i == 0)  # First image is primary
                )
                print(f"Added image {i+1} for product {product.name}: {image_url}")
            except Exception as e:
                print(f"Error adding image for product {product.name}: {str(e)}")
                continue

    def get_categories(self, obj):
        return ", ".join([c.name for c in obj.categories.all()])
    get_categories.short_description = 'Categories'

    def clean_description_view(self, request, product_id):
        """
        View to handle the clean description button click
        """
        try:
            # Get the product
            product = get_object_or_404(Product, pk=product_id)

            # Get the original description
            original_description = product.description

            # Check if we should use AI cleaning (if available)
            use_ai = request.GET.get('use_ai', 'false').lower() == 'true'

            if use_ai:
                # Get API key from settings (you would need to add this to your settings.py)
                from django.conf import settings
                api_key = getattr(settings, 'OPENAI_API_KEY', None)

                # Clean the description with AI
                cleaned_description = clean_description_with_ai(original_description, api_key)
                message = "Description cleaned with AI assistance"
            else:
                # Clean the description with basic cleaning
                cleaned_description = clean_description(original_description)
                message = "Description cleaned successfully"

            # Update the product
            product.description = cleaned_description
            product.cleaned = True
            product.save()

            self.message_user(request, message, messages.SUCCESS)

        except Exception as e:
            self.message_user(
                request,
                f"Error cleaning description: {str(e)}",
                messages.ERROR
            )

        # Redirect back to the product detail page
        return HttpResponseRedirect(reverse('admin:products_product_change', args=[product_id]))

    def discount_percentage_display(self, obj):
        return f"{obj.discount_percentage}%" if obj.discount_percentage > 0 else "-"
    discount_percentage_display.short_description = "Discount"

    def sync_images_action(self, request, queryset):
        """
        Admin action to sync images for selected products
        """
        image_sync_service = ImageSyncService()

        # Filter products that have Printify IDs
        products_with_printify_id = queryset.filter(printify_id__isnull=False).exclude(printify_id='')
        products_without_printify_id = queryset.filter(printify_id__isnull=True) | queryset.filter(printify_id='')

        if products_without_printify_id.exists():
            product_names = ', '.join([p.name for p in products_without_printify_id[:3]])
            if products_without_printify_id.count() > 3:
                product_names += f" and {products_without_printify_id.count() - 3} more"

            self.message_user(
                request,
                f"Skipped {products_without_printify_id.count()} products without Printify ID: {product_names}",
                messages.WARNING
            )

        if not products_with_printify_id.exists():
            self.message_user(
                request,
                "No products with Printify ID found in selection.",
                messages.ERROR
            )
            return

        # Sync images for products with Printify ID
        summary = image_sync_service.sync_multiple_products(products_with_printify_id)

        # Display results
        if summary['successful_syncs'] > 0:
            self.message_user(
                request,
                f"Successfully synced images for {summary['successful_syncs']} products. "
                f"Added {summary['total_images_added']} images, updated {summary['total_images_updated']} images.",
                messages.SUCCESS
            )

        if summary['failed_syncs'] > 0:
            error_messages = summary['errors'][:3]  # Show first 3 errors
            error_text = '; '.join(error_messages)
            if len(summary['errors']) > 3:
                error_text += f" and {len(summary['errors']) - 3} more errors"

            self.message_user(
                request,
                f"Failed to sync images for {summary['failed_syncs']} products. Errors: {error_text}",
                messages.ERROR
            )

    sync_images_action.short_description = "Sync images from Printify"

    def sync_images_view(self, request, product_id):
        """
        View to handle the sync images button click for a specific product
        """
        try:
            # Get the product
            product = Product.objects.get(pk=product_id)

            # Check if product has a Printify ID
            if not product.printify_id:
                self.message_user(
                    request,
                    f"Product '{product.name}' does not have a Printify ID.",
                    messages.ERROR
                )
                return HttpResponseRedirect(reverse('admin:products_product_change', args=[product_id]))

            # Sync images for this product
            image_sync_service = ImageSyncService()
            result = image_sync_service.sync_product_images(product)

            if result['success']:
                self.message_user(
                    request,
                    f"Successfully synced images for '{product.name}': "
                    f"{result['images_added']} added, {result['images_updated']} updated.",
                    messages.SUCCESS
                )
            else:
                error_text = '; '.join(result['errors'])
                self.message_user(
                    request,
                    f"Failed to sync images for '{product.name}': {error_text}",
                    messages.ERROR
                )

        except Product.DoesNotExist:
            self.message_user(
                request,
                f"Product with ID {product_id} not found.",
                messages.ERROR
            )
        except Exception as e:
            self.message_user(
                request,
                f"Error syncing images: {str(e)}",
                messages.ERROR
            )

        # Redirect back to the product detail page
        return HttpResponseRedirect(reverse('admin:products_product_change', args=[product_id]))


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ['product', 'is_primary', 'has_image', 'image_preview', 'file_status', 'created_at']
    list_filter = ['is_primary', 'product']
    search_fields = ['product__name', 'alt_text']
    fields = ['product', 'image', 'image_url', 'alt_text', 'is_primary']
    readonly_fields = ['image_preview', 'file_status']
    actions = ['sync_product_images_action']

    def has_image(self, obj):
        try:
            # Use the safe image_display_url property
            return bool(obj.image_display_url)
        except Exception:
            return False
    has_image.boolean = True
    has_image.short_description = 'Has Image'

    def image_preview(self, obj):
        try:
            # Use the safe image_display_url property
            image_url = obj.image_display_url
            if image_url:
                return format_html('<img src="{}" style="max-height: 50px; max-width: 100px;" />', image_url)
            else:
                # Check if file exists but is missing
                if obj.image and not obj.file_exists():
                    return format_html('<span style="color: red;">Missing file: {}</span>', str(obj.image))
                return "No image"
        except Exception as e:
            return f"Preview error: {str(e)}"
    image_preview.short_description = 'Preview'

    def file_status(self, obj):
        """Show file status for debugging"""
        try:
            if obj.image_url:
                return format_html('<span style="color: green;">External URL</span>')
            elif obj.image:
                if obj.file_exists():
                    return format_html('<span style="color: green;">File exists</span>')
                else:
                    return format_html('<span style="color: red;">File missing</span>')
            else:
                return format_html('<span style="color: gray;">No image</span>')
        except Exception as e:
            return f"Error: {str(e)}"
    file_status.short_description = 'File Status'

    def sync_product_images_action(self, request, queryset):
        """
        Admin action to sync images for products related to selected images
        """
        # Get unique products from selected images
        products = set()
        for image in queryset:
            if image.product and image.product.printify_id:
                products.add(image.product)

        if not products:
            self.message_user(
                request,
                "No products with Printify ID found for selected images.",
                messages.ERROR
            )
            return

        # Sync images for the products
        image_sync_service = ImageSyncService()
        summary = image_sync_service.sync_multiple_products(list(products))

        # Display results
        if summary['successful_syncs'] > 0:
            self.message_user(
                request,
                f"Successfully synced images for {summary['successful_syncs']} products. "
                f"Added {summary['total_images_added']} images, updated {summary['total_images_updated']} images.",
                messages.SUCCESS
            )

        if summary['failed_syncs'] > 0:
            error_messages = summary['errors'][:2]  # Show first 2 errors
            error_text = '; '.join(error_messages)
            if len(summary['errors']) > 2:
                error_text += f" and {len(summary['errors']) - 2} more errors"

            self.message_user(
                request,
                f"Failed to sync images for {summary['failed_syncs']} products. Errors: {error_text}",
                messages.ERROR
            )

    sync_product_images_action.short_description = "Sync images from Printify for related products"


@admin.register(ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    list_display = ['product', 'title', 'color', 'size', 'gender', 'price', 'is_available', 'created_at']
    list_filter = ['is_available', 'gender', 'size', 'color']
    search_fields = ['product__name', 'title', 'variant_id']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ['product', 'user', 'rating', 'created_at']
    list_filter = ['rating']
    search_fields = ['product__name', 'user__username', 'comment']
    readonly_fields = ['created_at']
