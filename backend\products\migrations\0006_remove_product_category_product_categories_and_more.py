# Generated by Django 5.0.2 on 2025-05-04 08:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_productimage_image_url'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='category',
        ),
        migrations.AddField(
            model_name='product',
            name='categories',
            field=models.ManyToManyField(related_name='products', to='products.category'),
        ),
        migrations.AddField(
            model_name='product',
            name='printify_id',
            field=models.CharField(blank=True, db_index=True, help_text='Printify product ID for synced products', max_length=100, null=True),
        ),
    ]
