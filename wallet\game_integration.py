"""
Game integration utilities for token economy system
"""
from django.contrib.auth.models import User
from .models import Wallet


def handle_game_loss(user_id, game_id=None, description="Game loss penalty"):
    """
    Handle token deduction when user loses a game
    
    Args:
        user_id: ID of the user who lost
        game_id: Optional game ID for reference
        description: Description for the transaction
    
    Returns:
        dict: Result of the operation
    """
    try:
        user = User.objects.get(id=user_id)
        wallet = user.wallet
        
        # Check if user has tokens to deduct
        if wallet.balance <= 0:
            return {
                'success': False,
                'error': 'User has no tokens to deduct',
                'balance': 0,
                'can_play': False
            }
        
        # Deduct 1 token for game loss
        wallet.deduct_game_loss_penalty(game_id=game_id, description=description)
        
        return {
            'success': True,
            'tokens_deducted': 1,
            'remaining_balance': wallet.balance,
            'can_play': wallet.can_play_games(),
            'can_use_discounts': wallet.can_use_token_discounts()
        }
        
    except User.DoesNotExist:
        return {
            'success': False,
            'error': 'User not found'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def check_game_eligibility(user_id):
    """
    Check if user can play token-based games (requires 2 tokens for participation)

    Args:
        user_id: ID of the user

    Returns:
        dict: Eligibility status
    """
    try:
        user = User.objects.get(id=user_id)
        wallet = user.wallet

        # Check if user has enough tokens for participation (2 tokens required)
        can_play = wallet.balance >= 2

        return {
            'can_play': can_play,
            'balance': wallet.balance,
            'balance_in_inr': float(wallet.balance_in_inr),
            'message': 'You can play games' if can_play else 'You need at least 2 tokens to play games (participation fee)'
        }

    except User.DoesNotExist:
        return {
            'can_play': False,
            'balance': 0,
            'balance_in_inr': 0.0,
            'message': 'User not found'
        }
    except Exception as e:
        return {
            'can_play': False,
            'balance': 0,
            'balance_in_inr': 0.0,
            'message': str(e)
        }


def deduct_game_participation_fee(user_id, game_type, game_id=None):
    """
    Deduct 2 tokens for game participation at the start of the game

    Args:
        user_id: ID of the user
        game_type: Type of game (e.g., 'color_match', 'memory_card')
        game_id: Optional game ID for reference

    Returns:
        dict: Result of the operation
    """
    try:
        user = User.objects.get(id=user_id)
        wallet = user.wallet

        # Check if user has enough tokens for participation
        if wallet.balance < 2:
            return {
                'success': False,
                'error': 'Insufficient tokens for participation (2 tokens required)',
                'balance': wallet.balance,
                'can_play': False
            }

        # Deduct 2 tokens for participation
        wallet.spend_tokens(
            amount=2,
            transaction_type='game_participation',
            description=f"Participation fee for {game_type.replace('_', ' ').title()} game"
        )

        return {
            'success': True,
            'tokens_deducted': 2,
            'remaining_balance': wallet.balance,
            'can_play': wallet.can_play_games(),
            'message': f'2 tokens deducted for {game_type.replace("_", " ").title()} participation'
        }

    except User.DoesNotExist:
        return {
            'success': False,
            'error': 'User not found'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


def get_user_token_status(user_id):
    """
    Get comprehensive token status for user
    
    Args:
        user_id: ID of the user
    
    Returns:
        dict: Complete token status
    """
    try:
        user = User.objects.get(id=user_id)
        wallet = user.wallet
        
        # Check for pending token requests
        from .models import TokenRequest
        pending_requests = TokenRequest.objects.filter(
            user=user, 
            status='pending'
        ).count()
        
        return {
            'balance': wallet.balance,
            'total_earned': wallet.total_earned,
            'total_spent': wallet.total_spent,
            'can_play_games': wallet.can_play_games(),
            'can_use_token_discounts': wallet.can_use_token_discounts(),
            'is_zero_balance': wallet.balance == 0,
            'pending_refill_requests': pending_requests,
            'balance_in_inr': float(wallet.balance_in_inr)
        }
        
    except User.DoesNotExist:
        return {
            'balance': 0,
            'error': 'User not found'
        }
    except Exception as e:
        return {
            'balance': 0,
            'error': str(e)
        }


# Utility functions for gaming system integration
class TokenEconomyMixin:
    """
    Mixin class for gaming views to integrate token economy
    """
    
    def check_user_can_play(self, user):
        """Check if user can play token-based games"""
        if not hasattr(user, 'wallet'):
            return False, "User wallet not found"
        
        if not user.wallet.can_play_games():
            return False, "You need tokens to play games. Request a refill if your balance is 0."
        
        return True, "User can play games"
    
    def handle_game_result(self, user, game_result, game_id=None):
        """
        Handle game result and token transactions
        
        Args:
            user: User object
            game_result: 'win', 'loss', 'draw'
            game_id: Optional game ID
        
        Returns:
            dict: Transaction result
        """
        wallet = user.wallet
        
        if game_result == 'loss':
            return handle_game_loss(user.id, game_id)
        elif game_result == 'win':
            # Add tokens for win: +5 tokens (new standardized rule)
            wallet.add_tokens(
                amount=5,
                transaction_type='game_win',
                description=f"Game win reward"
            )
            return {
                'success': True,
                'tokens_earned': 5,
                'new_balance': wallet.balance
            }
        elif game_result == 'draw':
            # Add tokens for draw: +2 tokens (new standardized rule)
            wallet.add_tokens(
                amount=2,
                transaction_type='game_draw',
                description=f"Game draw reward"
            )
            return {
                'success': True,
                'tokens_earned': 2,
                'new_balance': wallet.balance
            }
        elif game_result == 'participate':
            # Add tokens for participation: +2 tokens (new standardized rule)
            wallet.add_tokens(
                amount=2,
                transaction_type='game_participation',
                description=f"Game participation reward"
            )
            return {
                'success': True,
                'tokens_earned': 2,
                'new_balance': wallet.balance
            }
        
        return {
            'success': False,
            'error': 'Invalid game result'
        }


# API endpoint helpers
def process_game_transaction(user_id, game_type, game_result, difficulty='medium', game_id=None):
    """
    Process game transaction with standardized token rewards
    Note: 2 tokens were already deducted for participation at game start

    Args:
        user_id: ID of the user
        game_type: Type of game (e.g., 'color_match', 'memory_card')
        game_result: 'win', 'loss', 'draw'
        difficulty: Game difficulty level (not used but kept for compatibility)
        game_id: Optional game ID for reference

    Returns:
        dict: Transaction result
    """
    try:
        user = User.objects.get(id=user_id)
        wallet = user.wallet

        # Standardized token rewards (accounting for 2 tokens already deducted for participation)
        # Net rewards: Win: +5, Draw: +2, Loss: -1 (total after participation fee)
        if game_result == 'win':
            tokens_earned = 5  # Add 5 tokens for win
            transaction_type = 'game_win'
            description = f"Won {game_type.replace('_', ' ').title()} game (+5 tokens)"
        elif game_result == 'draw':
            tokens_earned = 2  # Add 2 tokens for draw
            transaction_type = 'game_draw'
            description = f"Draw in {game_type.replace('_', ' ').title()} game (+2 tokens)"
        elif game_result == 'loss':
            # For loss: deduct additional 1 token (total -3: -2 participation, -1 loss penalty)
            tokens_earned = -1
            transaction_type = 'game_loss'
            description = f"Lost {game_type.replace('_', ' ').title()} game (-1 token penalty)"
        else:
            return {
                'success': False,
                'message': 'Invalid game result'
            }

        # Process the transaction
        if tokens_earned > 0:
            wallet.add_tokens(
                amount=tokens_earned,
                transaction_type=transaction_type,
                description=description
            )
        else:
            # For losses, deduct 1 additional token (loss penalty)
            if wallet.balance > 0:
                wallet.deduct_game_loss_penalty(
                    game_id=game_id,
                    description=description
                )
            else:
                # If no tokens to deduct, just record the attempt
                return {
                    'success': True,
                    'tokens_earned': 0,  # No additional penalty if balance is 0
                    'new_balance': wallet.balance,
                    'balance_in_inr': float(wallet.balance_in_inr),
                    'transaction_type': 'game_loss_no_penalty',
                    'description': f"Lost {game_type.replace('_', ' ').title()} game (no additional penalty - insufficient balance)",
                    'can_play_more': wallet.can_play_games()
                }

        return {
            'success': True,
            'tokens_earned': tokens_earned,
            'new_balance': wallet.balance,
            'balance_in_inr': float(wallet.balance_in_inr),
            'transaction_type': transaction_type,
            'description': description,
            'can_play_more': wallet.can_play_games()
        }

    except User.DoesNotExist:
        return {
            'success': False,
            'message': 'User not found'
        }
    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }


def validate_checkout_token_usage(user, tokens_to_use):
    """
    Validate if user can use tokens during checkout

    Args:
        user: User object
        tokens_to_use: Number of tokens user wants to use

    Returns:
        tuple: (is_valid, error_message)
    """
    if not hasattr(user, 'wallet'):
        return False, "User wallet not found"

    wallet = user.wallet

    if not wallet.can_use_token_discounts():
        return False, "You cannot use token discounts with zero balance"

    if tokens_to_use > wallet.balance:
        return False, f"Insufficient tokens. You have {wallet.balance} tokens."

    return True, "Token usage is valid"
