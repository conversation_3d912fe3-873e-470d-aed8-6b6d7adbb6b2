#!/usr/bin/env python
"""
Test Production Login System
===========================

Test both username and email login functionality in production
"""

import requests
import json

def test_login_endpoint(base_url, credential, password, credential_type):
    """Test login with either username or email"""
    
    print(f"\n🔧 Testing {credential_type} login...")
    print(f"Credential: {credential}")
    print(f"Password: {'*' * len(password)}")
    
    # Test the custom login endpoint
    login_url = f"{base_url}/api/auth/login/"
    
    # Prepare data based on credential type
    if credential_type == "email":
        login_data = {
            "email": credential,
            "password": password
        }
    else:
        login_data = {
            "username": credential,
            "password": password
        }
    
    try:
        print(f"POST {login_url}")
        print(f"Data: {json.dumps({**login_data, 'password': '***'}, indent=2)}")
        
        response = requests.post(
            login_url,
            json=login_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {credential_type.title()} login SUCCESSFUL!")
            print(f"Access Token: {data.get('access', 'N/A')[:20]}...")
            print(f"User: {data.get('user', {}).get('username', 'N/A')}")
            print(f"Email: {data.get('user', {}).get('email', 'N/A')}")
            return True, data
        else:
            print(f"❌ {credential_type.title()} login FAILED!")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except:
                print(f"Error: {response.text}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False, None

def test_user_data_fetch(base_url, access_token):
    """Test fetching user data with access token"""
    
    print(f"\n🔧 Testing user data fetch...")
    
    user_url = f"{base_url}/api/auth/user/"
    
    try:
        response = requests.get(
            user_url,
            headers={
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json'
            },
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ User data fetch SUCCESSFUL!")
            print(f"Username: {data.get('username', 'N/A')}")
            print(f"Email: {data.get('email', 'N/A')}")
            print(f"First Name: {data.get('first_name', 'N/A')}")
            print(f"Last Name: {data.get('last_name', 'N/A')}")
            return True, data
        else:
            print(f"❌ User data fetch FAILED!")
            try:
                error_data = response.json()
                print(f"Error: {error_data}")
            except:
                print(f"Error: {response.text}")
            return False, None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False, None

def main():
    """Main test function"""
    
    print("🔐 TESTING PRODUCTION LOGIN SYSTEM")
    print("=" * 50)
    
    base_url = "https://pickmetrendofficial-render.onrender.com"
    
    # Test credentials (you'll need to provide real ones)
    print("\n📝 Please provide test credentials:")
    print("Note: Use a real account for testing")
    
    username = input("Enter username: ").strip()
    email = input("Enter email: ").strip()
    password = input("Enter password: ").strip()
    
    if not username or not email or not password:
        print("❌ All fields are required for testing")
        return
    
    print(f"\n🌐 Testing against: {base_url}")
    print("=" * 50)
    
    # Test 1: Login with username
    username_success, username_data = test_login_endpoint(
        base_url, username, password, "username"
    )
    
    # Test 2: Login with email
    email_success, email_data = test_login_endpoint(
        base_url, email, password, "email"
    )
    
    # Test 3: Fetch user data if login successful
    if username_success and username_data:
        access_token = username_data.get('access')
        if access_token:
            test_user_data_fetch(base_url, access_token)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if username_success:
        print("✅ Username login: WORKING")
    else:
        print("❌ Username login: FAILED")
    
    if email_success:
        print("✅ Email login: WORKING")
    else:
        print("❌ Email login: FAILED")
    
    if username_success and email_success:
        print("\n🎉 BOTH USERNAME AND EMAIL LOGIN ARE WORKING!")
        print("✅ Your login system supports both authentication methods")
        print("✅ Users can login with either username or email")
        print("✅ Production authentication is fully functional")
    elif username_success or email_success:
        print("\n⚠️ PARTIAL SUCCESS")
        print("One authentication method is working, but not both")
        print("This may indicate a configuration issue")
    else:
        print("\n❌ BOTH LOGIN METHODS FAILED")
        print("There may be an issue with the authentication system")
        print("Check credentials and server configuration")
    
    print("\n💡 Frontend Integration:")
    print("The frontend login form should work with both username and email")
    print("Users can enter either in the 'Username or Email' field")

if __name__ == '__main__':
    main()
