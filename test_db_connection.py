#!/usr/bin/env python3
"""
Test PostgreSQL database connection
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

def test_database_connection():
    print("🔍 Testing PostgreSQL Database Connection...")
    
    try:
        from django.db import connection
        from django.core.management import call_command
        
        # Test basic connection
        print("1. Testing basic database connection...")
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"   ✅ Basic connection successful: {result}")
        
        # Test if tables exist
        print("2. Checking if tables exist...")
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%order%'
            LIMIT 5
        """)
        tables = cursor.fetchall()
        print(f"   📊 Order-related tables found: {len(tables)}")
        for table in tables:
            print(f"      - {table[0]}")
        
        # Test Django ORM
        print("3. Testing Django ORM...")
        from django.contrib.auth.models import User
        user_count = User.objects.count()
        print(f"   👥 Users in database: {user_count}")
        
        from orders.models import Order
        order_count = Order.objects.count()
        print(f"   📦 Orders in database: {order_count}")
        
        print("✅ Database connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        
        # Check if it's a connection error
        if "connection" in str(e).lower():
            print("\n🔧 Possible solutions:")
            print("   1. Make sure PostgreSQL is running")
            print("   2. Check database credentials in .env.local")
            print("   3. Create the database if it doesn't exist")
            print("   4. Run migrations: python manage.py migrate")
        
        # Check if it's a missing table error
        if "relation" in str(e).lower() and "does not exist" in str(e).lower():
            print("\n🔧 Missing tables detected. Running migrations...")
            try:
                call_command('migrate')
                print("   ✅ Migrations completed successfully!")
                return test_database_connection()  # Retry
            except Exception as migrate_error:
                print(f"   ❌ Migration failed: {migrate_error}")
        
        return False

def check_postgresql_service():
    print("\n🔍 Checking PostgreSQL service status...")
    import subprocess
    
    try:
        # Check if PostgreSQL is running (Windows)
        result = subprocess.run(['sc', 'query', 'postgresql-x64-14'], 
                              capture_output=True, text=True)
        if "RUNNING" in result.stdout:
            print("   ✅ PostgreSQL service is running")
            return True
        else:
            print("   ⚠️ PostgreSQL service not found or not running")
            
            # Try other common service names
            for service in ['postgresql-x64-15', 'postgresql-x64-16', 'PostgreSQL']:
                result = subprocess.run(['sc', 'query', service], 
                                      capture_output=True, text=True)
                if "RUNNING" in result.stdout:
                    print(f"   ✅ Found running PostgreSQL service: {service}")
                    return True
            
            print("   ❌ No running PostgreSQL service found")
            return False
            
    except Exception as e:
        print(f"   ⚠️ Could not check service status: {e}")
        return False

if __name__ == "__main__":
    print("🚀 PostgreSQL Database Diagnostic Tool")
    print("=" * 50)
    
    # Check service first
    service_running = check_postgresql_service()
    
    # Test database connection
    connection_success = test_database_connection()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    print(f"   PostgreSQL Service: {'✅ Running' if service_running else '❌ Not Running'}")
    print(f"   Database Connection: {'✅ Working' if connection_success else '❌ Failed'}")
    
    if not connection_success:
        print("\n🔧 NEXT STEPS:")
        print("   1. Start PostgreSQL service if not running")
        print("   2. Create database: createdb pickmetrend")
        print("   3. Run migrations: python manage.py migrate")
        print("   4. Test again: python test_db_connection.py")
