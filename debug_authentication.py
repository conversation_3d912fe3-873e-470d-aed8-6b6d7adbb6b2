#!/usr/bin/env python
"""
Authentication Debug Script
============================

This script helps debug authentication issues by:
1. Checking if users exist in the database
2. Testing password verification
3. Testing custom login endpoint
4. Checking user activation status
5. Verifying related objects (profile, wallet, etc.)
"""

import os
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from accounts.models import UserProfile
from wallet.models import Wallet
from gaming.models import PlayerStats


def check_users_in_database():
    """Check all users in the database"""
    print("👥 Users in Database")
    print("=" * 40)
    
    users = User.objects.all()
    print(f"Total users: {users.count()}")
    
    if users.count() == 0:
        print("❌ No users found in database!")
        return []
    
    for user in users:
        print(f"\n📋 User: {user.username}")
        print(f"   - ID: {user.id}")
        print(f"   - Email: {user.email}")
        print(f"   - Active: {user.is_active}")
        print(f"   - Staff: {user.is_staff}")
        print(f"   - Superuser: {user.is_superuser}")
        print(f"   - Date joined: {user.date_joined}")
        print(f"   - Last login: {user.last_login}")
        
        # Check password (can't show actual password, but can check if it's set)
        if user.password:
            print(f"   - Password: Set (hash: {user.password[:20]}...)")
        else:
            print(f"   - Password: ❌ NOT SET")
        
        # Check related objects
        try:
            profile = user.profile
            print(f"   - Profile: ✅ {profile}")
        except UserProfile.DoesNotExist:
            print(f"   - Profile: ❌ Missing")
        
        try:
            wallet = user.wallet
            print(f"   - Wallet: ✅ {wallet.balance} tokens")
        except Wallet.DoesNotExist:
            print(f"   - Wallet: ❌ Missing")
        
        try:
            stats = user.gaming_stats
            print(f"   - Gaming Stats: ✅ {stats}")
        except PlayerStats.DoesNotExist:
            print(f"   - Gaming Stats: ❌ Missing")
    
    return list(users)


def test_password_verification(username, password):
    """Test password verification for a specific user"""
    print(f"\n🔐 Testing Password for {username}")
    print("=" * 40)
    
    try:
        user = User.objects.get(username=username)
        print(f"User found: {user.username}")
        
        # Test password
        if user.check_password(password):
            print("✅ Password is correct")
            return True
        else:
            print("❌ Password is incorrect")
            return False
            
    except User.DoesNotExist:
        print(f"❌ User '{username}' does not exist")
        return False


def test_custom_login_endpoint(username, password):
    """Test the custom login endpoint directly"""
    print(f"\n🔗 Testing Custom Login Endpoint")
    print("=" * 40)
    
    client = Client()
    
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = client.post('/api/auth/login/',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content.decode()}")
        
        if response.status_code == 200:
            print("✅ Custom login endpoint works!")
            return True
        else:
            print("❌ Custom login endpoint failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception during login test: {e}")
        return False


def test_djoser_login_endpoint(username, password):
    """Test the Djoser JWT login endpoint"""
    print(f"\n🔗 Testing Djoser JWT Login Endpoint")
    print("=" * 40)
    
    client = Client()
    
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = client.post('/api/auth/djoser/jwt/create/',
                             data=json.dumps(login_data),
                             content_type='application/json')
        
        print(f"Response status: {response.status_code}")
        print(f"Response content: {response.content.decode()}")
        
        if response.status_code == 200:
            print("✅ Djoser JWT login works!")
            return True
        else:
            print("❌ Djoser JWT login failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception during Djoser login test: {e}")
        return False


def create_test_user():
    """Create a test user for debugging"""
    print(f"\n👤 Creating Test User")
    print("=" * 40)
    
    test_username = "debug_test_user"
    test_email = "<EMAIL>"
    test_password = "TestPass123!"
    
    # Delete existing test user
    User.objects.filter(username=test_username).delete()
    User.objects.filter(email=test_email).delete()
    
    try:
        # Create user
        user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password=test_password
        )
        user.is_active = True
        user.save()
        
        print(f"✅ Created test user: {user.username}")
        
        # Create related objects
        try:
            UserProfile.objects.create(user=user)
            print("✅ Created profile")
        except Exception as e:
            print(f"❌ Failed to create profile: {e}")
        
        try:
            Wallet.give_signup_bonus(user)
            print("✅ Created wallet with signup bonus")
        except Exception as e:
            print(f"❌ Failed to create wallet: {e}")
        
        try:
            PlayerStats.objects.create(user=user)
            print("✅ Created gaming stats")
        except Exception as e:
            print(f"❌ Failed to create gaming stats: {e}")
        
        return test_username, test_password
        
    except Exception as e:
        print(f"❌ Failed to create test user: {e}")
        return None, None


def main():
    """Main debug function"""
    print("🔧 Authentication Debug Script")
    print("=" * 50)
    
    # Check existing users
    users = check_users_in_database()
    
    # If no users exist, create a test user
    if not users:
        print("\n⚠️ No users found. Creating test user...")
        test_username, test_password = create_test_user()
        if test_username:
            users = [User.objects.get(username=test_username)]
    
    # Test with existing users
    if users:
        # Test with the first user
        user = users[0]
        print(f"\n🧪 Testing with user: {user.username}")
        
        # Ask for password (in production, you'd know the password)
        print(f"\n⚠️ To test login, we need the password for {user.username}")
        print("Common test passwords to try:")
        print("- TestPass123!")
        print("- password123")
        print("- admin123")
        print("- The password you used during registration")
        
        test_passwords = ["TestPass123!", "password123", "admin123"]
        
        for test_password in test_passwords:
            print(f"\n🔍 Trying password: {test_password}")
            
            # Test password verification
            if test_password_verification(user.username, test_password):
                # Test custom login endpoint
                test_custom_login_endpoint(user.username, test_password)
                
                # Test Djoser login endpoint
                test_djoser_login_endpoint(user.username, test_password)
                break
        else:
            print("\n❌ None of the test passwords worked")
            print("Please check what password was used during registration")
    
    print("\n📋 Debug Summary")
    print("=" * 40)
    print("1. Check if users exist in database")
    print("2. Verify user passwords are set correctly")
    print("3. Test custom login endpoint")
    print("4. Test Djoser login endpoint")
    print("5. Check user activation status")
    print("6. Verify related objects (profile, wallet, gaming stats)")
    
    print("\n💡 Next Steps:")
    print("1. If no users exist, register a new user")
    print("2. If password verification fails, check registration process")
    print("3. If custom login fails, check Django logs")
    print("4. If Djoser login fails, check Djoser configuration")


if __name__ == '__main__':
    main()
