import os
import importlib.util
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')

# Check if Celery is installed
celery_installed = importlib.util.find_spec('celery') is not None
redis_available = False

# Check if Redis is available
if celery_installed:
    try:
        import redis
        redis_client = redis.Redis.from_url(settings.CELERY_BROKER_URL)
        redis_client.ping()
        redis_available = True
    except (ImportError, redis.exceptions.ConnectionError, AttributeError):
        redis_available = False

# Use real Celery if it's installed and Redis is available
if celery_installed and redis_available:
    from celery import Celery

    # Create the Celery app
    app = Celery('dropshipping_backend')

    # Using a string here means the worker doesn't have to serialize
    # the configuration object to child processes.
    app.config_from_object('django.conf:settings', namespace='CELERY')

    # Load task modules from all registered Django app configs.
    app.autodiscover_tasks()

    @app.task(bind=True, ignore_result=True)
    def debug_task(self):
        print(f'Request: {self.request!r}')

    print("Using real Celery with Redis")
else:
    # Use mock Celery implementation
    from dropshipping_backend.celery_mock import app, shared_task
    print("Using mock Celery implementation (tasks will execute immediately)")
