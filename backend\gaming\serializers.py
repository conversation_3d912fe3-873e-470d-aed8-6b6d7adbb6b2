from rest_framework import serializers
from django.contrib.auth.models import User
from .models import GameType, Battle, GameMove, PlayerStats


class GameTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = GameType
        fields = ['id', 'name', 'display_name', 'description', 'rules', 'is_active']


class UserBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name']


class GameMoveSerializer(serializers.ModelSerializer):
    player = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = GameMove
        fields = ['id', 'player', 'move_data', 'move_number', 'timestamp']


class BattleSerializer(serializers.ModelSerializer):
    game_type = GameTypeSerializer(read_only=True)
    player1 = UserBasicSerializer(read_only=True)
    player2 = UserBasicSerializer(read_only=True)
    moves = GameMoveSerializer(many=True, read_only=True)
    duration = serializers.ReadOnlyField()
    
    class Meta:
        model = Battle
        fields = [
            'id', 'game_type', 'player1', 'player2', 'is_ai_battle',
            'status', 'result', 'game_state', 'moves_history', 'moves',
            'created_at', 'started_at', 'completed_at', 'duration',
            'tokens_awarded', 'winner_tokens', 'loser_tokens'
        ]


class BattleListSerializer(serializers.ModelSerializer):
    """Simplified serializer for battle lists"""
    game_type_name = serializers.CharField(source='game_type.display_name', read_only=True)
    player1_username = serializers.CharField(source='player1.username', read_only=True)
    player2_username = serializers.CharField(source='player2.username', read_only=True)
    opponent_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Battle
        fields = [
            'id', 'game_type_name', 'player1_username', 'player2_username', 
            'opponent_name', 'is_ai_battle', 'status', 'result',
            'created_at', 'completed_at', 'tokens_awarded'
        ]
    
    def get_opponent_name(self, obj):
        request_user = self.context.get('request').user if self.context.get('request') else None
        if not request_user:
            return None
        
        if obj.is_ai_battle:
            return "AI Bot"
        elif obj.player1.id == request_user.id:
            return obj.player2.username if obj.player2 else "Waiting..."
        elif obj.player2 and obj.player2.id == request_user.id:
            return obj.player1.username
        else:
            return "Unknown"


class PlayerStatsSerializer(serializers.ModelSerializer):
    user = UserBasicSerializer(read_only=True)
    win_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = PlayerStats
        fields = [
            'user', 'total_battles', 'battles_won', 'battles_lost', 
            'battles_drawn', 'win_rate', 'total_tokens_earned',
            'current_win_streak', 'best_win_streak', 'total_play_time',
            'created_at', 'updated_at'
        ]


class CreateBattleSerializer(serializers.Serializer):
    game_type = serializers.CharField()
    is_ai_battle = serializers.BooleanField(default=False)
    
    def validate_game_type(self, value):
        try:
            GameType.objects.get(name=value, is_active=True)
            return value
        except GameType.DoesNotExist:
            raise serializers.ValidationError("Invalid or inactive game type")


class MakeMoveSerializer(serializers.Serializer):
    move = serializers.JSONField()
    
    def validate_move(self, value):
        # Basic validation - specific validation happens in game logic
        if not value:
            raise serializers.ValidationError("Move data is required")
        return value
