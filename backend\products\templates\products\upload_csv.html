{% extends 'admin/base_site.html' %}
{% load static %}

{% block title %}Upload Products CSV{% endblock %}

{% block extrahead %}
<style>
    .upload-form {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .upload-form h1 {
        margin-bottom: 20px;
        color: #447e9b;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ccc;
        border-radius: 3px;
    }
    
    .btn-primary {
        background-color: #447e9b;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }
    
    .btn-primary:hover {
        background-color: #385f6e;
    }
    
    .messages {
        margin-bottom: 20px;
    }
    
    .message {
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 3px;
    }
    
    .success {
        background-color: #dff0d8;
        color: #3c763d;
    }
    
    .error {
        background-color: #f2dede;
        color: #a94442;
    }
    
    .warning {
        background-color: #fcf8e3;
        color: #8a6d3b;
    }
    
    .info {
        background-color: #d9edf7;
        color: #31708f;
    }
    
    .csv-example {
        margin-top: 20px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 3px;
    }
    
    code {
        display: block;
        margin: 10px 0;
        padding: 10px;
        background-color: #272822;
        color: #f8f8f2;
        border-radius: 3px;
        overflow-x: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="upload-form">
    <h1>Upload Products CSV</h1>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="message {{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <div class="form-group">
            <label for="csv_file">Select CSV File</label>
            <input type="file" name="csv_file" id="csv_file" class="form-control" required>
        </div>
        <button type="submit" class="btn-primary">Upload CSV</button>
    </form>
    
    <div class="csv-example">
        <h3>CSV Format</h3>
        <p>The CSV file should have the following columns:</p>
        <code>name,description,image,price,stock</code>
        <p>Example:</p>
        <code>T-Shirt,Comfortable cotton t-shirt,https://example.com/images/tshirt.jpg,19.99,100</code>
        <p>Notes:</p>
        <ul>
            <li>First row should be the header row</li>
            <li>Name is required</li>
            <li>Price must be a valid number</li>
            <li>Stock must be a valid integer</li>
            <li>Image field should contain a URL to the image</li>
        </ul>
    </div>
</div>
{% endblock %} 