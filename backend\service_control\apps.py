from django.apps import AppConfig


class ServiceControlConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'service_control'
    verbose_name = 'Service Control'

    def ready(self):
        """
        Initialize the app when Django starts.

        This method is called when the app is ready. It's a good place to
        perform initialization tasks.
        """
        # Import signals to register them
        import service_control.signals
