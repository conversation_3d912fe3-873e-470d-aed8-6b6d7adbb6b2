#!/usr/bin/env python
"""
Fix Production Deployment Now
============================

This script commits and pushes the missing process_game_transaction function
to fix the production deployment error.
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        print(f"Command: {command}")
        print(f"Return code: {result.returncode}")
        if result.stdout:
            print(f"Output: {result.stdout}")
        if result.stderr:
            print(f"Error: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"Exception running command: {e}")
        return False

def main():
    """Main function to fix production deployment"""
    print("🔧 FIXING PRODUCTION DEPLOYMENT")
    print("=" * 40)
    
    # Change to the correct directory
    os.chdir(r"C:\Users\<USER>\OneDrive\Desktop\pickmetrend_gameversion")
    print(f"Working directory: {os.getcwd()}")
    
    # Add the fixed file
    print("\n1. Adding wallet/game_integration.py...")
    if run_command("git add wallet/game_integration.py"):
        print("✅ File added successfully")
    else:
        print("❌ Failed to add file")
        return
    
    # Commit the fix
    print("\n2. Committing the fix...")
    commit_message = "🔧 Add missing process_game_transaction function to fix production deployment"
    if run_command(f'git commit -m "{commit_message}"'):
        print("✅ Commit successful")
    else:
        print("❌ Failed to commit")
        return
    
    # Push to production
    print("\n3. Pushing to production...")
    if run_command("git push origin master"):
        print("✅ Push successful")
    else:
        print("❌ Failed to push")
        return
    
    print("\n🎉 PRODUCTION FIX COMPLETE!")
    print("=" * 35)
    print("✅ Added missing process_game_transaction function")
    print("✅ Committed and pushed to production")
    print("✅ Production deployment should work now")
    
    print("\n💡 What was fixed:")
    print("- Added process_game_transaction() function to wallet/game_integration.py")
    print("- Function handles standardized token rewards for all games")
    print("- Win: +5 tokens, Draw: +2 tokens, Loss: -1 token")
    print("- Color Match and Memory Card APIs will now work")
    
    print("\n🚀 Next steps:")
    print("1. Wait for Render to redeploy (2-3 minutes)")
    print("2. Test the games on your website")
    print("3. All games should work without errors!")

if __name__ == '__main__':
    main()
