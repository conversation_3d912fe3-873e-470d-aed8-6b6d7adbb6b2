"""
Game Session Service
===================

Comprehensive service for managing game sessions, tokens, and game logic
for all 5 games with exact token rules implementation.
"""

from django.contrib.auth.models import User
from django.utils import timezone
from wallet.models import Wallet
from .models import GameSession
import uuid


class GameSessionService:
    """Service for managing game sessions and token transactions"""
    
    @staticmethod
    def start_game_session(user, game_type):
        """
        Start a new game session with 2 token participation fee
        
        Args:
            user: User instance
            game_type: str - one of the 5 game types
            
        Returns:
            dict: {
                'success': bool,
                'session_id': str,
                'message': str,
                'balance': int,
                'error': str (if failed)
            }
        """
        try:
            # Check if user has enough tokens
            wallet = Wallet.objects.get(user=user)
            if wallet.balance < 2:
                return {
                    'success': False,
                    'error': 'Insufficient tokens. You need 2 tokens to play.',
                    'balance': wallet.balance
                }
            
            # Check for existing active or pending replay sessions
            existing_session = GameSession.objects.filter(
                user=user,
                game_type=game_type,
                status__in=['active', 'pending_replay']
            ).first()
            
            if existing_session:
                if existing_session.status == 'pending_replay':
                    # Resume draw game - no additional token deduction
                    existing_session.status = 'active'
                    existing_session.last_activity = timezone.now()
                    existing_session.save()
                    
                    return {
                        'success': True,
                        'session_id': str(existing_session.id),
                        'message': 'Resuming draw game - no additional tokens deducted',
                        'balance': wallet.balance,
                        'is_resume': True
                    }
                else:
                    # Active session exists
                    return {
                        'success': True,
                        'session_id': str(existing_session.id),
                        'message': 'Resuming active game session',
                        'balance': wallet.balance,
                        'is_resume': True
                    }
            
            # Deduct 2 tokens for participation
            wallet.spend_tokens(
                amount=2,
                transaction_type='game_participation',
                description=f"Participation fee for {game_type.replace('_', ' ').title()}"
            )
            
            # Create new game session
            session = GameSession.objects.create(
                user=user,
                game_type=game_type,
                participation_tokens_deducted=2,
                net_token_change=-2
            )
            
            session.start_session()
            
            return {
                'success': True,
                'session_id': str(session.id),
                'message': f'Game started! 2 tokens deducted for participation.',
                'balance': wallet.balance,
                'is_resume': False
            }
            
        except Wallet.DoesNotExist:
            return {
                'success': False,
                'error': 'Wallet not found',
                'balance': 0
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'balance': 0
            }
    
    @staticmethod
    def complete_game_session(session_id, result, game_data=None):
        """
        Complete a game session with result and handle tokens
        
        Args:
            session_id: str - UUID of the session
            result: str - 'win', 'loss', 'draw', 'forfeit'
            game_data: dict - additional game data to store
            
        Returns:
            dict: {
                'success': bool,
                'tokens_change': int,
                'new_balance': int,
                'message': str,
                'status': str,
                'error': str (if failed)
            }
        """
        try:
            session = GameSession.objects.get(id=session_id)
            
            if session.status not in ['active', 'pending_replay']:
                return {
                    'success': False,
                    'error': 'Session is not active'
                }
            
            # Complete the session
            tokens_change = session.complete_session(result, game_data)
            
            # Get updated wallet balance
            wallet = Wallet.objects.get(user=session.user)
            
            # Prepare response message
            if result == 'win':
                message = f"Congratulations! You won and earned 5 tokens. Net gain: +3 tokens."
            elif result == 'loss':
                message = f"You lost. 1 token penalty applied. Net loss: -3 tokens."
            elif result == 'draw':
                message = f"Draw! You must replay with the same participation token. No additional tokens deducted."
            elif result == 'forfeit':
                message = f"Game forfeited. Participation tokens lost. Net loss: -2 tokens."
            else:
                message = f"Game completed with result: {result}"
            
            return {
                'success': True,
                'tokens_change': tokens_change,
                'new_balance': wallet.balance,
                'message': message,
                'status': session.status,
                'result': result
            }
            
        except GameSession.DoesNotExist:
            return {
                'success': False,
                'error': 'Game session not found'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def forfeit_game_session(session_id):
        """
        Forfeit an active game session
        
        Args:
            session_id: str - UUID of the session
            
        Returns:
            dict: Result of completion with forfeit
        """
        return GameSessionService.complete_game_session(session_id, 'forfeit')
    
    @staticmethod
    def get_session_status(session_id):
        """
        Get current status of a game session
        
        Args:
            session_id: str - UUID of the session
            
        Returns:
            dict: Session information
        """
        try:
            session = GameSession.objects.get(id=session_id)
            
            return {
                'success': True,
                'session_id': str(session.id),
                'game_type': session.game_type,
                'status': session.status,
                'result': session.result,
                'tokens_deducted': session.participation_tokens_deducted,
                'tokens_awarded': session.tokens_awarded,
                'net_change': session.net_token_change,
                'created_at': session.created_at.isoformat(),
                'can_resume': session.can_resume(),
                'is_draw_replay': session.is_draw_replay(),
                'game_data': session.game_data
            }
            
        except GameSession.DoesNotExist:
            return {
                'success': False,
                'error': 'Session not found'
            }
    
    @staticmethod
    def get_user_active_sessions(user):
        """
        Get all active or pending replay sessions for a user
        
        Args:
            user: User instance
            
        Returns:
            list: List of active session data
        """
        sessions = GameSession.objects.filter(
            user=user,
            status__in=['active', 'pending_replay']
        ).order_by('-last_activity')
        
        return [
            {
                'session_id': str(session.id),
                'game_type': session.game_type,
                'status': session.status,
                'created_at': session.created_at.isoformat(),
                'last_activity': session.last_activity.isoformat(),
                'is_draw_replay': session.is_draw_replay()
            }
            for session in sessions
        ]
    
    @staticmethod
    def get_user_game_history(user, game_type=None, limit=10):
        """
        Get user's game history
        
        Args:
            user: User instance
            game_type: str - filter by game type (optional)
            limit: int - number of records to return
            
        Returns:
            list: List of completed game sessions
        """
        queryset = GameSession.objects.filter(
            user=user,
            status__in=['completed', 'incomplete']
        )
        
        if game_type:
            queryset = queryset.filter(game_type=game_type)
        
        sessions = queryset.order_by('-completed_at')[:limit]
        
        return [
            {
                'session_id': str(session.id),
                'game_type': session.game_type,
                'result': session.result,
                'tokens_change': session.net_token_change,
                'completed_at': session.completed_at.isoformat() if session.completed_at else None,
                'moves_count': session.moves_count
            }
            for session in sessions
        ]
    
    @staticmethod
    def cleanup_abandoned_sessions():
        """
        Clean up sessions that have been inactive for too long
        Mark as forfeited if inactive for more than 30 minutes
        """
        from datetime import timedelta
        
        cutoff_time = timezone.now() - timedelta(minutes=30)
        
        abandoned_sessions = GameSession.objects.filter(
            status='active',
            last_activity__lt=cutoff_time
        )
        
        count = 0
        for session in abandoned_sessions:
            session.complete_session('forfeit', {'reason': 'abandoned'})
            count += 1
        
        return count
