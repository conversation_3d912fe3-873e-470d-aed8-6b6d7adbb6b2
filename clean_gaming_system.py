#!/usr/bin/env python
"""
Clean Gaming System
===================

This script cleans up the gaming system by:
1. Removing duplicate games
2. Ensuring only working games are active
3. Fixing game configurations
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType, Battle, PlayerStats
from django.contrib.auth.models import User


def show_current_games():
    """Show all current games in the database"""
    print("🎮 Current Games in Database")
    print("=" * 50)
    
    games = GameType.objects.all().order_by('name')
    
    for i, game in enumerate(games, 1):
        print(f"\n{i}. {game.display_name}")
        print(f"   Name: {game.name}")
        print(f"   Active: {game.is_active}")
        print(f"   Description: {game.description}")
        print(f"   Rules: {game.rules}")
        print(f"   Created: {game.created_at}")
        
        # Count battles for this game
        battle_count = Battle.objects.filter(game_type=game).count()
        print(f"   Battles played: {battle_count}")
    
    print(f"\nTotal games: {games.count()}")
    return list(games)


def clean_duplicate_games():
    """Remove duplicate games and keep only the working ones"""
    print("\n🧹 Cleaning Duplicate Games")
    print("=" * 50)
    
    # Define the correct games that should exist
    correct_games = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic hand game! Choose rock, paper, or scissors and outsmart your opponent.',
            'rules': {
                'choices': ['rock', 'paper', 'scissors'],
                'win_conditions': {
                    'rock': 'scissors',
                    'paper': 'rock', 
                    'scissors': 'paper'
                }
            }
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the secret number between 1-100. Closest guess wins!',
            'rules': {
                'min_number': 1,
                'max_number': 100,
                'max_attempts': 5
            }
        },
        {
            'name': 'tic_tac_toe',
            'display_name': 'Tic Tac Toe',
            'description': 'Classic strategy game against AI. Get 3 in a row to win!',
            'rules': {
                'board_size': 3,
                'win_condition': '3_in_a_row',
                'ai_difficulty': 'hard'
            }
        }
    ]
    
    # Get all current games
    current_games = GameType.objects.all()
    
    # Find duplicates and incorrect games
    games_to_delete = []
    games_to_keep = []
    
    for game in current_games:
        # Check if this game matches any of our correct games
        matching_correct_game = None
        for correct_game in correct_games:
            if (game.name == correct_game['name'] or 
                game.display_name == correct_game['display_name']):
                matching_correct_game = correct_game
                break
        
        if matching_correct_game:
            # This is a correct game, but check if it's configured properly
            if (game.name == matching_correct_game['name'] and
                game.display_name == matching_correct_game['display_name']):
                games_to_keep.append(game)
                print(f"✅ Keeping: {game.display_name}")
            else:
                games_to_delete.append(game)
                print(f"❌ Deleting incorrect version: {game.display_name} (name: {game.name})")
        else:
            games_to_delete.append(game)
            print(f"❌ Deleting unknown game: {game.display_name} (name: {game.name})")
    
    # Delete incorrect/duplicate games
    deleted_count = 0
    for game in games_to_delete:
        print(f"🗑️ Deleting: {game.display_name}")
        game.delete()
        deleted_count += 1
    
    print(f"\n📊 Deleted {deleted_count} games")
    
    # Ensure all correct games exist
    created_count = 0
    for correct_game in correct_games:
        game_type, created = GameType.objects.get_or_create(
            name=correct_game['name'],
            defaults={
                'display_name': correct_game['display_name'],
                'description': correct_game['description'],
                'rules': correct_game['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created: {game_type.display_name}")
            created_count += 1
        else:
            # Update existing game to ensure correct configuration
            game_type.display_name = correct_game['display_name']
            game_type.description = correct_game['description']
            game_type.rules = correct_game['rules']
            game_type.is_active = True
            game_type.save()
            print(f"🔄 Updated: {game_type.display_name}")
    
    print(f"\n📊 Created {created_count} new games")
    return deleted_count, created_count


def verify_game_functionality():
    """Verify that all games are properly configured"""
    print("\n🔍 Verifying Game Functionality")
    print("=" * 50)
    
    games = GameType.objects.filter(is_active=True).order_by('name')
    
    for game in games:
        print(f"\n🎮 {game.display_name}")
        print(f"   Name: {game.name}")
        print(f"   Active: {game.is_active}")
        
        # Check rules
        if game.rules:
            print(f"   ✅ Rules configured: {list(game.rules.keys())}")
        else:
            print(f"   ❌ No rules configured")
        
        # Check if game has been played
        battle_count = Battle.objects.filter(game_type=game).count()
        if battle_count > 0:
            print(f"   ✅ Has been played: {battle_count} battles")
        else:
            print(f"   ⚠️ Never played")
    
    print(f"\nTotal active games: {games.count()}")


def show_game_statistics():
    """Show statistics about game usage"""
    print("\n📊 Game Statistics")
    print("=" * 50)
    
    total_battles = Battle.objects.count()
    total_players = User.objects.filter(gaming_stats__isnull=False).count()
    
    print(f"Total battles played: {total_battles}")
    print(f"Total players with gaming stats: {total_players}")
    
    # Game-specific stats
    games = GameType.objects.filter(is_active=True)
    for game in games:
        battle_count = Battle.objects.filter(game_type=game).count()
        print(f"{game.display_name}: {battle_count} battles")


def main():
    """Main cleanup function"""
    print("🧹 Gaming System Cleanup")
    print("=" * 50)
    
    # Show current state
    current_games = show_current_games()
    
    if len(current_games) > 3:
        print(f"\n⚠️ Found {len(current_games)} games, but should only have 3!")
        
        # Clean up duplicates
        deleted, created = clean_duplicate_games()
        
        print(f"\n📋 Cleanup Summary:")
        print(f"   Deleted: {deleted} games")
        print(f"   Created: {created} games")
    else:
        print(f"\n✅ Game count looks correct ({len(current_games)} games)")
    
    # Verify functionality
    verify_game_functionality()
    
    # Show statistics
    show_game_statistics()
    
    # Show final state
    print("\n🎯 Final Game List")
    print("=" * 30)
    final_games = GameType.objects.filter(is_active=True).order_by('name')
    for i, game in enumerate(final_games, 1):
        print(f"{i}. {game.display_name} ({game.name})")
    
    print(f"\n🎉 Gaming system cleanup complete!")
    print(f"✅ {final_games.count()} active games")
    print(f"✅ No duplicates")
    print(f"✅ All games properly configured")
    
    print(f"\n💡 Next steps:")
    print(f"1. Refresh your gaming dashboard")
    print(f"2. Test each game to ensure they work")
    print(f"3. Check that only 3 games are displayed")


if __name__ == '__main__':
    main()
