{"name": "react-confetti", "version": "6.4.0", "description": "React component to draw confetti for your party.", "main": "dist/react-confetti.cjs", "module": "dist/react-confetti.mjs", "types": "dist/react-confetti.d.cts", "exports": {"import": {"types": "./dist/react-confetti.d.mts", "default": "./dist/react-confetti.mjs"}, "require": {"types": "./dist/react-confetti.d.cts", "default": "./dist/react-confetti.cjs"}, "browser": "./dist/react-confetti.iife.js", "default": "./dist/react-confetti.umd.js"}, "repository": {"type": "git", "url": "https://github.com/alampros/react-confetti.git"}, "keywords": ["react", "confetti", "component", "react-component"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/alampros/react-confetti/issues"}, "homepage": "https://github.com/alampros/react-confetti#readme", "files": ["dist/"], "scripts": {"clean": "git clean -xfd dist/", "build": "rollup -c --configPlugin @rollup/plugin-typescript", "lint": "biome check .", "prepare": "npm run build", "develop": "npm run storybook", "storybook": "storybook dev -p 3000", "build-storybook": "storybook build", "semantic-release": "semantic-release"}, "peerDependencies": {"react": "^16.3.0 || ^17.0.1 || ^18.0.0 || ^19.0.0"}, "dependencies": {"tween-functions": "^1.2.0"}, "devDependencies": {"@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3", "@biomejs/biome": "1.9.4", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.2", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.2", "@storybook/addon-essentials": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/react-vite": "^8.4.7", "@storybook/test": "^8.4.7", "@types/react": "^19.0.2", "@types/tween-functions": "^1.2.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fps-stats": "^0.3.1", "react-use": "^17.6.0", "rollup": "^4.29.1", "rollup-plugin-dts": "^6.1.1", "semantic-release": "^24.2.0", "storybook": "^8.4.7", "typescript": "^5.7.2"}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/git", "@semantic-release/github"]}, "engines": {"node": ">=16"}}