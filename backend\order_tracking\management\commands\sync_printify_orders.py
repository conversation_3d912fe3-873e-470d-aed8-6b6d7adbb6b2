from django.core.management.base import BaseCommand
from django.utils import timezone
from order_tracking.utils import sync_orders_from_printify

class Command(BaseCommand):
    help = 'Sync orders from Printify to the OrderTracking model'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days-back',
            type=int,
            default=30,
            help='Number of days to look back for orders (default: 30)'
        )

    def handle(self, *args, **options):
        days_back = options['days_back']
        
        self.stdout.write(self.style.SUCCESS(f'Starting Printify order sync (looking back {days_back} days)...'))
        
        # Call the sync function
        result = sync_orders_from_printify(days_back=days_back)
        
        if result['success']:
            self.stdout.write(self.style.SUCCESS(result['message']))
            self.stdout.write(self.style.SUCCESS(f"Orders synced: {result['orders_synced']}"))
            self.stdout.write(self.style.SUCCESS(f"Orders updated: {result['orders_updated']}"))
        else:
            self.stdout.write(self.style.ERROR(result['message']))
            
            # Print detailed error messages if available
            if result['errors']:
                for error in result['errors']:
                    self.stdout.write(self.style.WARNING(f"Error: {error}"))
        
        self.stdout.write(self.style.SUCCESS('Printify order sync completed'))
