{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}">
  <style>
    .sync-form {
      max-width: 600px;
      margin: 20px auto;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .sync-form h1 {
      margin-top: 0;
      color: #417690;
    }
    .sync-form p {
      margin-bottom: 20px;
      line-height: 1.5;
    }
    .sync-form .form-row {
      margin-bottom: 15px;
    }
    .sync-form label {
      display: block;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .sync-form select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .sync-form .submit-row {
      margin-top: 20px;
      text-align: right;
    }
    .sync-form .submit-row input[type="submit"] {
      background-color: #28a745;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .sync-form .submit-row input[type="submit"]:hover {
      background-color: #218838;
    }
    .sync-form .cancel-link {
      display: inline-block;
      margin-right: 10px;
      color: #6c757d;
      text-decoration: none;
    }
    .sync-form .cancel-link:hover {
      text-decoration: underline;
    }
  </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
  &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
  &rsaquo; <a href="{% url 'admin:order_tracking_ordertracking_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
  &rsaquo; {% trans 'Sync Orders from Printify' %}
</div>
{% endblock %}

{% block content %}
<div class="sync-form">
  <h1>{% trans 'Sync Orders from Printify' %}</h1>
  
  <p>
    This will fetch the latest orders from Printify and update the tracking information in the database.
    Only orders that are newer than the ones already in the database will be synced.
  </p>
  
  <form method="post">
    {% csrf_token %}
    
    <div class="form-row">
      <label for="id_days_back">How far back to look for orders:</label>
      <select name="days_back" id="id_days_back">
        {% for days in days_back_options %}
          <option value="{{ days }}" {% if days == 30 %}selected{% endif %}>{{ days }} days</option>
        {% endfor %}
      </select>
      <div class="help">
        This setting is only used if there are no orders in the database yet.
      </div>
    </div>
    
    <div class="submit-row">
      <a href="{% url 'admin:order_tracking_ordertracking_changelist' %}" class="cancel-link">Cancel</a>
      <input type="submit" value="Sync Orders">
    </div>
  </form>
</div>
{% endblock %}
