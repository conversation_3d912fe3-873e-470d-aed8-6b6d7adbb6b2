#!/usr/bin/env python
"""
Emergency Fix for AI Battle Creation
====================================

This script diagnoses and fixes the "Failed to create AI battle" error
by checking every component and fixing issues step by step.
"""

import os
import django
import traceback

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, Battle
from wallet.models import Wallet
from django.test import Client
import json


def emergency_create_game_types():
    """Emergency creation of all game types"""
    print("🚨 Emergency Game Type Creation")
    print("=" * 40)
    
    # Delete all existing game types to start fresh
    GameType.objects.all().delete()
    print("🗑️ Cleared all existing game types")
    
    # Create all 5 games from scratch
    games = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic battle game',
            'rules': {'choices': ['rock', 'paper', 'scissors']}
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number game',
            'rules': {'min_number': 1, 'max_number': 100}
        },
        {
            'name': 'tic_tac_toe',
            'display_name': 'Tic Tac Toe',
            'description': 'Classic strategy game',
            'rules': {'board_size': 3}
        },
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Memory color sequence game',
            'rules': {'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange']}
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs',
            'rules': {'card_pairs': 8, 'total_cards': 16}
        }
    ]
    
    created_games = []
    for game_data in games:
        try:
            game = GameType.objects.create(
                name=game_data['name'],
                display_name=game_data['display_name'],
                description=game_data['description'],
                rules=game_data['rules'],
                is_active=True
            )
            created_games.append(game)
            print(f"✅ Created: {game.display_name} (ID: {game.id})")
        except Exception as e:
            print(f"❌ Failed to create {game_data['name']}: {e}")
    
    print(f"\n📊 Created {len(created_games)} games")
    return created_games


def test_user_setup():
    """Ensure test user has proper setup"""
    print("\n👤 Setting Up Test User")
    print("=" * 25)
    
    # Get or create test user
    test_user, created = User.objects.get_or_create(
        username='test_ai_battles',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using existing user: {test_user.username}")
    
    # Ensure user has wallet with tokens
    try:
        wallet = test_user.wallet
        if wallet.balance < 10:
            wallet.add_tokens(100, 'test', 'Test tokens for AI battles')
            print(f"✅ Added tokens, balance: {wallet.balance}")
        else:
            print(f"✅ User has {wallet.balance} tokens")
    except Wallet.DoesNotExist:
        wallet = Wallet.objects.create(user=test_user)
        wallet.add_tokens(100, 'test', 'Test tokens for AI battles')
        print(f"✅ Created wallet with {wallet.balance} tokens")
    
    return test_user


def test_direct_battle_creation():
    """Test direct battle creation without API"""
    print("\n🧪 Testing Direct Battle Creation")
    print("=" * 35)
    
    test_user = User.objects.get(username='test_ai_battles')
    
    for game_type in GameType.objects.all():
        print(f"\n🎮 Testing {game_type.display_name}:")
        
        try:
            # Test direct battle creation
            battle = Battle.objects.create(
                game_type=game_type,
                player1=test_user,
                is_ai_battle=True,
                status='waiting'
            )
            
            print(f"   ✅ Battle created: {battle.id}")
            print(f"   Game type: {battle.game_type.name}")
            print(f"   Player: {battle.player1.username}")
            print(f"   AI battle: {battle.is_ai_battle}")
            
            # Test game state initialization
            from gaming.game_logic import GameEngine
            try:
                initial_state = GameEngine.create_initial_state(game_type.name)
                battle.game_state = initial_state
                battle.save()
                print(f"   ✅ Game state initialized")
            except Exception as e:
                print(f"   ❌ Game state error: {e}")
            
            # Clean up
            battle.delete()
            print(f"   🗑️ Cleaned up")
            
        except Exception as e:
            print(f"   ❌ Battle creation failed: {e}")
            traceback.print_exc()


def test_api_endpoint_detailed():
    """Test API endpoint with detailed error reporting"""
    print("\n🌐 Testing API Endpoint (Detailed)")
    print("=" * 40)
    
    client = Client()
    test_user = User.objects.get(username='test_ai_battles')
    client.force_login(test_user)
    
    print(f"Logged in as: {test_user.username}")
    print(f"User tokens: {test_user.wallet.balance}")
    
    for game_type in GameType.objects.all():
        print(f"\n🎮 Testing API for {game_type.display_name}:")
        print(f"   Game type name: '{game_type.name}'")
        print(f"   Game type ID: {game_type.id}")
        print(f"   Is active: {game_type.is_active}")
        
        try:
            # Test the exact API call that frontend makes
            response = client.post('/api/gaming/create-ai-battle/', 
                                 data=json.dumps({'game_type': game_type.name}),
                                 content_type='application/json')
            
            print(f"   Response status: {response.status_code}")
            print(f"   Response headers: {dict(response.items())}")
            
            if response.status_code == 201:
                response_data = json.loads(response.content.decode())
                print(f"   ✅ SUCCESS!")
                print(f"   Response data: {response_data}")
                
                # Verify battle was created
                if 'battle_id' in response_data:
                    try:
                        battle = Battle.objects.get(id=response_data['battle_id'])
                        print(f"   ✅ Battle verified in database")
                        print(f"   Battle game type: {battle.game_type.name}")
                        print(f"   Battle status: {battle.status}")
                        
                        # Clean up
                        battle.delete()
                        print(f"   🗑️ Cleaned up")
                    except Battle.DoesNotExist:
                        print(f"   ❌ Battle not found in database!")
                        
            else:
                error_content = response.content.decode()
                print(f"   ❌ FAILED!")
                print(f"   Error content: {error_content}")
                
                # Try to parse error as JSON
                try:
                    error_data = json.loads(error_content)
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Raw error: {error_content}")
                    
        except Exception as e:
            print(f"   ❌ Exception during API test: {e}")
            traceback.print_exc()


def test_matchmaking_service():
    """Test the MatchmakingService directly"""
    print("\n🔄 Testing MatchmakingService")
    print("=" * 30)
    
    test_user = User.objects.get(username='test_ai_battles')
    
    try:
        from gaming.matchmaking import MatchmakingService
        import asyncio
        
        for game_type in GameType.objects.all():
            print(f"\n🎮 Testing MatchmakingService for {game_type.display_name}:")
            
            try:
                # Test async AI battle creation
                battle = asyncio.run(MatchmakingService.create_ai_battle(test_user, game_type.name))
                
                print(f"   ✅ MatchmakingService success!")
                print(f"   Battle ID: {battle.id}")
                print(f"   Game type: {battle.game_type.name}")
                
                # Clean up
                battle.delete()
                print(f"   🗑️ Cleaned up")
                
            except Exception as e:
                print(f"   ❌ MatchmakingService failed: {e}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ Could not import MatchmakingService: {e}")


def fix_permissions_and_settings():
    """Fix any permission or settings issues"""
    print("\n🔧 Fixing Permissions and Settings")
    print("=" * 35)
    
    # Check Django settings
    from django.conf import settings
    
    print("Checking Django settings:")
    print(f"   DEBUG: {getattr(settings, 'DEBUG', 'Not set')}")
    print(f"   DATABASES: {len(getattr(settings, 'DATABASES', {}))}")
    
    # Check gaming settings
    gaming_settings = getattr(settings, 'GAMING_SETTINGS', {})
    print(f"   GAMING_SETTINGS: {gaming_settings}")
    
    # Ensure all users have proper permissions
    test_user = User.objects.get(username='test_ai_battles')
    test_user.is_active = True
    test_user.save()
    print(f"✅ Ensured test user is active")


def main():
    """Main emergency fix function"""
    print("🚨 EMERGENCY AI BATTLE FIX")
    print("=" * 50)
    print("This script will completely rebuild the gaming system")
    print("and test every component to fix the AI battle issue.")
    print("=" * 50)
    
    try:
        # Step 1: Emergency game type creation
        games = emergency_create_game_types()
        
        # Step 2: Set up test user
        test_user = test_user_setup()
        
        # Step 3: Fix permissions and settings
        fix_permissions_and_settings()
        
        # Step 4: Test direct battle creation
        test_direct_battle_creation()
        
        # Step 5: Test MatchmakingService
        test_matchmaking_service()
        
        # Step 6: Test API endpoint
        test_api_endpoint_detailed()
        
        print("\n🎉 EMERGENCY FIX COMPLETE")
        print("=" * 30)
        print(f"✅ {len(games)} games created")
        print(f"✅ Test user ready: {test_user.username}")
        print("✅ All components tested")
        
        print("\n💡 Next steps:")
        print("1. Try the new games on your frontend")
        print("2. Check the detailed test results above")
        print("3. If still failing, check Django logs")
        
        # Show final status
        print(f"\n🎮 Final Game Status:")
        for game in GameType.objects.all():
            print(f"   • {game.display_name} (ID: {game.id}, Active: {game.is_active})")
            
    except Exception as e:
        print(f"\n💥 EMERGENCY FIX FAILED: {e}")
        traceback.print_exc()


if __name__ == '__main__':
    main()
