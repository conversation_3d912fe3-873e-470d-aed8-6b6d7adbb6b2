#!/bin/bash

# Build script for Render deployment
# This script changes to the backend directory and runs the build commands

echo "🔧 Starting build process..."
echo "📁 Current directory: $(pwd)"
echo "📋 Directory contents:"
ls -la

echo "🔄 Changing to backend directory..."
cd backend

echo "📁 Now in directory: $(pwd)"
echo "📋 Backend directory contents:"
ls -la

echo "🔍 Checking for requirements.txt..."
if [ -f "requirements.txt" ]; then
    echo "✅ Found requirements.txt"
    echo "📄 Requirements.txt contents:"
    cat requirements.txt
else
    echo "❌ requirements.txt not found!"
    exit 1
fi

echo "📦 Fixing pip certificate issues..."
# Set environment variables to bypass SSL verification
export PYTHONHTTPSVERIFY=0
export SSL_VERIFY=false
export CURL_CA_BUNDLE=""
export REQUESTS_CA_BUNDLE=""
export PIP_DISABLE_PIP_VERSION_CHECK=1

echo "📦 Installing requirements with certificate bypass and NO pip upgrade..."
# Use --no-deps first to avoid pip auto-upgrade, then install with deps
# Install packages with explicit certificate bypass
pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --no-cache-dir --disable-pip-version-check -r requirements.txt || \
python -c "
import subprocess
import sys
import ssl
ssl._create_default_https_context = ssl._create_unverified_context
subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt', '--no-cache-dir', '--disable-pip-version-check'])
"

echo "🗂️ Collecting static files..."
python manage.py collectstatic --noinput

echo "✅ Build completed successfully!"
