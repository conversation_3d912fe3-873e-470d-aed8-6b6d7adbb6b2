"""
Tic <PERSON> Game API
===================

API endpoints for Tic <PERSON> game with comprehensive token management
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from wallet.models import Wallet
from .game_session_service import GameSessionService
from .models import GameSession
import uuid
import json


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_tic_tac_toe_game(request):
    """
    Start a new Tic Tac Toe game and deduct 2 tokens for participation
    """
    try:
        user = request.user
        difficulty = 'hard'  # Always hard mode

        # Check if user can play games (requires 2 tokens)
        eligibility = check_game_eligibility(user.id)
        if not eligibility['can_play']:
            return Response({
                'error': eligibility['message'],
                'can_play': False,
                'balance': eligibility['balance']
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate game ID
        game_id = str(uuid.uuid4())

        # Deduct 2 tokens for participation
        participation_result = deduct_game_participation_fee(
            user_id=user.id,
            game_type='tic_tac_toe',
            game_id=game_id
        )

        if not participation_result['success']:
            return Response({
                'error': participation_result['error'],
                'can_play': False,
                'balance': participation_result['balance']
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'success': True,
            'game_id': game_id,
            'difficulty': difficulty,
            'can_play': True,
            'balance': participation_result['remaining_balance'],
            'message': 'Tic Tac Toe game started successfully (2 tokens deducted for participation)'
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_tic_tac_toe_game(request):
    """
    Complete a Tic Tac Toe game and handle token transactions
    """
    try:
        user = request.user
        game_id = request.data.get('game_id')
        game_result = request.data.get('result')  # 'win', 'loss', 'draw'
        difficulty = 'hard'  # Always hard mode

        if not game_id or not game_result:
            return Response({
                'error': 'Missing game_id or result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        if game_result not in ['win', 'loss', 'draw']:
            return Response({
                'error': 'Invalid game result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        # Process token transaction using standardized system
        transaction_result = process_game_transaction(
            user_id=user.id,
            game_type='tic_tac_toe',
            game_result=game_result,
            difficulty=difficulty,
            game_id=game_id
        )

        if not transaction_result['success']:
            return Response({
                'error': transaction_result['message'],
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'success': True,
            'tokens_earned': transaction_result['tokens_earned'],
            'new_balance': transaction_result['new_balance'],
            'balance_in_inr': transaction_result['balance_in_inr'],
            'transaction_type': transaction_result['transaction_type'],
            'description': transaction_result['description'],
            'can_play_more': transaction_result['can_play_more']
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_tic_tac_toe_stats(request):
    """
    Get user's Tic Tac Toe game statistics
    """
    try:
        user = request.user
        wallet, _ = Wallet.objects.get_or_create(user=user)
        
        # Get Tic Tac Toe related transactions
        tic_tac_toe_transactions = wallet.transactions.filter(
            description__icontains='Tic Tac Toe'
        ).order_by('-created_at')
        
        # Calculate stats
        total_games = tic_tac_toe_transactions.count()
        wins = tic_tac_toe_transactions.filter(description__icontains='win').count()
        draws = tic_tac_toe_transactions.filter(description__icontains='draw').count()
        losses = total_games - wins - draws
        
        total_tokens_earned = sum(
            transaction.amount for transaction in tic_tac_toe_transactions
            if transaction.amount > 0
        )
        
        return Response({
            'success': True,
            'stats': {
                'total_games': total_games,
                'wins': wins,
                'losses': losses,
                'draws': draws,
                'total_tokens_earned': total_tokens_earned,
                'current_balance': wallet.balance,
                'balance_in_inr': float(wallet.balance_in_inr)
            },
            'recent_games': [
                {
                    'date': transaction.created_at.isoformat(),
                    'result': 'win' if 'win' in transaction.description else 
                             'draw' if 'draw' in transaction.description else 'loss',
                    'tokens_earned': transaction.amount,
                    'description': transaction.description
                }
                for transaction in tic_tac_toe_transactions[:10]  # Last 10 games
            ]
        })
        
    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
