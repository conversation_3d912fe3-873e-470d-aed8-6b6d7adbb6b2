from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .views_debug import debug_productimage, debug_printify_api, test_sync_products, quick_sync_test, test_clean_description, sync_status

router = DefaultRouter()
router.register('categories', views.CategoryViewSet)
router.register('items', views.ProductViewSet)
router.register('images', views.ProductImageViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('upload-csv/', views.upload_csv_view, name='upload_csv'),
    path('debug/productimage/', debug_productimage, name='debug_productimage'),
    path('debug/printify/', debug_printify_api, name='debug_printify_api'),
    path('debug/sync/', test_sync_products, name='test_sync_products'),
    path('debug/quick-sync/', quick_sync_test, name='quick_sync_test'),
    path('debug/clean-description/', test_clean_description, name='test_clean_description'),
    path('debug/sync-status/', sync_status, name='sync_status'),
]