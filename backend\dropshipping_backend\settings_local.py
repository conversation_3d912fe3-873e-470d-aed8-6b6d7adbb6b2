# Local development settings
# Use this for local development to avoid PostgreSQL dependency

from .settings import *
import os

# Override database to use SQLite for local development
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Disable Redis for local development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Disable Celery for local development
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Debug settings
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# Disable HTTPS redirect for local development
SECURE_SSL_REDIRECT = False
SECURE_PROXY_SSL_HEADER = None

# Simple email backend for local development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Disable some production-only middleware
MIDDLEWARE = [item for item in MIDDLEWARE if 'SecurityMiddleware' not in item]

print("🔧 Using local development settings with SQLite database")
