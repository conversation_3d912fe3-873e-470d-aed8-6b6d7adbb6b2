import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useWallet } from '../../hooks/useWallet';
import { gameSessionService } from '../../services/gameSessionService';

interface GameState {
  targetNumber: number;
  playerGuess: number | null;
  aiGuess: number | null;
  playerScore: number;
  aiScore: number;
  round: number;
  maxRounds: number;
  gameStatus: 'waiting' | 'playing' | 'round-result' | 'finished';
  sessionId: string | null;
  roundWinner: 'player' | 'ai' | 'draw' | null;
  isResumedGame: boolean;
  guessHistory: Array<{
    round: number;
    target: number;
    playerGuess: number;
    aiGuess: number;
    winner: 'player' | 'ai' | 'draw';
  }>;
}

const ModernNumberGuessing: React.FC = () => {
  const { user } = useAuth();
  const { wallet, refreshWallet } = useWallet();
  
  const [gameState, setGameState] = useState<GameState>({
    targetNumber: 0,
    playerGuess: null,
    aiGuess: null,
    playerScore: 0,
    aiScore: 0,
    round: 1,
    maxRounds: 5,
    gameStatus: 'waiting',
    sessionId: null,
    roundWinner: null,
    guessHistory: [],
    isResumedGame: false
  });

  const [inputGuess, setInputGuess] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [gameResult, setGameResult] = useState<'win' | 'loss' | 'draw' | 'forfeit' | null>(null);
  const [tokensEarned, setTokensEarned] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Handle page unload/exit warning
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (gameState.gameStatus === 'playing' && gameState.sessionId) {
        e.preventDefault();
        e.returnValue = 'Exiting now will forfeit your participation tokens!';
        return 'Exiting now will forfeit your participation tokens!';
      }
    };

    const handleUnload = () => {
      if (gameState.gameStatus === 'playing' && gameState.sessionId) {
        // Forfeit the game session on unload
        gameSessionService.forfeitGameSession(gameState.sessionId);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [gameState.gameStatus, gameState.sessionId]);

  // Generate random number between 1-100
  const generateTargetNumber = () => Math.floor(Math.random() * 100) + 1;

  // Generate AI guess (with some strategy)
  const generateAIGuess = () => {
    // AI uses a simple strategy: random with slight bias towards middle numbers
    const random = Math.random();
    if (random < 0.3) {
      // 30% chance for middle range (40-60)
      return Math.floor(Math.random() * 21) + 40;
    } else if (random < 0.6) {
      // 30% chance for lower-middle (20-40) or upper-middle (60-80)
      return Math.random() < 0.5 
        ? Math.floor(Math.random() * 21) + 20
        : Math.floor(Math.random() * 21) + 60;
    } else {
      // 40% chance for any number
      return Math.floor(Math.random() * 100) + 1;
    }
  };

  // Start new game
  const startGame = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await gameSessionService.startGameSession('number_guessing');

      if (result.success) {
        const targetNumber = generateTargetNumber();

        setGameState({
          targetNumber,
          playerGuess: null,
          aiGuess: null,
          playerScore: 0,
          aiScore: 0,
          round: 1,
          maxRounds: 5,
          gameStatus: 'playing',
          sessionId: result.session_id,
          roundWinner: null,
          guessHistory: [],
          isResumedGame: result.is_resume
        });

        setInputGuess('');
        setShowResult(false);

        // Refresh wallet to show updated balance
        await refreshWallet();

        // Show message about token deduction or resume
        if (result.is_resume) {
          console.log('Resumed game:', result.message);
        } else {
          console.log('New game started:', result.message);
        }
      } else {
        alert(result.error || 'Failed to start game');
      }
    } catch (error) {
      console.error('Error starting game:', error);
      alert('Failed to start game');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle player guess
  const handleGuess = () => {
    const guess = parseInt(inputGuess);
    
    if (isNaN(guess) || guess < 1 || guess > 100) {
      alert('Please enter a number between 1 and 100');
      return;
    }

    const aiGuess = generateAIGuess();
    const playerDistance = Math.abs(gameState.targetNumber - guess);
    const aiDistance = Math.abs(gameState.targetNumber - aiGuess);
    
    let roundWinner: 'player' | 'ai' | 'draw';
    if (playerDistance < aiDistance) {
      roundWinner = 'player';
    } else if (aiDistance < playerDistance) {
      roundWinner = 'ai';
    } else {
      roundWinner = 'draw';
    }

    const newHistory = [...gameState.guessHistory, {
      round: gameState.round,
      target: gameState.targetNumber,
      playerGuess: guess,
      aiGuess: aiGuess,
      winner: roundWinner
    }];

    setGameState(prev => ({
      ...prev,
      playerGuess: guess,
      aiGuess: aiGuess,
      roundWinner,
      gameStatus: 'round-result',
      playerScore: prev.playerScore + (roundWinner === 'player' ? 1 : 0),
      aiScore: prev.aiScore + (roundWinner === 'ai' ? 1 : 0),
      guessHistory: newHistory
    }));

    // Show result for 3 seconds, then next round or finish
    setTimeout(() => {
      if (gameState.round >= gameState.maxRounds) {
        finishGame();
      } else {
        const newTarget = generateTargetNumber();
        setGameState(prev => ({
          ...prev,
          round: prev.round + 1,
          targetNumber: newTarget,
          gameStatus: 'playing',
          playerGuess: null,
          aiGuess: null,
          roundWinner: null
        }));
        setInputGuess('');
      }
    }, 3000);
  };

  // Finish game
  const finishGame = async () => {
    if (!gameState.sessionId) return;

    let finalResult: 'win' | 'loss' | 'draw';

    if (gameState.playerScore > gameState.aiScore) {
      finalResult = 'win';
    } else if (gameState.playerScore < gameState.aiScore) {
      finalResult = 'loss';
    } else {
      finalResult = 'draw';
    }

    try {
      // Prepare game data
      const gameData = {
        rounds: gameState.guessHistory,
        final_score: {
          player: gameState.playerScore,
          ai: gameState.aiScore
        },
        max_rounds: gameState.maxRounds,
        total_rounds: gameState.round - 1
      };

      const submitResult = await gameSessionService.completeGameSession(
        gameState.sessionId,
        finalResult,
        gameData
      );

      if (submitResult.success) {
        setTokensEarned(submitResult.tokens_earned);
        setGameResult(finalResult);
        await refreshWallet();

        // Handle draw case
        if (finalResult === 'draw') {
          gameSessionService.handleDrawContinuation(
            () => {
              // Continue playing - start new round with same session
              const newTarget = generateTargetNumber();
              setGameState(prev => ({
                ...prev,
                targetNumber: newTarget,
                playerGuess: null,
                aiGuess: null,
                playerScore: 0,
                aiScore: 0,
                round: 1,
                gameStatus: 'playing',
                roundWinner: null,
                guessHistory: []
              }));
              setInputGuess('');
              setShowResult(false);
              return;
            },
            () => {
              // Forfeit the draw game
              gameSessionService.forfeitGameSession(gameState.sessionId!);
              setShowResult(true);
            }
          );
          return; // Don't show result immediately for draws
        }
      } else {
        console.error('Error completing game:', submitResult.error);
      }
    } catch (error) {
      console.error('Error completing game:', error);
    }

    setGameState(prev => ({ ...prev, gameStatus: 'finished' }));
    setShowResult(true);
  };

  // Forfeit game
  const forfeitGame = () => {
    if (!gameState.sessionId) return;

    gameSessionService.showExitWarning(
      async () => {
        // User confirmed forfeit
        try {
          const result = await gameSessionService.forfeitGameSession(gameState.sessionId!);
          if (result.success) {
            setTokensEarned(result.tokens_earned);
            setGameResult('forfeit');
            await refreshWallet();
            setShowResult(true);
          }
        } catch (error) {
          console.error('Error forfeiting game:', error);
        }
      },
      () => {
        // User cancelled forfeit - continue playing
        console.log('Forfeit cancelled');
      }
    );
  };

  if (showResult) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-600 via-teal-600 to-blue-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h2 className="text-3xl font-bold mb-6">🎯 Game Complete!</h2>
          
          <div className="space-y-4 mb-6">
            <div className="flex justify-between text-lg">
              <span>Your Score:</span>
              <span className="font-bold">{gameState.playerScore}</span>
            </div>
            <div className="flex justify-between text-lg">
              <span>AI Score:</span>
              <span className="font-bold">{gameState.aiScore}</span>
            </div>
          </div>

          <div className="mb-6">
            {gameResult === 'win' && (
              <div className="text-green-400">
                <div className="text-6xl mb-4">🎉</div>
                <div className="text-2xl font-bold">You Won!</div>
                <div className="text-lg">+{tokensEarned} tokens (Net: +{tokensEarned > 0 ? tokensEarned - 2 : tokensEarned} tokens)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens, Win bonus: +5 tokens</div>
              </div>
            )}
            {gameResult === 'loss' && (
              <div className="text-red-400">
                <div className="text-6xl mb-4">😔</div>
                <div className="text-2xl font-bold">You Lost</div>
                <div className="text-lg">{tokensEarned} tokens (Net: {tokensEarned - 2} tokens)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens, Loss penalty: -1 token</div>
              </div>
            )}
            {gameResult === 'draw' && (
              <div className="text-yellow-400">
                <div className="text-6xl mb-4">🤝</div>
                <div className="text-2xl font-bold">Draw!</div>
                <div className="text-lg">Must replay (No token change)</div>
                <div className="text-sm opacity-75 mt-2">Participation tokens held until win/loss</div>
              </div>
            )}
            {gameResult === 'forfeit' && (
              <div className="text-orange-400">
                <div className="text-6xl mb-4">🚪</div>
                <div className="text-2xl font-bold">Game Forfeited</div>
                <div className="text-lg">{tokensEarned} tokens (Participation fee lost)</div>
                <div className="text-sm opacity-75 mt-2">Participation fee: -2 tokens</div>
              </div>
            )}
          </div>

          <div className="mb-6">
            <div className="text-sm opacity-75">Current Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-green-600 hover:to-blue-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Play Again'}
          </button>
        </div>
      </div>
    );
  }

  if (gameState.gameStatus === 'waiting') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-600 via-teal-600 to-blue-600 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 max-w-md w-full text-center text-white">
          <h1 className="text-4xl font-bold mb-4">🎯 Number Guessing</h1>
          <p className="text-lg mb-4 opacity-90">
            Guess the number between 1-100! Closest guess wins the round. Best of 5 rounds!
          </p>

          <div className="mb-4">
            <div className="px-3 py-2 bg-yellow-50/20 border border-yellow-200/30 rounded-lg inline-block">
              <span className="text-sm font-medium text-yellow-200">🟡 Medium Difficulty</span>
            </div>
          </div>
          
          <div className="mb-6">
            <div className="text-sm opacity-75">Your Balance</div>
            <div className="text-xl font-bold">{wallet?.balance || 0} tokens</div>
          </div>

          <div className="mb-6 text-sm opacity-75">
            <div>Win: +5 tokens</div>
            <div>Draw: +2 tokens</div>
            <div>Lose: -1 token</div>
          </div>

          <button
            onClick={startGame}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-3 px-6 rounded-xl font-bold text-lg hover:from-green-600 hover:to-blue-600 transition-all duration-200 disabled:opacity-50"
          >
            {isLoading ? 'Starting...' : 'Start Game'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-600 via-teal-600 to-blue-600 p-4">
      <div className="max-w-2xl mx-auto">
        {/* Game Header */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mb-6 text-white">
          <div className="flex justify-between items-center mb-4">
            <div className="text-center">
              <div className="text-sm opacity-75">Your Score</div>
              <div className="text-2xl font-bold">{gameState.playerScore}</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold">Round {gameState.round}/{gameState.maxRounds}</div>
              <div className="text-sm opacity-75">Best of {gameState.maxRounds}</div>
            </div>
            <div className="text-center">
              <div className="text-sm opacity-75">AI Score</div>
              <div className="text-2xl font-bold">{gameState.aiScore}</div>
            </div>
          </div>
        </div>

        {/* Round Result */}
        {gameState.gameStatus === 'round-result' && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 mb-6 text-center text-white">
            <div className="text-2xl font-bold mb-4">🎯 Target Number: {gameState.targetNumber}</div>
            
            <div className="grid grid-cols-2 gap-6 mb-6">
              <div className="text-center">
                <div className="text-lg font-bold mb-2">Your Guess</div>
                <div className="text-4xl font-bold text-blue-300">{gameState.playerGuess}</div>
                <div className="text-sm opacity-75">
                  Distance: {gameState.playerGuess ? Math.abs(gameState.targetNumber - gameState.playerGuess) : 0}
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold mb-2">AI Guess</div>
                <div className="text-4xl font-bold text-pink-300">{gameState.aiGuess}</div>
                <div className="text-sm opacity-75">
                  Distance: {gameState.aiGuess ? Math.abs(gameState.targetNumber - gameState.aiGuess) : 0}
                </div>
              </div>
            </div>
            
            <div className="text-2xl font-bold">
              {gameState.roundWinner === 'player' && <span className="text-green-400">You Win This Round! 🎉</span>}
              {gameState.roundWinner === 'ai' && <span className="text-red-400">AI Wins This Round! 😔</span>}
              {gameState.roundWinner === 'draw' && <span className="text-yellow-400">Round Draw! 🤝</span>}
            </div>
          </div>
        )}

        {/* Guessing Interface */}
        {gameState.gameStatus === 'playing' && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold text-white mb-6">
              Guess the number between 1-100!
            </h3>
            
            <div className="max-w-xs mx-auto mb-6">
              <input
                type="number"
                min="1"
                max="100"
                value={inputGuess}
                onChange={(e) => setInputGuess(e.target.value)}
                placeholder="Enter your guess..."
                className="w-full px-4 py-3 text-xl text-center bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none focus:border-white/60"
                onKeyPress={(e) => e.key === 'Enter' && handleGuess()}
              />
            </div>
            
            <button
              onClick={handleGuess}
              disabled={!inputGuess || isLoading}
              className="bg-white/20 backdrop-blur-sm border-2 border-white/30 rounded-xl px-8 py-3 text-white font-bold text-lg hover:bg-white/30 hover:border-white/50 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Submit Guess
            </button>
            
            <div className="mt-6 text-white opacity-75">
              <div className="text-sm">Closest guess to the target number wins the round!</div>
            </div>

            {/* Game Controls */}
            <div className="mt-6 flex justify-center space-x-4">
              <button
                onClick={forfeitGame}
                className="px-4 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-200"
              >
                ⚠️ Forfeit Game
              </button>
            </div>

            {/* Token Information */}
            <div className="mt-4 text-center text-sm opacity-75 text-white">
              <div>Current Balance: {wallet?.balance || 0} tokens</div>
              {gameState.isResumedGame && (
                <div className="text-yellow-300 mt-1">📍 Resumed Game - No additional tokens deducted</div>
              )}
            </div>
          </div>
        )}

        {/* Game History */}
        {gameState.guessHistory.length > 0 && (
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mt-6">
            <h4 className="text-lg font-bold text-white mb-4">Round History</h4>
            <div className="space-y-2">
              {gameState.guessHistory.map((history, index) => (
                <div key={index} className="flex justify-between items-center text-white text-sm bg-white/10 rounded-lg p-3">
                  <span>Round {history.round}</span>
                  <span>Target: {history.target}</span>
                  <span>You: {history.playerGuess}</span>
                  <span>AI: {history.aiGuess}</span>
                  <span className={
                    history.winner === 'player' ? 'text-green-400' :
                    history.winner === 'ai' ? 'text-red-400' : 'text-yellow-400'
                  }>
                    {history.winner === 'player' ? '🎉' : history.winner === 'ai' ? '😔' : '🤝'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernNumberGuessing;
