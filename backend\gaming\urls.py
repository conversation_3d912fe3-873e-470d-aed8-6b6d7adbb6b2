from django.urls import path
from . import views
from . import color_match_api
from . import memory_card_api
from . import session_api
from . import new_tic_tac_toe_api
from . import rock_paper_scissors_api
from . import number_guessing_api
from . import ludo_api
from . import spin_wheel_api

urlpatterns = [
    # Game types
    path('game-types/', views.GameTypeListView.as_view(), name='game-types'),
    
    # Battles
    path('battles/', views.BattleListView.as_view(), name='battle-list'),
    path('battles/<uuid:pk>/', views.BattleDetailView.as_view(), name='battle-detail'),
    path('battles/<uuid:battle_id>/start/', views.start_battle, name='start-battle'),
    path('battles/<uuid:battle_id>/move/', views.make_move, name='make-move'),
    path('battles/recent/', views.recent_battles, name='recent-battles'),
    path('create-battle/', views.create_battle, name='create-battle'),
    path('create-ai-battle/', views.create_ai_battle, name='create-ai-battle'),
    
    # Matchmaking
    path('matchmaking/status/', views.matchmaking_status, name='matchmaking-status'),
    path('matchmaking/cancel/', views.cancel_matchmaking, name='cancel-matchmaking'),
    
    # Stats
    path('stats/', views.PlayerStatsView.as_view(), name='player-stats'),
    path('stats/global/', views.gaming_stats, name='gaming-stats'),

    # Game Session Management API
    path('session/start/', session_api.start_game_session, name='session-start'),
    path('session/complete/', session_api.complete_game_session, name='session-complete'),
    path('session/forfeit/', session_api.forfeit_game_session, name='session-forfeit'),
    path('session/<str:session_id>/', session_api.get_session_status, name='session-status'),
    path('session/active/', session_api.get_active_sessions, name='session-active'),
    path('session/history/', session_api.get_game_history, name='session-history'),
    path('session/stats/', session_api.get_user_stats, name='session-stats'),
    path('session/cleanup/', session_api.cleanup_abandoned_sessions, name='session-cleanup'),

    # Tic Tac Toe API endpoints
    path('tic-tac-toe/start/', new_tic_tac_toe_api.start_tic_tac_toe_game, name='tic-tac-toe-start'),
    path('tic-tac-toe/complete/', new_tic_tac_toe_api.complete_tic_tac_toe_game, name='tic-tac-toe-complete'),
    path('tic-tac-toe/forfeit/', new_tic_tac_toe_api.forfeit_tic_tac_toe_game, name='tic-tac-toe-forfeit'),
    path('tic-tac-toe/stats/', new_tic_tac_toe_api.get_tic_tac_toe_stats, name='tic-tac-toe-stats'),
    path('tic-tac-toe/session/<str:game_id>/', new_tic_tac_toe_api.get_tic_tac_toe_session, name='tic-tac-toe-session'),

    # Rock Paper Scissors API endpoints
    path('rock-paper-scissors/start/', rock_paper_scissors_api.start_rock_paper_scissors_game, name='rps-start'),
    path('rock-paper-scissors/complete/', rock_paper_scissors_api.complete_rock_paper_scissors_game, name='rps-complete'),
    path('rock-paper-scissors/forfeit/', rock_paper_scissors_api.forfeit_rock_paper_scissors_game, name='rps-forfeit'),
    path('rock-paper-scissors/stats/', rock_paper_scissors_api.get_rock_paper_scissors_stats, name='rps-stats'),
    path('rock-paper-scissors/session/<str:game_id>/', rock_paper_scissors_api.get_rock_paper_scissors_session, name='rps-session'),

    # Number Guessing API endpoints
    path('number-guessing/start/', number_guessing_api.start_number_guessing_game, name='number-guessing-start'),
    path('number-guessing/complete/', number_guessing_api.complete_number_guessing_game, name='number-guessing-complete'),
    path('number-guessing/forfeit/', number_guessing_api.forfeit_number_guessing_game, name='number-guessing-forfeit'),
    path('number-guessing/stats/', number_guessing_api.get_number_guessing_stats, name='number-guessing-stats'),
    path('number-guessing/session/<str:game_id>/', number_guessing_api.get_number_guessing_session, name='number-guessing-session'),

    # Color Match API endpoints
    path('color-match/start/', color_match_api.start_color_match_game, name='color-match-start'),
    path('color-match/complete/', color_match_api.complete_color_match_game, name='color-match-complete'),
    path('color-match/forfeit/', color_match_api.forfeit_color_match_game, name='color-match-forfeit'),
    path('color-match/stats/', color_match_api.get_color_match_stats, name='color-match-stats'),
    path('color-match/session/<str:game_id>/', color_match_api.get_color_match_session, name='color-match-session'),

    # Memory Card API endpoints
    path('memory-card/start/', memory_card_api.start_memory_card_game, name='memory-card-start'),
    path('memory-card/complete/', memory_card_api.complete_memory_card_game, name='memory-card-complete'),
    path('memory-card/forfeit/', memory_card_api.forfeit_memory_card_game, name='memory-card-forfeit'),
    path('memory-card/stats/', memory_card_api.get_memory_card_stats, name='memory-card-stats'),
    path('memory-card/session/<str:game_id>/', memory_card_api.get_memory_card_session, name='memory-card-session'),

    # Ludo API endpoints
    path('ludo/start/', ludo_api.start_ludo_game, name='ludo-start'),
    path('ludo/roll-dice/', ludo_api.roll_dice_ludo, name='ludo-roll-dice'),
    path('ludo/make-move/', ludo_api.make_move_ludo, name='ludo-make-move'),
    path('ludo/valid-moves/', ludo_api.get_valid_moves_ludo, name='ludo-valid-moves'),
    path('ludo/bot-move/', ludo_api.bot_move_ludo, name='ludo-bot-move'),
    path('ludo/complete/', ludo_api.complete_ludo_game, name='ludo-complete'),
    path('ludo/forfeit/', ludo_api.forfeit_ludo_game, name='ludo-forfeit'),

    # Spin Wheel API endpoints
    path('spin-wheel/status/', spin_wheel_api.spin_wheel_status, name='spin-wheel-status'),
    path('spin-wheel/spin/', spin_wheel_api.spin_wheel, name='spin-wheel-spin'),
    path('spin-wheel/reveal-scratch/', spin_wheel_api.reveal_scratch_card, name='spin-wheel-reveal-scratch'),
    path('spin-wheel/history/', spin_wheel_api.spin_history, name='spin-wheel-history'),
]
