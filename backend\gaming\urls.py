from django.urls import path
from . import views
from . import tic_tac_toe_api

urlpatterns = [
    # Game types
    path('game-types/', views.GameTypeListView.as_view(), name='game-types'),
    
    # Battles
    path('battles/', views.BattleListView.as_view(), name='battle-list'),
    path('battles/<uuid:pk>/', views.BattleDetailView.as_view(), name='battle-detail'),
    path('battles/<uuid:battle_id>/start/', views.start_battle, name='start-battle'),
    path('battles/<uuid:battle_id>/move/', views.make_move, name='make-move'),
    path('battles/recent/', views.recent_battles, name='recent-battles'),
    path('create-battle/', views.create_battle, name='create-battle'),
    path('create-ai-battle/', views.create_ai_battle, name='create-ai-battle'),
    
    # Matchmaking
    path('matchmaking/status/', views.matchmaking_status, name='matchmaking-status'),
    path('matchmaking/cancel/', views.cancel_matchmaking, name='cancel-matchmaking'),
    
    # Stats
    path('stats/', views.PlayerStatsView.as_view(), name='player-stats'),
    path('stats/global/', views.gaming_stats, name='gaming-stats'),

    # Tic Tac Toe API endpoints
    path('tic-tac-toe/start/', tic_tac_toe_api.start_tic_tac_toe_game, name='tic-tac-toe-start'),
    path('tic-tac-toe/complete/', tic_tac_toe_api.complete_tic_tac_toe_game, name='tic-tac-toe-complete'),
    path('tic-tac-toe/stats/', tic_tac_toe_api.get_tic_tac_toe_stats, name='tic-tac-toe-stats'),
]
