from django.contrib import admin
from .models import GameType, Battle, GameMove, PlayerStats


@admin.register(GameType)
class GameTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'display_name']
    readonly_fields = ['created_at']


@admin.register(Battle)
class BattleAdmin(admin.ModelAdmin):
    list_display = ['id', 'game_type', 'player1', 'player2', 'is_ai_battle', 'status', 'result', 'created_at']
    list_filter = ['status', 'result', 'is_ai_battle', 'game_type', 'created_at']
    search_fields = ['player1__username', 'player2__username']
    readonly_fields = ['id', 'created_at', 'started_at', 'completed_at', 'duration']
    
    fieldsets = (
        ('Battle Information', {
            'fields': ('id', 'game_type', 'player1', 'player2', 'is_ai_battle')
        }),
        ('Status', {
            'fields': ('status', 'result')
        }),
        ('Game Data', {
            'fields': ('game_state', 'moves_history'),
            'classes': ('collapse',)
        }),
        ('Rewards', {
            'fields': ('tokens_awarded', 'winner_tokens', 'loser_tokens')
        }),
        ('Timing', {
            'fields': ('created_at', 'started_at', 'completed_at', 'duration')
        }),
    )


@admin.register(GameMove)
class GameMoveAdmin(admin.ModelAdmin):
    list_display = ['battle', 'player', 'move_number', 'timestamp']
    list_filter = ['timestamp', 'battle__game_type']
    search_fields = ['battle__id', 'player__username']
    readonly_fields = ['id', 'timestamp']


@admin.register(PlayerStats)
class PlayerStatsAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_battles', 'battles_won', 'win_rate', 'total_tokens_earned', 'current_win_streak']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created_at', 'updated_at', 'win_rate']
    
    fieldsets = (
        ('Player', {
            'fields': ('user',)
        }),
        ('Battle Statistics', {
            'fields': ('total_battles', 'battles_won', 'battles_lost', 'battles_drawn', 'win_rate')
        }),
        ('Tokens & Streaks', {
            'fields': ('total_tokens_earned', 'current_win_streak', 'best_win_streak')
        }),
        ('Time', {
            'fields': ('total_play_time', 'created_at', 'updated_at')
        }),
    )
