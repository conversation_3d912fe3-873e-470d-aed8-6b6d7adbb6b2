import re
from bs4 import BeautifulSoup
import logging

logger = logging.getLogger(__name__)

def clean_description(description):
    """
    Clean product description by removing unwanted HTML tags and trimming extra spaces.

    Args:
        description (str): The product description to clean

    Returns:
        str: The cleaned description
    """
    if not description:
        return ""

    try:
        # Parse the HTML
        soup = BeautifulSoup(description, 'html.parser')

        # Remove <br/> tags and replace with newlines
        for br in soup.find_all('br'):
            br.replace_with('\n')

        # Get the text content
        text = soup.get_text()

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove leading/trailing whitespace
        text = text.strip()

        # Replace multiple newlines with double newlines for better formatting
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # Clean up common issues in product descriptions
        # Remove excessive punctuation
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        text = re.sub(r'[.]{3,}', '...', text)

        # Fix spacing around punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s*([A-Z])', r'\1 \2', text)

        # Remove common promotional text patterns
        promotional_patterns = [
            r'(?i)limited time offer[!.]*',
            r'(?i)act now[!.]*',
            r'(?i)order today[!.]*',
            r'(?i)while supplies last[!.]*',
            r'(?i)don\'t miss out[!.]*'
        ]

        for pattern in promotional_patterns:
            text = re.sub(pattern, '', text)

        # Clean up any double spaces created by removals
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        return text
    except Exception as e:
        logger.error(f"Error cleaning description: {str(e)}")
        return description


def clean_description_with_ai(description, api_key=None):
    """
    Clean product description using AI (OpenAI).
    This is a placeholder for future enhancement.

    Args:
        description (str): The product description to clean
        api_key (str, optional): OpenAI API key

    Returns:
        str: The cleaned description
    """
    # First apply basic cleaning
    cleaned_text = clean_description(description)

    # If no API key is provided, return the basic cleaned text
    if not api_key:
        logger.warning("No API key provided for AI cleaning, using basic cleaning only")
        return cleaned_text

    try:
        # This is a placeholder for future OpenAI integration
        # Example implementation:
        """
        import openai

        openai.api_key = api_key

        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that cleans product descriptions."},
                {"role": "user", "content": f"Clean this product description, improve grammar and readability, but keep all important information: {cleaned_text}"}
            ]
        )

        return response.choices[0].message.content
        """

        # For now, just return the basic cleaned text
        return cleaned_text
    except Exception as e:
        logger.error(f"Error cleaning description with AI: {str(e)}")
        return cleaned_text
