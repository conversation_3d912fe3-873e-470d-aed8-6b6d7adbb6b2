@echo off
echo 🔍 PRODUCTION DIAGNOSIS AND FIX
echo ===============================
echo.
echo This will:
echo   1. Check authentication system
echo   2. Analyze production database
echo   3. Fix all user wallets and bonuses
echo   4. Test games functionality
echo   5. Verify token flow
echo.

echo 🔧 Step 1: Quick Auth Check
echo ============================
python check_auth_system.py

echo.
echo 🔍 Step 2: Complete Production Diagnosis
echo ========================================
python production_diagnosis.py

echo.
echo ✅ Diagnosis completed!
echo.
echo 📋 Next steps:
echo   - Check the output above for any issues
echo   - Test registration on your production site
echo   - Verify all users have 100 tokens
echo   - Test games and token earning
echo.
pause
