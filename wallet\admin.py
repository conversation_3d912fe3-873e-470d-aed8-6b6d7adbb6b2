from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Wallet, WalletTransaction, TokenRequest


@admin.register(Wallet)
class WalletAdmin(admin.ModelAdmin):
    list_display = ['user', 'balance', 'balance_in_inr', 'total_earned', 'total_spent', 'created_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['id', 'created_at', 'updated_at', 'balance_in_inr']
    
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'id')
        }),
        ('Balance Information', {
            'fields': ('balance', 'balance_in_inr', 'total_earned', 'total_spent')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )


@admin.register(WalletTransaction)
class WalletTransactionAdmin(admin.ModelAdmin):
    list_display = ['wallet_user', 'transaction_type', 'amount', 'balance_after', 'created_at']
    list_filter = ['transaction_type', 'created_at']
    search_fields = ['wallet__user__username', 'wallet__user__email', 'description']
    readonly_fields = ['id', 'created_at']
    
    def wallet_user(self, obj):
        return obj.wallet.user.username
    wallet_user.short_description = 'User'
    
    fieldsets = (
        ('Transaction Information', {
            'fields': ('wallet', 'transaction_type', 'amount', 'description')
        }),
        ('References', {
            'fields': ('game_id', 'order_id')
        }),
        ('Balance Information', {
            'fields': ('balance_after',)
        }),
        ('Timestamps', {
            'fields': ('created_at',)
        }),
    )


@admin.register(TokenRequest)
class TokenRequestAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'status', 'user_balance', 'tokens_granted',
        'requested_at', 'processed_by', 'quick_actions'
    ]
    list_filter = ['status', 'requested_at', 'processed_at']
    search_fields = ['user__username', 'user__email', 'message', 'admin_notes']
    readonly_fields = ['id', 'requested_at', 'processed_at']

    fieldsets = (
        ('Request Information', {
            'fields': ('user', 'message', 'status', 'id')
        }),
        ('Processing Information', {
            'fields': ('processed_by', 'tokens_granted', 'admin_notes')
        }),
        ('Timestamps', {
            'fields': ('requested_at', 'processed_at')
        }),
    )

    actions = ['approve_selected_requests', 'reject_selected_requests']

    def user_balance(self, obj):
        """Display user's current token balance"""
        if hasattr(obj.user, 'wallet'):
            balance = obj.user.wallet.balance
            if balance == 0:
                return format_html('<span style="color: red; font-weight: bold;">0 tokens</span>')
            return f"{balance} tokens"
        return "No wallet"
    user_balance.short_description = "Current Balance"

    def quick_actions(self, obj):
        """Quick action buttons for pending requests"""
        if obj.status == 'pending':
            approve_url = reverse('admin:approve_token_request', args=[obj.id])
            reject_url = reverse('admin:reject_token_request', args=[obj.id])
            return format_html(
                '<a class="button" href="{}">Approve (50 tokens)</a> '
                '<a class="button" href="{}">Reject</a>',
                approve_url, reject_url
            )
        return format_html('<span style="color: gray;">Processed</span>')
    quick_actions.short_description = "Actions"

    def approve_selected_requests(self, request, queryset):
        """Bulk approve selected token requests"""
        approved_count = 0
        for token_request in queryset.filter(status='pending'):
            try:
                token_request.approve(
                    admin_user=request.user,
                    tokens_to_grant=50,
                    admin_notes="Bulk approved via admin"
                )
                approved_count += 1
            except Exception as e:
                self.message_user(request, f"Error approving request {token_request.id}: {str(e)}")

        self.message_user(request, f"Successfully approved {approved_count} token requests.")
    approve_selected_requests.short_description = "Approve selected requests (50 tokens each)"

    def reject_selected_requests(self, request, queryset):
        """Bulk reject selected token requests"""
        rejected_count = 0
        for token_request in queryset.filter(status='pending'):
            try:
                token_request.reject(
                    admin_user=request.user,
                    admin_notes="Bulk rejected via admin"
                )
                rejected_count += 1
            except Exception as e:
                self.message_user(request, f"Error rejecting request {token_request.id}: {str(e)}")

        self.message_user(request, f"Successfully rejected {rejected_count} token requests.")
    reject_selected_requests.short_description = "Reject selected requests"
