# PickMeTrend E-commerce Platform

An e-commerce platform for PickMeTrend custom t-shirts with Printify integration.

## Features

- Product management with Printify integration
- Shopping cart and checkout functionality
- Order tracking and management
- Customer communication system
- Razorpay payment integration
- Team members and site settings management

## API Keys and Sensitive Information

This repository contains references to API keys and sensitive information in documentation files. These are included for development and deployment guidance but should be handled securely:

1. **SendGrid API Key**: `*********************************************************************`
2. **Render API Key**: `rnd_CEBAcorKkMTqzbOI2OHgH1tUUI5k`

For security, these keys are stored in a `.env` file which is excluded from version control. When deploying or setting up a new development environment, make sure to:

1. Create a `.env` file in the project root
2. Add the necessary API keys to the `.env` file
3. Use environment variables in production environments

## Deployment

### Render Deployment
This project can be deployed using Render. See the `RENDER_DEPLOYMENT_GUIDE.md` file for detailed instructions.

### Railway Deployment

1. **Create a Railway Account**
   - Sign up at https://railway.app/

2. **Create a New Project**
   - From the Railway dashboard, click "New Project"
   - Select "Deploy from GitHub repo"
   - Connect your GitHub account and select the repository

3. **Add PostgreSQL and Redis Services**
   - Click "New Service" > "Database" > "PostgreSQL"
   - Click "New Service" > "Database" > "Redis"

4. **Configure Environment Variables**
   - Go to your project's "Variables" tab
   - Add the variables listed in `.env.example`
   - Railway automatically adds `DATABASE_URL` and `REDIS_URL` variables

5. **Deploy the Application**
   - Railway will automatically deploy your application
   - You can trigger manual deployments from the "Deployments" tab

6. **Run Migrations**
   - Go to the "Settings" tab
   - Click "Generate Command"
   - Run: `python manage.py migrate`
   - Run: `python manage.py createsuperuser`

7. **Access Your Application**
   - Go to the "Settings" tab
   - Find the "Domains" section
   - Click on the generated domain to access your application

## Development Setup

1. Clone the repository:
   ```
   git clone https://github.com/phinihas30/pickmetrend.git
   cd pickmetrend
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file with your API keys:
   ```
   SENDGRID_API_KEY=your_sendgrid_api_key
   RENDER_API_KEY=your_render_api_key
   ```

5. Run migrations:
   ```
   python manage.py migrate
   ```

6. Start the development server:
   ```
   python manage.py runserver
   ```

## Documentation

- `RENDER_DEPLOYMENT_GUIDE.md`: Instructions for deploying to Render
- `SENDGRID_SETUP.md`: Guide for setting up SendGrid email integration
- Various other documentation files for specific features

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
