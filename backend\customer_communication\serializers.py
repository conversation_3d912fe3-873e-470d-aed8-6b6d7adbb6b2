from rest_framework import serializers
from .models import SupportTicket, TicketResponse, CustomerFeedback


class SupportTicketSerializer(serializers.ModelSerializer):
    """
    Serializer for the SupportTicket model.
    """
    class Meta:
        model = SupportTicket
        fields = [
            'id', 'name', 'email', 'subject', 'message', 
            'status', 'priority', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'status', 'priority', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Get the user from the request if authenticated
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
            
            # If the user has a recent order, associate it with the ticket
            order = request.user.orders.order_by('-created_at').first()
            if order:
                validated_data['order'] = order
        
        return super().create(validated_data)


class TicketResponseSerializer(serializers.ModelSerializer):
    """
    Serializer for the TicketResponse model.
    """
    class Meta:
        model = TicketResponse
        fields = ['id', 'ticket', 'message', 'is_staff', 'created_at']
        read_only_fields = ['id', 'is_staff', 'created_at']
    
    def create(self, validated_data):
        # Get the user from the request
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
            validated_data['is_staff'] = request.user.is_staff
        
        return super().create(validated_data)


class CustomerFeedbackSerializer(serializers.ModelSerializer):
    """
    Serializer for the CustomerFeedback model.
    """
    class Meta:
        model = CustomerFeedback
        fields = ['id', 'order', 'rating', 'comment', 'created_at']
        read_only_fields = ['id', 'created_at']
    
    def validate_order(self, value):
        """
        Validate that the order belongs to the user.
        """
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            if value.user != request.user:
                raise serializers.ValidationError("You can only provide feedback for your own orders.")
        return value
    
    def create(self, validated_data):
        # Get the user from the request
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            validated_data['user'] = request.user
        
        return super().create(validated_data)


class SupportTicketDetailSerializer(SupportTicketSerializer):
    """
    Detailed serializer for the SupportTicket model including responses.
    """
    responses = TicketResponseSerializer(many=True, read_only=True)
    
    class Meta(SupportTicketSerializer.Meta):
        fields = SupportTicketSerializer.Meta.fields + ['responses']
