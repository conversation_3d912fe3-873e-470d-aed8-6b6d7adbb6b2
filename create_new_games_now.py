#!/usr/bin/env python
"""
Create New Games Now
===================

This script immediately creates the Color Match and Memory Card games
in the database so they can be played.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType
from django.db import transaction


def create_new_games():
    """Create Color Match and Memory Card games"""
    print("🎮 Creating New Games")
    print("=" * 25)
    
    new_games = [
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember color sequences - Play vs AI or human opponents!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True,
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match', 
            'description': 'Find matching card pairs - Play vs AI or human opponents!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼'],
                'token_cost': 2,
                'win_reward': 5,
                'draw_reward': 2,
                'loss_penalty': 1
            }
        }
    ]
    
    created_count = 0
    
    with transaction.atomic():
        for game_data in new_games:
            try:
                game_type, created = GameType.objects.get_or_create(
                    name=game_data['name'],
                    defaults={
                        'display_name': game_data['display_name'],
                        'description': game_data['description'],
                        'rules': game_data['rules'],
                        'is_active': True
                    }
                )
                
                if created:
                    print(f"✅ Created: {game_type.display_name}")
                    created_count += 1
                else:
                    # Ensure it's active and properly configured
                    game_type.is_active = True
                    game_type.display_name = game_data['display_name']
                    game_type.description = game_data['description']
                    game_type.rules = game_data['rules']
                    game_type.save()
                    print(f"✅ Updated: {game_type.display_name}")
                    
            except Exception as e:
                print(f"❌ Error creating {game_data['name']}: {e}")
    
    return created_count


def verify_games():
    """Verify all games exist"""
    print(f"\n🔍 Verifying Games")
    print("=" * 20)
    
    required_games = ['rock_paper_scissors', 'number_guessing', 'tic_tac_toe', 'color_match', 'memory_card']
    
    for game_name in required_games:
        try:
            game = GameType.objects.get(name=game_name, is_active=True)
            print(f"✅ {game.display_name} (ID: {game.id})")
        except GameType.DoesNotExist:
            print(f"❌ {game_name}: Still missing")
    
    total_active = GameType.objects.filter(is_active=True).count()
    print(f"\nTotal active games: {total_active}")


def main():
    """Main function"""
    print("🚀 CREATE NEW GAMES")
    print("=" * 30)
    
    # Create the new games
    created = create_new_games()
    
    # Verify all games
    verify_games()
    
    print(f"\n🎉 COMPLETE!")
    print(f"Created: {created} new games")
    
    if created > 0:
        print(f"\n✅ Color Match and Memory Card games are now available!")
        print(f"✅ Try playing them on your gaming dashboard!")
    else:
        print(f"\n✅ All games already existed and are active!")
    
    print(f"\n💡 Next steps:")
    print(f"1. Go to your gaming dashboard")
    print(f"2. Click 'Play vs AI' on Color Match")
    print(f"3. Click 'Play vs AI' on Memory Card Match")
    print(f"4. Both should work without errors!")


if __name__ == '__main__':
    main()
