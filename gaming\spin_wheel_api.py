from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone
from django.db import transaction
from django.conf import settings
import random
import json
import logging

from .models import (
    SpinWheelReward,
    SpinWheelHistory,
    ScratchCard,
    SpinWheelSettings
)
from wallet.models import Wallet

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def spin_wheel_status(request):
    """
    Get user's spin wheel status and availability
    """
    try:
        user = request.user
        settings_obj = SpinWheelSettings.get_settings()
        
        # Check if spin wheel is active
        if not settings_obj.is_active:
            return Response({
                'success': False,
                'error': 'Spin wheel is currently disabled',
                'maintenance_mode': settings_obj.maintenance_mode,
                'maintenance_message': settings_obj.maintenance_message
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        # Check if user can spin
        can_spin = SpinWheelHistory.can_user_spin(user)
        last_spin = SpinWheelHistory.get_user_last_spin(user)
        next_spin_time = SpinWheelHistory.get_next_spin_time(user)
        
        # Get user's wallet balance
        wallet, _ = Wallet.objects.get_or_create(user=user)
        
        # Get available rewards for display
        active_rewards = SpinWheelReward.objects.filter(is_active=True).order_by('probability')
        
        rewards_data = []
        for reward in active_rewards:
            rewards_data.append({
                'id': str(reward.id),
                'name': reward.name,
                'type': reward.reward_type,
                'value': reward.value,
                'probability': reward.probability
            })
        
        return Response({
            'success': True,
            'can_spin': can_spin,
            'last_spin': last_spin.isoformat() if last_spin else None,
            'next_spin_time': next_spin_time.isoformat() if next_spin_time else None,
            'cooldown_hours': settings_obj.cooldown_hours,
            'wheel_segments': settings_obj.wheel_segments,
            'animation_duration': settings_obj.animation_duration,
            'current_balance': wallet.balance,
            'available_rewards': rewards_data,
            'total_spins_today': SpinWheelHistory.objects.filter(
                user=user,
                spin_timestamp__date=timezone.now().date()
            ).count()
        })
        
    except Exception as e:
        logger.error(f"Error getting spin wheel status for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get spin wheel status'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def spin_wheel(request):
    """
    Perform a spin and determine the reward
    """
    try:
        user = request.user
        settings_obj = SpinWheelSettings.get_settings()
        
        # Check if spin wheel is active
        if not settings_obj.is_active or settings_obj.maintenance_mode:
            return Response({
                'success': False,
                'error': 'Spin wheel is currently unavailable'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        
        # Check if user can spin (cooldown)
        if not SpinWheelHistory.can_user_spin(user):
            return Response({
                'success': False,
                'error': 'You can only spin once per day. Come back tomorrow!',
                'next_spin_time': SpinWheelHistory.get_next_spin_time(user).isoformat()
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)
        
        # Get client info for tracking
        ip_address = request.META.get('REMOTE_ADDR')
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        with transaction.atomic():
            # Determine the reward
            reward = _select_random_reward()
            if not reward:
                return Response({
                    'success': False,
                    'error': 'No rewards available'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            # Create spin history record
            spin_history = SpinWheelHistory.objects.create(
                user=user,
                reward=reward,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Process the reward
            reward_result = _process_reward(user, reward, spin_history)
            
            # Calculate wheel position (for animation)
            wheel_position = _calculate_wheel_position(reward, settings_obj.wheel_segments)
            
            return Response({
                'success': True,
                'spin_id': str(spin_history.id),
                'reward': {
                    'id': str(reward.id),
                    'name': reward.name,
                    'type': reward.reward_type,
                    'value': reward.value,
                    'description': reward_result.get('description', '')
                },
                'wheel_position': wheel_position,
                'animation_duration': settings_obj.animation_duration,
                'result': reward_result,
                'next_spin_time': SpinWheelHistory.get_next_spin_time(user).isoformat()
            })
            
    except Exception as e:
        logger.error(f"Error spinning wheel for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to spin wheel. Please try again.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def reveal_scratch_card(request):
    """
    Reveal a scratch card and process the hidden reward
    """
    try:
        spin_id = request.data.get('spin_id')
        if not spin_id:
            return Response({
                'success': False,
                'error': 'Spin ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the spin history
        try:
            spin_history = SpinWheelHistory.objects.get(
                id=spin_id,
                user=request.user
            )
        except SpinWheelHistory.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Invalid spin ID'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Check if this spin has a scratch card
        if not hasattr(spin_history, 'scratch_card'):
            return Response({
                'success': False,
                'error': 'No scratch card associated with this spin'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        scratch_card = spin_history.scratch_card
        
        # Check if already revealed
        if scratch_card.revealed:
            return Response({
                'success': False,
                'error': 'Scratch card already revealed',
                'reward_data': scratch_card.hidden_reward
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Reveal the card and process reward
        with transaction.atomic():
            reward_result = scratch_card.reveal_card()
            
            # Update spin history
            spin_history.scratch_card_revealed = True
            spin_history.reward_claimed = True
            spin_history.reward_claimed_at = timezone.now()
            spin_history.save()
            
            # Get updated wallet balance
            wallet = Wallet.objects.get(user=request.user)
            
            return Response({
                'success': True,
                'scratch_card_id': str(scratch_card.id),
                'reward_result': reward_result,
                'hidden_reward': scratch_card.hidden_reward,
                'new_balance': wallet.balance,
                'revealed_at': scratch_card.revealed_at.isoformat()
            })
            
    except Exception as e:
        logger.error(f"Error revealing scratch card for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to reveal scratch card'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def spin_history(request):
    """
    Get user's spin wheel history
    """
    try:
        user = request.user
        limit = int(request.GET.get('limit', 10))
        
        history = SpinWheelHistory.objects.filter(user=user)[:limit]
        
        history_data = []
        for spin in history:
            spin_data = {
                'id': str(spin.id),
                'reward_name': spin.reward.name,
                'reward_type': spin.reward.reward_type,
                'reward_value': spin.reward.value,
                'spin_timestamp': spin.spin_timestamp.isoformat(),
                'reward_claimed': spin.reward_claimed,
                'has_scratch_card': hasattr(spin, 'scratch_card'),
                'scratch_card_revealed': spin.scratch_card_revealed
            }
            
            if hasattr(spin, 'scratch_card') and spin.scratch_card.revealed:
                spin_data['scratch_card_reward'] = spin.scratch_card.hidden_reward
            
            history_data.append(spin_data)
        
        return Response({
            'success': True,
            'history': history_data,
            'total_spins': SpinWheelHistory.objects.filter(user=user).count()
        })
        
    except Exception as e:
        logger.error(f"Error getting spin history for user {request.user.id}: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get spin history'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _select_random_reward():
    """
    Select a random reward based on probabilities
    """
    active_rewards = list(SpinWheelReward.objects.filter(is_active=True))
    if not active_rewards:
        return None
    
    # Calculate total probability
    total_prob = sum(reward.probability for reward in active_rewards)
    if total_prob <= 0:
        return random.choice(active_rewards)
    
    # Normalize probabilities
    normalized_probs = [reward.probability / total_prob for reward in active_rewards]
    
    # Select based on probability
    return random.choices(active_rewards, weights=normalized_probs)[0]


def _process_reward(user, reward, spin_history):
    """
    Process the reward based on its type
    """
    if reward.reward_type == 'tokens':
        return _process_token_reward(user, reward)
    elif reward.reward_type == 'scratch_card':
        return _process_scratch_card_reward(user, reward, spin_history)
    elif reward.reward_type == 'discount':
        return _process_discount_reward(user, reward)
    else:
        return {
            'success': False,
            'description': 'Unknown reward type'
        }


def _process_token_reward(user, reward):
    """
    Process token reward
    """
    try:
        wallet, _ = Wallet.objects.get_or_create(user=user)
        wallet.add_tokens(
            amount=reward.value,
            transaction_type='spin_wheel',
            description=f"Spin wheel reward: {reward.name}"
        )
        
        return {
            'success': True,
            'type': 'tokens',
            'amount': reward.value,
            'new_balance': wallet.balance,
            'description': f"You won {reward.value} tokens!"
        }
    except Exception as e:
        logger.error(f"Error processing token reward: {str(e)}")
        return {
            'success': False,
            'description': 'Failed to process token reward'
        }


def _process_scratch_card_reward(user, reward, spin_history):
    """
    Create a scratch card with hidden reward
    """
    try:
        # Generate random hidden reward
        hidden_reward = _generate_scratch_card_reward()
        
        # Create scratch card
        scratch_card = ScratchCard.objects.create(
            spin_history=spin_history,
            card_type='token_reveal',
            hidden_reward=hidden_reward,
            card_design='default',
            scratch_areas=[
                {'x': 50, 'y': 50, 'width': 100, 'height': 50},
                {'x': 200, 'y': 50, 'width': 100, 'height': 50},
                {'x': 125, 'y': 150, 'width': 100, 'height': 50}
            ]
        )
        
        return {
            'success': True,
            'type': 'scratch_card',
            'scratch_card_id': str(scratch_card.id),
            'description': 'You won a scratch card! Reveal to see your prize.',
            'requires_action': True
        }
    except Exception as e:
        logger.error(f"Error creating scratch card: {str(e)}")
        return {
            'success': False,
            'description': 'Failed to create scratch card'
        }


def _process_discount_reward(user, reward):
    """
    Process discount reward
    """
    return {
        'success': True,
        'type': 'discount',
        'amount': reward.value,
        'description': f"You won {reward.value}% discount on your next purchase!"
    }


def _generate_scratch_card_reward():
    """
    Generate a random reward for scratch cards
    """
    settings_obj = SpinWheelSettings.get_settings()
    
    # Random token amount between min and max
    token_amount = random.randint(
        settings_obj.min_token_reward,
        settings_obj.max_token_reward
    )
    
    return {
        'type': 'tokens',
        'amount': token_amount,
        'message': f"Congratulations! You found {token_amount} tokens!"
    }


def _calculate_wheel_position(reward, segments):
    """
    Calculate wheel position for animation
    """
    # This would be based on the reward's position on the wheel
    # For now, return a random position within the reward's segment
    segment_angle = 360 / segments
    reward_segment = hash(str(reward.id)) % segments
    base_angle = reward_segment * segment_angle
    
    # Add some randomness within the segment
    random_offset = random.uniform(0, segment_angle)
    
    return (base_angle + random_offset) % 360
