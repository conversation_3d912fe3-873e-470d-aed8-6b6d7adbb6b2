{% extends "admin/edit_inline/tabular.html" %}
{% load i18n admin_urls %}

{% block field_sets %}
{{ block.super }}
{% if inline_admin_formset.formset.total_form_count > inline_admin_formset.formset.max_num %}
<div class="help">
    <p style="color: #666; margin-top: 10px; padding: 10px; background-color: #f8f8f8; border-radius: 4px;">
        <strong>{% trans "Note:" %}</strong> 
        {% blocktrans with count=inline_admin_formset.formset.total_form_count %}
        This product has {{ count }} variants, but only the first {{ inline_admin_formset.formset.max_num }} are shown here to improve performance.
        {% endblocktrans %}
        <a href="{% url 'admin:products_productvariant_changelist' %}?product__id__exact={{ inline_admin_formset.formset.instance.pk }}" class="viewlink">
            {% trans "View all variants" %}
        </a>
        {% if inline_admin_formset.formset.instance.printify_id %}
        | 
        <a href="{% url 'admin:sync-product-variants' inline_admin_formset.formset.instance.pk %}" class="viewlink">
            {% trans "Sync variants from Printify" %}
        </a>
        {% endif %}
    </p>
</div>
{% endif %}
{% endblock %}
