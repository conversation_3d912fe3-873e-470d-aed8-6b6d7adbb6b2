from django.shortcuts import render, redirect
from rest_framework import viewsets, generics, permissions, filters, status
from rest_framework.response import Response
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from .models import Category, Product, ProductImage, Review
from .serializers import (
    CategorySerializer,
    ProductListSerializer,
    ProductDetailSerializer,
    ProductCreateUpdateSerializer,
    ProductImageSerializer,
    ReviewSerializer
)
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.utils.text import slugify
from django.urls import reverse
import csv
import io


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Permission to allow only admin users to create, update or delete objects
    """
    def has_permission(self, request, view):
        # Allow GET, HEAD, OPTIONS requests (read-only)
        if request.method in permissions.SAFE_METHODS:
            return True
        # Require authentication and staff status for write operations
        return request.user and request.user.is_staff


class CategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Category CRUD operations
    """
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    permission_classes = [IsAdminOrReadOnly]
    lookup_field = 'slug'
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Product CRUD operations
    """
    queryset = Product.objects.filter(is_active=True)
    permission_classes = [IsAdminOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['categories__slug', 'is_featured']
    search_fields = ['name', 'description']
    ordering_fields = ['price', 'created_at', 'name']
    lookup_field = 'slug'

    def get_serializer_class(self):
        if self.action == 'list':
            return ProductListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ProductCreateUpdateSerializer
        return ProductDetailSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context

    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def review(self, request, slug=None):
        product = self.get_object()
        serializer = ReviewSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            # Check if user already reviewed this product
            if Review.objects.filter(user=request.user, product=product).exists():
                return Response(
                    {'detail': 'You have already reviewed this product.'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            serializer.save(product=product)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def token_eligible(self, request):
        """Get products that allow token discounts"""
        token_products = Product.objects.filter(
            is_active=True,
            allow_token_discount=True
        )

        # Apply same filtering as main queryset
        if 'categories__slug' in request.query_params:
            token_products = token_products.filter(
                categories__slug=request.query_params['categories__slug']
            )

        if 'search' in request.query_params:
            search_term = request.query_params['search']
            token_products = token_products.filter(
                name__icontains=search_term
            ) | token_products.filter(
                description__icontains=search_term
            )

        # Apply ordering
        ordering = request.query_params.get('ordering', '-created_at')
        token_products = token_products.order_by(ordering)

        # Paginate
        page = self.paginate_queryset(token_products)
        if page is not None:
            serializer = ProductListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(token_products, many=True, context={'request': request})
        return Response(serializer.data)


class ProductImageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ProductImage CRUD operations
    """
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    permission_classes = [IsAdminOrReadOnly]

    def get_queryset(self):
        if 'product_slug' in self.kwargs:
            return self.queryset.filter(product__slug=self.kwargs['product_slug'])
        return self.queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        return context

    def create(self, request, *args, **kwargs):
        """
        Custom create method to handle potential image validation errors
        """
        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)


def is_staff(user):
    return user.is_staff or user.is_superuser

@login_required
@user_passes_test(is_staff)
def upload_csv_view(request):
    if request.method == 'POST':
        csv_file = request.FILES.get('csv_file')

        if not csv_file:
            messages.error(request, 'Please upload a CSV file')
            return redirect('upload_csv')

        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'File is not a CSV')
            return redirect('upload_csv')

        # Read the CSV file
        try:
            csv_data = csv_file.read().decode('utf-8')
            io_string = io.StringIO(csv_data)
            reader = csv.reader(io_string)

            # Skip header row
            next(reader)

            success_count = 0
            error_count = 0
            error_messages = []

            for row_num, row in enumerate(reader, start=2):  # Start from 2 to account for header row
                try:
                    if len(row) < 5:
                        error_count += 1
                        error_messages.append(f"Row {row_num}: Not enough fields, expected 5 fields.")
                        continue

                    name, description, image_url, price, stock = row[:5]

                    # Validate data
                    if not name:
                        error_count += 1
                        error_messages.append(f"Row {row_num}: Name is required.")
                        continue

                    try:
                        price = float(price)
                    except ValueError:
                        error_count += 1
                        error_messages.append(f"Row {row_num}: Price must be a valid number.")
                        continue

                    try:
                        stock = int(stock)
                    except ValueError:
                        error_count += 1
                        error_messages.append(f"Row {row_num}: Stock must be a valid integer.")
                        continue

                    # Generate a slug from the name
                    slug = slugify(name)

                    # Check if product with this slug already exists
                    product, created = Product.objects.update_or_create(
                        slug=slug,
                        defaults={
                            'name': name,
                            'description': description,
                            'price': price,
                            'stock': stock,
                        }
                    )

                    # If image URL is provided, create a product image
                    if image_url:
                        # For simplicity, we're not downloading the image here,
                        # just storing the URL as is. In a real app, you would
                        # download and save the image file.
                        ProductImage.objects.create(
                            product=product,
                            image=image_url,
                            is_primary=True
                        )

                    success_count += 1

                except Exception as e:
                    error_count += 1
                    error_messages.append(f"Row {row_num}: {str(e)}")

            # Show a summary message
            messages.success(request, f"Successfully processed {success_count} products")

            if error_count > 0:
                messages.warning(request, f"Failed to process {error_count} products")
                for error in error_messages[:10]:  # Show only first 10 errors to avoid too long messages
                    messages.error(request, error)

                if len(error_messages) > 10:
                    messages.warning(request, f"... and {len(error_messages) - 10} more errors")

            return redirect('upload_csv')

        except Exception as e:
            messages.error(request, f"Error processing CSV file: {str(e)}")
            return redirect('upload_csv')

    return render(request, 'products/upload_csv.html')
