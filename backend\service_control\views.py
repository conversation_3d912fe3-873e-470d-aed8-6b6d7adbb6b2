from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import user_passes_test
from django.views.decorators.csrf import csrf_exempt
import json

from .models import ServiceControl


def is_admin_or_staff(user):
    """Check if user is admin or staff."""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


@user_passes_test(is_admin_or_staff)
def service_dashboard(request):
    """
    Render the service control dashboard.

    Args:
        request: The HTTP request

    Returns:
        HttpResponse: The rendered dashboard
    """
    service_control = ServiceControl.get_instance()

    context = {
        'service_control': service_control,
    }

    return render(request, 'service_control/dashboard.html', context)


@require_http_methods(["GET"])
@user_passes_test(is_admin_or_staff)
def service_status(request):
    """
    API endpoint to get the current status of services.

    Args:
        request: The HTTP request

    Returns:
        JsonResponse: JSON with service statuses
    """
    service_control = ServiceControl.get_instance()

    return JsonResponse({
        'celery_enabled': service_control.celery_enabled,
        'redis_enabled': service_control.redis_enabled,
        'celery_status': 'ON' if service_control.celery_enabled else 'OFF',
        'redis_status': 'ON' if service_control.redis_enabled else 'OFF',
        'updated_at': service_control.updated_at.isoformat(),
    })


@csrf_exempt
@require_http_methods(["POST"])
@user_passes_test(is_admin_or_staff)
def toggle_celery(request):
    """
    API endpoint to toggle Celery enabled/disabled.

    Args:
        request: The HTTP request

    Returns:
        JsonResponse: JSON with updated service status
    """
    service_control = ServiceControl.get_instance()
    service_control.celery_enabled = not service_control.celery_enabled
    service_control.save()

    status = "enabled" if service_control.celery_enabled else "disabled"

    return JsonResponse({
        'success': True,
        'message': f"Celery has been {status} successfully.",
        'celery_enabled': service_control.celery_enabled,
        'celery_status': 'ON' if service_control.celery_enabled else 'OFF',
    })


@csrf_exempt
@require_http_methods(["POST"])
@user_passes_test(is_admin_or_staff)
def toggle_redis(request):
    """
    API endpoint to toggle Redis enabled/disabled.

    Args:
        request: The HTTP request

    Returns:
        JsonResponse: JSON with updated service status
    """
    service_control = ServiceControl.get_instance()
    service_control.redis_enabled = not service_control.redis_enabled
    service_control.save()

    status = "enabled" if service_control.redis_enabled else "disabled"

    return JsonResponse({
        'success': True,
        'message': f"Redis has been {status} successfully.",
        'redis_enabled': service_control.redis_enabled,
        'redis_status': 'ON' if service_control.redis_enabled else 'OFF',
    })
