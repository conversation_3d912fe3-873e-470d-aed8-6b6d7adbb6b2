# Email Setup Guide for Production

This guide explains how to set up the email configuration for the PickMeTrend customer communication system in a production environment.

## Current Configuration

The system is currently configured to use the console email backend for development, which prints emails to the console instead of actually sending them. This is useful for testing but not suitable for production.

## Setting Up Gmail SMTP for Production

To use your `<EMAIL>` email address with Django, follow these steps:

### 1. Create an App Password for Gmail

Since Gmail uses 2-factor authentication, you'll need to create an app password:

1. Go to your Google Account settings: https://myaccount.google.com/
2. Select "Security" from the left menu
3. Under "Signing in to Google," select "2-Step Verification"
4. Scroll down and select "App passwords"
5. Select "Mail" as the app and "Other" as the device
6. Enter "PickMeTrend Django" as the name
7. Click "Generate"
8. Copy the 16-character password that appears

### 2. Update settings.py

Update the email settings in `settings.py` by uncommenting the SMTP configuration and adding your app password:

```python
# Email Configuration
# For production, use SMTP backend
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password-here'  # Replace with your app password
DEFAULT_FROM_EMAIL = 'PickMeTrend <<EMAIL>>'

# Comment out the console backend
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

### 3. Use Environment Variables for Security

For better security, store your email password in an environment variable instead of hardcoding it in the settings file:

1. Add this to your `settings.py`:

```python
import os

# Email Configuration
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_PASSWORD', '')
DEFAULT_FROM_EMAIL = 'PickMeTrend <<EMAIL>>'
```

2. Set the environment variable before running the server:

```bash
# Windows
set EMAIL_PASSWORD=your-app-password-here
python manage.py runserver

# Linux/Mac
export EMAIL_PASSWORD=your-app-password-here
python manage.py runserver
```

### 4. Test the Configuration

After setting up the SMTP configuration, test it by running:

```bash
python manage.py test_direct_email
```

## Troubleshooting

### Common Issues

1. **Authentication Error**: Make sure you're using an app password, not your regular Gmail password.

2. **Less Secure Apps**: If you're not using 2-factor authentication, you might need to allow "Less secure app access" in your Google Account settings, but this is not recommended for security reasons.

3. **Rate Limiting**: Gmail has sending limits. For high-volume emails, consider using a dedicated email service like SendGrid or Mailgun.

### Alternative Email Services

If Gmail SMTP doesn't meet your needs, consider these alternatives:

1. **SendGrid**:
   - Sign up at sendgrid.com
   - Update settings.py:
   ```python
   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
   EMAIL_HOST = 'smtp.sendgrid.net'
   EMAIL_PORT = 587
   EMAIL_USE_TLS = True
   EMAIL_HOST_USER = 'apikey'  # This is literally the string 'apikey'
   EMAIL_HOST_PASSWORD = 'your-sendgrid-api-key'
   DEFAULT_FROM_EMAIL = 'PickMeTrend <<EMAIL>>'
   ```

2. **Mailgun**:
   - Sign up at mailgun.com
   - Update settings.py:
   ```python
   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
   EMAIL_HOST = 'smtp.mailgun.org'
   EMAIL_PORT = 587
   EMAIL_USE_TLS = True
   EMAIL_HOST_USER = 'postmaster@your-mailgun-domain'
   EMAIL_HOST_PASSWORD = 'your-mailgun-password'
   DEFAULT_FROM_EMAIL = 'PickMeTrend <<EMAIL>>'
   ```

## Conclusion

Once you've set up the email configuration for production, all customer communication features will send actual emails instead of just printing them to the console. This includes:

- Order confirmation emails
- Support ticket confirmation emails
- Feedback request emails
- Admin notifications for new support tickets

Remember to keep your email credentials secure and never commit them to version control.
