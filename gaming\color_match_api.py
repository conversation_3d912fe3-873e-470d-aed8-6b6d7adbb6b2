"""
Color Match Game API
===================

API endpoints for Color Match game following the same pattern as <PERSON>ic <PERSON>
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from wallet.game_integration import check_game_eligibility, process_game_transaction, deduct_game_participation_fee
from .game_session_service import GameSessionService
import uuid


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def start_color_match_game(request):
    """
    Start a new Color Match game and deduct 2 tokens for participation
    """
    try:
        user = request.user
        difficulty = request.data.get('difficulty', 'medium')

        # Check if user can play games (requires 2 tokens)
        eligibility = check_game_eligibility(user.id)
        if not eligibility['can_play']:
            return Response({
                'error': eligibility['message'],
                'can_play': False,
                'balance': eligibility['balance']
            }, status=status.HTTP_400_BAD_REQUEST)

        # Generate game ID
        game_id = str(uuid.uuid4())

        # Deduct 2 tokens for participation
        participation_result = deduct_game_participation_fee(
            user_id=user.id,
            game_type='color_match',
            game_id=game_id
        )

        if not participation_result['success']:
            return Response({
                'error': participation_result['error'],
                'can_play': False,
                'balance': participation_result['balance']
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'success': True,
            'game_id': game_id,
            'difficulty': difficulty,
            'can_play': True,
            'balance': participation_result['remaining_balance'],
            'message': 'Color Match game started successfully (2 tokens deducted for participation)'
        })

    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def complete_color_match_game(request):
    """
    Complete a Color Match game and handle token transactions
    """
    try:
        user = request.user
        game_id = request.data.get('game_id')
        game_result = request.data.get('result')  # 'win', 'loss', 'draw'
        difficulty = request.data.get('difficulty', 'medium')
        
        if not game_id or not game_result:
            return Response({
                'error': 'Missing game_id or result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if game_result not in ['win', 'loss', 'draw']:
            return Response({
                'error': 'Invalid game result',
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Process token transaction
        transaction_result = process_game_transaction(
            user_id=user.id,
            game_type='color_match',
            game_result=game_result,
            difficulty=difficulty,
            game_id=game_id
        )
        
        if not transaction_result['success']:
            return Response({
                'error': transaction_result['message'],
                'success': False
            }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'success': True,
            'tokens_earned': transaction_result['tokens_earned'],
            'new_balance': transaction_result['new_balance'],
            'balance_in_inr': transaction_result['balance_in_inr'],
            'transaction_type': transaction_result['transaction_type'],
            'description': transaction_result['description'],
            'can_play_more': transaction_result['can_play_more']
        })
        
    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def forfeit_color_match_game(request):
    """
    Forfeit an active Color Match game
    """
    try:
        game_id = request.data.get('game_id')

        if not game_id:
            return Response({
                'success': False,
                'error': 'game_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Forfeit game session
        result = GameSessionService.forfeit_game_session(game_id)

        if result['success']:
            return Response({
                'success': True,
                'tokens_lost': abs(result['tokens_change']),
                'new_balance': result['new_balance'],
                'message': result['message']
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'error': result['error']
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_color_match_session(request, game_id):
    """
    Get details of a specific Color Match game session
    """
    try:
        result = GameSessionService.get_session_status(game_id)

        if result['success']:
            # Verify this is a Color Match session
            if result['game_type'] != 'color_match':
                return Response({
                    'success': False,
                    'error': 'Session is not a Color Match game'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_color_match_stats(request):
    """
    Get Color Match game statistics for the user
    """
    try:
        user = request.user
        
        # Import here to avoid circular imports
        from wallet.models import WalletTransaction
        from django.db.models import Q, Sum
        
        # Get Color Match transactions
        color_match_transactions = WalletTransaction.objects.filter(
            user=user,
            game_type='color_match'
        ).order_by('-created_at')
        
        # Calculate stats
        total_games = color_match_transactions.count()
        wins = color_match_transactions.filter(description__icontains='won').count()
        losses = color_match_transactions.filter(description__icontains='lost').count()
        draws = color_match_transactions.filter(description__icontains='draw').count()
        
        # Calculate total tokens earned (sum of positive amounts)
        total_tokens_earned = color_match_transactions.filter(
            amount__gt=0
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # Get current balance
        current_balance = user.wallet.balance
        balance_in_inr = user.wallet.balance_in_inr
        
        # Get recent games (last 10)
        recent_games = []
        for transaction in color_match_transactions[:10]:
            if 'won' in transaction.description.lower():
                result = 'win'
            elif 'lost' in transaction.description.lower():
                result = 'loss'
            elif 'draw' in transaction.description.lower():
                result = 'draw'
            else:
                result = 'unknown'
            
            recent_games.append({
                'date': transaction.created_at.strftime('%Y-%m-%d %H:%M'),
                'result': result,
                'tokens_earned': transaction.amount,
                'description': transaction.description
            })
        
        return Response({
            'success': True,
            'stats': {
                'total_games': total_games,
                'wins': wins,
                'losses': losses,
                'draws': draws,
                'total_tokens_earned': total_tokens_earned,
                'current_balance': current_balance,
                'balance_in_inr': balance_in_inr
            },
            'recent_games': recent_games
        })
        
    except Exception as e:
        return Response({
            'error': str(e),
            'success': False
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
