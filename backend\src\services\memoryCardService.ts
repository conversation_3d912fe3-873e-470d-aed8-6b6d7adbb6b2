import { api } from './api';

export interface MemoryCardGameStart {
  success: boolean;
  game_id: string;
  difficulty: string;
  can_play: boolean;
  balance: number;
  message: string;
  error?: string;
}

export interface MemoryCardGameComplete {
  success: boolean;
  tokens_earned: number;
  new_balance: number;
  balance_in_inr: number;
  transaction_type: string;
  description: string;
  can_play_more: boolean;
  error?: string;
}

export interface MemoryCardStats {
  success: boolean;
  stats: {
    total_games: number;
    wins: number;
    losses: number;
    draws: number;
    total_tokens_earned: number;
    current_balance: number;
    balance_in_inr: number;
  };
  recent_games: Array<{
    date: string;
    result: 'win' | 'loss' | 'draw';
    tokens_earned: number;
    description: string;
  }>;
  error?: string;
}

export const memoryCardService = {
  /**
   * Start a new Memory Card Match game
   */
  startGame: async (difficulty: string = 'medium'): Promise<MemoryCardGameStart> => {
    try {
      const response = await api.post('/api/gaming/memory-card/start/', {
        difficulty
      });
      return response.data;
    } catch (error: any) {
      console.error('Error starting Memory Card game:', error);
      return {
        success: false,
        game_id: '',
        difficulty,
        can_play: false,
        balance: 0,
        message: '',
        error: error.response?.data?.error || 'Failed to start game'
      };
    }
  },

  /**
   * Complete a Memory Card Match game and handle token transactions
   */
  completeGame: async (
    gameId: string,
    result: 'win' | 'loss' | 'draw',
    difficulty: string = 'medium'
  ): Promise<MemoryCardGameComplete> => {
    try {
      const response = await api.post('/api/gaming/memory-card/complete/', {
        game_id: gameId,
        result,
        difficulty
      });
      return response.data;
    } catch (error: any) {
      console.error('Error completing Memory Card game:', error);
      return {
        success: false,
        tokens_earned: 0,
        new_balance: 0,
        balance_in_inr: 0,
        transaction_type: '',
        description: '',
        can_play_more: false,
        error: error.response?.data?.error || 'Failed to complete game'
      };
    }
  },

  /**
   * Get Memory Card Match game statistics
   */
  getStats: async (): Promise<MemoryCardStats> => {
    try {
      const response = await api.get('/api/gaming/memory-card/stats/');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching Memory Card stats:', error);
      return {
        success: false,
        stats: {
          total_games: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          total_tokens_earned: 0,
          current_balance: 0,
          balance_in_inr: 0
        },
        recent_games: [],
        error: error.response?.data?.error || 'Failed to fetch stats'
      };
    }
  }
};
