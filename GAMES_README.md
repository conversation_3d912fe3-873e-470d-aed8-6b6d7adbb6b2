# 🎮 PickMeTrend Interactive Games

Complete implementation of **Color Match** and **Memory Card Match** games with AI opponents, React frontend, and Django backend.

## 🎯 Games Overview

### 🎨 Color Match Game
**Single-player vs AI with Stroop Effect**

- **Objective**: Tap the correct color name while dealing with confusing text colors
- **Stroop Effect**: Color names displayed in different colors to create confusion
- **Gameplay**: Player and AI take turns, 5 rounds total
- **AI Logic**: 80% accuracy as specified
- **Winner**: Highest score after 5 rounds

### 🧠 Memory Card Match Game  
**Single-player vs AI with Memory Simulation**

- **Objective**: Find matching pairs of animal cards
- **Gameplay**: Player and AI alternate turns flipping 2 cards
- **AI Logic**: Remembers seen cards after 2-3 turns, 70% memory accuracy
- **Winner**: Most matches found (8 pairs total)

## 💰 Token System

Both games use standardized token rewards:
- **Win**: +5 tokens
- **Draw**: +2 tokens  
- **Lose**: -1 token

## 🏗️ Architecture

### Frontend (React/TypeScript)
```
src/
├── components/games/
│   ├── ColorMatchGame.tsx      # Complete Color Match game
│   └── MemoryCardGame.tsx      # Complete Memory Card game
├── services/
│   ├── colorMatchService.ts    # Color Match API service
│   └── memoryCardService.ts    # Memory Card API service
└── components/gaming/
    └── GameLobby.tsx          # Updated with game routes
```

### Backend (Django)
```
gaming/
├── color_match_api.py         # Color Match endpoints
├── memory_card_api.py         # Memory Card endpoints
├── models.py                  # Game models
├── urls.py                    # Game routes
└── management/commands/
    └── setup_games.py         # Database setup
```

## 🔌 API Endpoints

### Color Match
- `POST /api/gaming/color-match/start/` - Start game
- `POST /api/gaming/color-match/complete/` - Complete game
- `GET /api/gaming/color-match/stats/` - Get statistics

### Memory Card
- `POST /api/gaming/memory-card/start/` - Start game
- `POST /api/gaming/memory-card/complete/` - Complete game
- `GET /api/gaming/memory-card/stats/` - Get statistics

## 🎮 Game Features

### Color Match Features
- ✅ **Stroop Effect**: Color names in different text colors
- ✅ **Turn-based**: Player vs AI alternating
- ✅ **Timer**: 10 seconds per turn
- ✅ **AI Opponent**: 80% accuracy simulation
- ✅ **Responsive UI**: Works on all devices
- ✅ **Animations**: Color flash effects
- ✅ **Score Tracking**: Real-time score display

### Memory Card Features
- ✅ **Card Grid**: 4x4 grid with 8 pairs
- ✅ **Turn-based**: Player vs AI alternating
- ✅ **AI Memory**: Remembers cards after 2-3 turns
- ✅ **Card Animations**: Smooth flip animations
- ✅ **Match Detection**: Automatic pair matching
- ✅ **Score Tracking**: Real-time match counting

## 🔐 Authentication & Security

- ✅ **Django Authentication**: Only logged-in users can play
- ✅ **Token Validation**: Checks user balance before games
- ✅ **Game Sessions**: Secure game ID generation
- ✅ **Transaction Logging**: All token changes recorded
- ✅ **User Validation**: Prevents unauthorized access

## 📱 Responsive Design

Both games feature:
- ✅ **Mobile-first**: Optimized for touch devices
- ✅ **Tablet Support**: Perfect for medium screens
- ✅ **Desktop**: Full desktop experience
- ✅ **Modern UI**: Gradient backgrounds, glassmorphism
- ✅ **Accessibility**: Keyboard navigation support

## 🚀 Deployment

### 1. Backend Setup
```bash
# Add games to database
python manage.py setup_games

# Verify games exist
python manage.py shell
>>> from gaming.models import GameType
>>> GameType.objects.filter(is_active=True).count()
5
```

### 2. Frontend Routes
Games are accessible at:
- `/gaming/color-match` - Color Match game
- `/gaming/memory-card` - Memory Card game
- `/gaming` - Game lobby with all games

### 3. Environment Variables
Ensure these are set:
```
DJANGO_SETTINGS_MODULE=dropshipping_backend.settings
DATABASE_URL=your_database_url
REDIS_URL=your_redis_url
```

## 🧪 Testing

### Manual Testing
1. **Login** to your account
2. **Navigate** to `/gaming`
3. **Click** "🤖 Play vs AI" on Color Match
4. **Verify** game loads and works
5. **Repeat** for Memory Card game

### API Testing
```bash
# Test Color Match API
curl -X POST /api/gaming/color-match/start/ \
  -H "Authorization: JWT your_token" \
  -H "Content-Type: application/json" \
  -d '{"difficulty": "medium"}'

# Test Memory Card API  
curl -X POST /api/gaming/memory-card/start/ \
  -H "Authorization: JWT your_token" \
  -H "Content-Type: application/json" \
  -d '{"difficulty": "medium"}'
```

## 🎯 Game Logic

### Color Match AI
```typescript
// 80% accuracy as specified
const isCorrect = Math.random() < 0.8;
const botChoice = isCorrect 
  ? targetColor 
  : randomColor();
```

### Memory Card AI
```typescript
// Memory simulation after 2-3 turns
if (turnCount >= 3 && hasMemory) {
  // 70% chance to use remembered cards
  if (Math.random() < 0.7) {
    return useMemoryCard();
  }
}
return randomCard();
```

## 📊 Database Schema

### Game Sessions
```sql
-- Game sessions stored with:
user_id          # Player
game_type        # 'color_match' or 'memory_card'  
result           # 'win', 'loss', 'draw'
token_delta      # Tokens gained/lost
timestamp        # When played
game_data        # Session details
```

### Token Transactions
```sql
-- All token changes logged:
user_id          # Player
amount           # +5, +2, or -1
transaction_type # 'game_win', 'game_draw', 'game_loss'
description      # Human readable description
game_type        # Which game
created_at       # Timestamp
```

## 🎉 Success Criteria

✅ **Both games fully playable**
✅ **AI opponents working**  
✅ **Token system integrated**
✅ **Authentication required**
✅ **Responsive design**
✅ **Game sessions stored**
✅ **Transaction logging**
✅ **Error handling**
✅ **Beautiful animations**
✅ **Production ready**

## 🔧 Troubleshooting

### Common Issues

**"Failed to start game"**
- Check user authentication
- Verify token balance > 0
- Check API endpoints are working

**Games not loading**
- Verify routes in App.tsx
- Check component imports
- Ensure frontend is deployed

**Token balance not updating**
- Check process_game_transaction function
- Verify wallet integration
- Check transaction logging

### Debug Commands
```bash
# Check game types
python manage.py shell -c "from gaming.models import GameType; print(GameType.objects.all())"

# Check user tokens
python manage.py shell -c "from django.contrib.auth.models import User; from wallet.models import Wallet; print([(u.username, u.wallet.balance) for u in User.objects.all()])"

# Test API endpoints
python test_new_game_endpoints.py
```

---

## 🎮 Ready to Play!

Both **Color Match** and **Memory Card Match** games are now complete with:
- ✅ Full React implementations
- ✅ AI opponents with realistic behavior  
- ✅ Beautiful, responsive UI
- ✅ Complete token integration
- ✅ Django authentication
- ✅ Game session storage
- ✅ Production-ready code

**The games are ready for players to enjoy!** 🚀
