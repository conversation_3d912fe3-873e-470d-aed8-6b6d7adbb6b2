# Generated by Django 5.0.2 on 2025-05-05 16:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0009_alter_productimage_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('variant_id', models.CharField(help_text='Variant ID from Printify (SKU)', max_length=100)),
                ('title', models.CharField(help_text="Variant title (e.g. 'Black / XL')", max_length=255)),
                ('color', models.CharField(blank=True, max_length=100, null=True)),
                ('size', models.CharField(blank=True, max_length=100, null=True)),
                ('gender', models.Char<PERSON>ield(blank=True, help_text='Gender targeting (Unisex, Men, Women, Kids)', max_length=50, null=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('is_available', models.BooleanField(default=True, help_text='Whether this variant is in stock')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='products.product')),
            ],
            options={
                'ordering': ['title'],
                'unique_together': {('product', 'variant_id')},
            },
        ),
    ]
