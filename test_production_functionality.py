#!/usr/bin/env python
"""
Test Production Functionality
=============================

This script tests all the key functionalities:
1. Signup bonus system
2. Token approval system  
3. Game types in database
4. Token discount system
5. API endpoints

Run this to verify everything is working correctly.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from django.urls import reverse
from rest_framework_simplejwt.tokens import RefreshToken
from wallet.models import Wallet, WalletTransaction, TokenRequest
from products.models import Product
from gaming.models import GameType
from orders.models import Cart, CartItem


def test_signup_bonus():
    """Test signup bonus functionality"""
    print("\n🪙 Testing Signup Bonus System")
    print("=" * 35)
    
    # Create a test user
    test_username = f"test_user_{int(os.urandom(4).hex(), 16)}"
    user = User.objects.create_user(
        username=test_username,
        email=f"{test_username}@test.com",
        password="testpass123"
    )
    
    # Check if wallet was created with signup bonus
    try:
        wallet = user.wallet
        signup_bonus = WalletTransaction.objects.filter(
            wallet=wallet,
            transaction_type='signup_bonus'
        ).first()
        
        if signup_bonus:
            print(f"✅ Signup bonus working: {wallet.balance} tokens")
            print(f"   Transaction: {signup_bonus.description}")
        else:
            print(f"❌ No signup bonus found for user {user.username}")
            print(f"   Wallet balance: {wallet.balance}")
            
    except Wallet.DoesNotExist:
        print(f"❌ No wallet created for user {user.username}")
    
    # Clean up
    user.delete()


def test_token_approval_system():
    """Test token approval system"""
    print("\n🔐 Testing Token Approval System")
    print("=" * 35)
    
    # Create test user and admin
    test_user = User.objects.create_user(
        username="test_token_user",
        email="<EMAIL>",
        password="testpass123"
    )
    
    admin_user = User.objects.create_superuser(
        username="test_admin",
        email="<EMAIL>",
        password="adminpass123"
    )
    
    try:
        # Create a token request
        token_request = TokenRequest.objects.create(
            user=test_user,
            reason="Testing token approval system"
        )
        
        print(f"✅ Token request created: ID {token_request.id}")
        print(f"   Status: {token_request.status}")
        print(f"   Reason: {token_request.reason}")
        
        # Test approval
        initial_balance = test_user.wallet.balance
        token_request.approve(
            admin_user=admin_user,
            tokens_to_grant=50,
            admin_notes="Test approval"
        )
        
        # Refresh wallet
        test_user.wallet.refresh_from_db()
        new_balance = test_user.wallet.balance
        
        print(f"✅ Token approval working:")
        print(f"   Initial balance: {initial_balance}")
        print(f"   New balance: {new_balance}")
        print(f"   Tokens granted: {new_balance - initial_balance}")
        
    except Exception as e:
        print(f"❌ Token approval error: {e}")
    
    # Clean up
    test_user.delete()
    admin_user.delete()


def test_game_types():
    """Test game types in database"""
    print("\n🎮 Testing Game Types")
    print("=" * 25)
    
    expected_games = ['rock_paper_scissors', 'number_guessing', 'tic_tac_toe']
    
    for game_name in expected_games:
        try:
            game = GameType.objects.get(name=game_name, is_active=True)
            print(f"✅ {game.display_name}: Active")
        except GameType.DoesNotExist:
            print(f"❌ {game_name}: Not found or inactive")
    
    total_games = GameType.objects.filter(is_active=True).count()
    print(f"\n📊 Total active games: {total_games}")


def test_token_discount_system():
    """Test token discount system"""
    print("\n💰 Testing Token Discount System")
    print("=" * 35)
    
    # Check if products have token discount enabled
    total_products = Product.objects.filter(is_active=True).count()
    token_enabled_products = Product.objects.filter(
        is_active=True,
        token_discount_available=True
    ).count()
    
    print(f"📊 Products with token discount: {token_enabled_products}/{total_products}")
    
    if token_enabled_products > 0:
        # Test a product's token discount calculation
        product = Product.objects.filter(
            is_active=True,
            token_discount_available=True
        ).first()
        
        if product:
            discount_info = product.calculate_max_token_discount(quantity=1)
            print(f"✅ Sample product: {product.name}")
            print(f"   Price: ₹{product.price}")
            print(f"   Max discount: ₹{discount_info['max_inr_discount']}")
            print(f"   Discount %: {discount_info['discount_percentage']}%")
            print(f"   Tokens needed: {discount_info['max_tokens']}")
    else:
        print("❌ No products have token discount enabled")


def test_api_endpoints():
    """Test key API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 30)
    
    client = Client()
    
    # Test game types endpoint (public)
    response = client.get('/api/gaming/game-types/')
    print(f"Game Types API: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        games = data.get('results', data) if isinstance(data, dict) else data
        print(f"   ✅ Found {len(games)} games via API")
    else:
        print(f"   ❌ Error: {response.content.decode()}")
    
    # Create test user for authenticated endpoints
    test_user = User.objects.create_user(
        username="api_test_user",
        email="<EMAIL>",
        password="testpass123"
    )
    
    # Generate JWT token
    refresh = RefreshToken.for_user(test_user)
    access_token = str(refresh.access_token)
    
    # Test wallet endpoint
    response = client.get(
        '/api/wallet/',
        HTTP_AUTHORIZATION=f'Bearer {access_token}'
    )
    print(f"Wallet API: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ Wallet balance: {data.get('balance', 0)} tokens")
    else:
        print(f"   ❌ Error: {response.content.decode()}")
    
    # Test token-eligible products endpoint
    response = client.get('/api/products/items/token_eligible/')
    print(f"Token Products API: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        products = data.get('results', [])
        print(f"   ✅ Found {len(products)} token-eligible products")
    else:
        print(f"   ❌ Error: {response.content.decode()}")
    
    # Clean up
    test_user.delete()


def test_cart_token_discount():
    """Test cart token discount functionality"""
    print("\n🛒 Testing Cart Token Discount")
    print("=" * 35)
    
    # Create test user
    test_user = User.objects.create_user(
        username="cart_test_user",
        email="<EMAIL>",
        password="testpass123"
    )
    
    # Ensure user has tokens
    test_user.wallet.add_tokens(
        amount=200,
        transaction_type='test_bonus',
        description='Test tokens for cart discount'
    )
    
    # Find a token-eligible product
    product = Product.objects.filter(
        is_active=True,
        token_discount_available=True
    ).first()
    
    if product:
        # Create cart and add product
        cart = Cart.objects.create(user=test_user)
        CartItem.objects.create(
            cart=cart,
            product=product,
            quantity=1
        )
        
        # Test token discount calculation
        discount_info = cart.calculate_token_discount_info(test_user.wallet.balance)
        
        print(f"✅ Cart token discount test:")
        print(f"   Product: {product.name} (₹{product.price})")
        print(f"   User tokens: {test_user.wallet.balance}")
        print(f"   Discount eligible: {discount_info['eligible']}")
        
        if discount_info['eligible']:
            print(f"   Max discount: ₹{discount_info['max_inr_discount']}")
            print(f"   Tokens needed: {discount_info['max_tokens_usable']}")
            print(f"   Final amount: ₹{discount_info['final_amount']}")
        else:
            print(f"   Reason: {discount_info.get('reason', 'Unknown')}")
    else:
        print("❌ No token-eligible products found for testing")
    
    # Clean up
    test_user.delete()


def main():
    """Run all tests"""
    print("🔍 PickMeTrend Production Functionality Test")
    print("=" * 45)
    print("Testing all key functionalities...")
    
    try:
        test_signup_bonus()
        test_token_approval_system()
        test_game_types()
        test_token_discount_system()
        test_api_endpoints()
        test_cart_token_discount()
        
        print(f"\n🎉 All tests completed!")
        print(f"📋 Check the results above to see what's working and what needs fixing.")
        
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
