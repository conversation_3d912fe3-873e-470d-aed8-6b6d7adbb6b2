#!/usr/bin/env python
"""
Local Test for AI Battle Creation
=================================

This script tests AI battle creation locally to identify the exact issue
before pushing to production.
"""

import os
import django
import sys

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from gaming.models import GameType, Battle
from wallet.models import Wallet
from django.test import Client, TestCase
import json


def setup_test_environment():
    """Set up a clean test environment"""
    print("🔧 Setting Up Test Environment")
    print("=" * 35)
    
    # Create test user
    test_user, created = User.objects.get_or_create(
        username='local_test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"✅ Created test user: {test_user.username}")
    else:
        print(f"✅ Using existing test user: {test_user.username}")
    
    # Ensure user has wallet with tokens
    wallet, created = Wallet.objects.get_or_create(user=test_user)
    if wallet.balance < 10:
        wallet.add_tokens(100, 'test', 'Local test tokens')
        print(f"✅ Added tokens, balance: {wallet.balance}")
    else:
        print(f"✅ User has {wallet.balance} tokens")
    
    # Create all required game types
    game_types = [
        {
            'name': 'rock_paper_scissors',
            'display_name': 'Rock Paper Scissors',
            'description': 'Classic battle game',
            'rules': {'choices': ['rock', 'paper', 'scissors']}
        },
        {
            'name': 'number_guessing',
            'display_name': 'Number Guessing Battle',
            'description': 'Guess the number game',
            'rules': {'min_number': 1, 'max_number': 100}
        },
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Memory color sequence game',
            'rules': {'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange']}
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs',
            'rules': {'card_pairs': 8, 'total_cards': 16}
        }
    ]
    
    created_games = []
    for game_data in game_types:
        game_type, created = GameType.objects.get_or_create(
            name=game_data['name'],
            defaults={
                'display_name': game_data['display_name'],
                'description': game_data['description'],
                'rules': game_data['rules'],
                'is_active': True
            }
        )
        
        if created:
            print(f"✅ Created game type: {game_type.display_name}")
        else:
            print(f"✅ Game type exists: {game_type.display_name}")
        created_games.append(game_type)
    
    return test_user, created_games


def test_game_engine():
    """Test GameEngine with new games"""
    print("\n🔧 Testing GameEngine")
    print("=" * 25)
    
    from gaming.game_logic import GameEngine
    
    test_games = ['color_match', 'memory_card']
    
    for game_name in test_games:
        print(f"\n🎮 Testing {game_name}:")
        
        try:
            # Test if game class exists
            game_class = GameEngine.get_game_class(game_name)
            if game_class:
                print(f"   ✅ Game class found: {game_class.__name__}")
            else:
                print(f"   ❌ Game class not found")
                continue
            
            # Test initial state creation
            initial_state = GameEngine.create_initial_state(game_name)
            print(f"   ✅ Initial state created: {list(initial_state.keys())}")
            
            # Test game over check
            is_over = GameEngine.is_game_over(game_name, initial_state)
            print(f"   ✅ Game over check: {is_over}")
            
        except Exception as e:
            print(f"   ❌ GameEngine error: {e}")
            import traceback
            traceback.print_exc()


def test_ai_bot():
    """Test AI bot with new games"""
    print("\n🤖 Testing AI Bot")
    print("=" * 20)
    
    from gaming.ai_bot import AIBot
    
    ai_bot = AIBot(difficulty='medium')
    
    test_cases = {
        'color_match': {
            'colors': ['red', 'blue', 'green'],
            'sequence': ['red', 'blue'],
            'player2_sequence': []
        },
        'memory_card': {
            'cards': ['🐶', '🐱', '🐶', '🐱'],
            'revealed': [False, False, False, False],
            'matched': [False, False, False, False],
            'selected_cards': []
        }
    }
    
    for game_name, test_state in test_cases.items():
        print(f"\n🎮 Testing AI for {game_name}:")
        
        try:
            move = ai_bot.get_move(game_name, test_state)
            print(f"   ✅ AI move generated: {move}")
        except Exception as e:
            print(f"   ❌ AI bot error: {e}")
            import traceback
            traceback.print_exc()


def test_matchmaking_service():
    """Test MatchmakingService directly"""
    print("\n🔄 Testing MatchmakingService")
    print("=" * 30)
    
    test_user = User.objects.get(username='local_test_user')
    
    try:
        from gaming.matchmaking import MatchmakingService
        import asyncio
        
        test_games = ['color_match', 'memory_card']
        
        for game_name in test_games:
            print(f"\n🎮 Testing MatchmakingService for {game_name}:")
            
            try:
                # Test async AI battle creation
                battle = asyncio.run(MatchmakingService.create_ai_battle(test_user, game_name))
                
                print(f"   ✅ Battle created successfully!")
                print(f"   Battle ID: {battle.id}")
                print(f"   Game type: {battle.game_type.name}")
                print(f"   Player: {battle.player1.username}")
                print(f"   AI battle: {battle.is_ai_battle}")
                
                # Clean up
                battle.delete()
                print(f"   🗑️ Cleaned up")
                
            except Exception as e:
                print(f"   ❌ MatchmakingService error: {e}")
                import traceback
                traceback.print_exc()
                
    except Exception as e:
        print(f"❌ Could not import MatchmakingService: {e}")
        import traceback
        traceback.print_exc()


def test_api_endpoint():
    """Test the actual API endpoint"""
    print("\n🌐 Testing API Endpoint")
    print("=" * 25)
    
    client = Client()
    test_user = User.objects.get(username='local_test_user')
    client.force_login(test_user)
    
    test_games = ['color_match', 'memory_card', 'rock_paper_scissors']
    
    for game_name in test_games:
        print(f"\n🎮 Testing API for {game_name}:")
        
        try:
            response = client.post('/api/gaming/create-ai-battle/', 
                                 data=json.dumps({'game_type': game_name}),
                                 content_type='application/json')
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 201:
                response_data = json.loads(response.content.decode())
                print(f"   ✅ SUCCESS!")
                print(f"   Battle ID: {response_data.get('battle_id')}")
                print(f"   Message: {response_data.get('message')}")
                
                # Clean up
                if 'battle_id' in response_data:
                    try:
                        battle = Battle.objects.get(id=response_data['battle_id'])
                        battle.delete()
                        print(f"   🗑️ Cleaned up")
                    except:
                        pass
                        
            else:
                print(f"   ❌ FAILED!")
                error_content = response.content.decode()
                print(f"   Error: {error_content}")
                
                try:
                    error_data = json.loads(error_content)
                    print(f"   Error details: {error_data}")
                except:
                    print(f"   Raw error: {error_content}")
                    
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            import traceback
            traceback.print_exc()


def test_wallet_eligibility():
    """Test wallet eligibility check"""
    print("\n💰 Testing Wallet Eligibility")
    print("=" * 30)
    
    test_user = User.objects.get(username='local_test_user')
    
    try:
        from wallet.game_integration import check_game_eligibility
        
        eligibility = check_game_eligibility(test_user.id)
        print(f"Can play: {eligibility['can_play']}")
        print(f"Balance: {eligibility['balance']}")
        print(f"Message: {eligibility['message']}")
        
        if not eligibility['can_play']:
            print("❌ User cannot play games - wallet issue!")
        else:
            print("✅ User can play games")
            
    except Exception as e:
        print(f"❌ Wallet eligibility error: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Main local test function"""
    print("🧪 LOCAL AI BATTLE TEST")
    print("=" * 50)
    print("Testing AI battle creation locally to identify issues...")
    print("=" * 50)
    
    try:
        # Setup test environment
        test_user, game_types = setup_test_environment()
        
        # Test wallet eligibility
        test_wallet_eligibility()
        
        # Test GameEngine
        test_game_engine()
        
        # Test AI bot
        test_ai_bot()
        
        # Test MatchmakingService
        test_matchmaking_service()
        
        # Test API endpoint
        test_api_endpoint()
        
        print("\n🎉 LOCAL TEST COMPLETE")
        print("=" * 30)
        print("Check the results above to identify the issue.")
        print("Once fixed locally, we can push to production.")
        
    except Exception as e:
        print(f"\n💥 LOCAL TEST FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
