# Generated by Django 5.0.2 on 2025-05-11 09:17

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceControl',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('celery_enabled', models.BooleanField(default=True, help_text='Enable or disable Celery task processing', verbose_name='Celery Enabled')),
                ('redis_enabled', models.BooleanField(default=True, help_text='Enable or disable Redis service', verbose_name='Redis Enabled')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Service Control',
                'verbose_name_plural': 'Service Controls',
            },
        ),
    ]
