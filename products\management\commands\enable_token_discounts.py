from django.core.management.base import BaseCommand
from products.models import Product


class Command(BaseCommand):
    help = 'Enable token discounts on products'

    def add_arguments(self, parser):
        parser.add_argument(
            '--percentage',
            type=int,
            default=20,
            help='Token discount percentage (default: 20%)',
        )
        parser.add_argument(
            '--max-amount',
            type=float,
            default=100.0,
            help='Maximum discount amount in INR (default: 100.0)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        percentage = options['percentage']
        max_amount = options['max_amount']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        self.stdout.write('🔍 Checking products for token discount settings...')
        
        # Get all active products
        products = Product.objects.filter(is_active=True)
        products_updated = 0
        products_already_enabled = 0
        
        for product in products:
            if product.token_discount_available:
                products_already_enabled += 1
                continue
            
            if not dry_run:
                # Enable token discount
                product.token_discount_available = True
                product.token_discount_percentage = percentage
                product.token_discount_max_amount = max_amount
                product.save()
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✅ Enabled {percentage}% token discount (max ₹{max_amount}) for: {product.name}'
                    )
                )
            else:
                self.stdout.write(
                    f'Would enable {percentage}% token discount (max ₹{max_amount}) for: {product.name}'
                )
            
            products_updated += 1
        
        # Summary
        self.stdout.write('\n📊 Summary:')
        self.stdout.write(f'Total active products: {products.count()}')
        self.stdout.write(f'Products already had token discount: {products_already_enabled}')
        self.stdout.write(f'Products updated: {products_updated}')
        self.stdout.write(f'Token discount settings: {percentage}% (max ₹{max_amount})')
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('\nThis was a dry run. Run without --dry-run to apply changes.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'\n🎉 Successfully enabled token discounts on {products_updated} products!')
            )
