#!/usr/bin/env python
"""
Test script for the complete token economy system
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from django.contrib.auth.models import User
from wallet.models import Wallet, TokenRequest
from wallet.game_integration import handle_game_loss, check_game_eligibility, get_user_token_status


def test_token_economy_system():
    print("🎮 Testing Complete Token Economy System")
    print("=" * 60)
    
    # Create test user
    user, created = User.objects.create_user(
        username='token_test_user',
        email='<EMAIL>',
        password='testpass123'
    ) if not User.objects.filter(username='token_test_user').exists() else (
        User.objects.get(username='token_test_user'), False
    )
    
    print(f"👤 Test User: {user.username} ({'created' if created else 'existing'})")
    
    # Test 1: Signup Bonus
    print("\n1️⃣ Testing Signup Bonus")
    wallet = user.wallet
    print(f"   Initial balance: {wallet.balance} tokens")
    
    if wallet.balance == 100:
        print("   ✅ Signup bonus working correctly!")
    else:
        print("   ⚠️ Signup bonus may not be working")
    
    # Test 2: Game Loss Penalty
    print("\n2️⃣ Testing Game Loss Penalty")
    initial_balance = wallet.balance
    
    # Simulate game loss
    result = handle_game_loss(user.id, game_id="test-game-123", description="Test game loss")
    print(f"   Game loss result: {result}")
    
    wallet.refresh_from_db()
    if result['success'] and wallet.balance == initial_balance - 1:
        print("   ✅ Game loss penalty working correctly!")
    else:
        print("   ❌ Game loss penalty failed")
    
    # Test 3: Zero Balance Lock
    print("\n3️⃣ Testing Zero Balance Lock")
    
    # Drain wallet to 0
    while wallet.balance > 0:
        try:
            wallet.spend_tokens(1, 'admin_adjustment', 'Test drain')
        except:
            break
    
    wallet.refresh_from_db()
    print(f"   Wallet balance: {wallet.balance} tokens")
    
    # Test game eligibility
    eligibility = check_game_eligibility(user.id)
    print(f"   Can play games: {eligibility['can_play']}")
    print(f"   Can use discounts: {wallet.can_use_token_discounts()}")
    
    if not eligibility['can_play'] and not wallet.can_use_token_discounts():
        print("   ✅ Zero balance lock working correctly!")
    else:
        print("   ❌ Zero balance lock failed")
    
    # Test 4: Token Refill Request
    print("\n4️⃣ Testing Token Refill Request")
    
    # Create token request
    token_request = TokenRequest.objects.create(
        user=user,
        message="Test refill request - wallet is empty"
    )
    print(f"   Token request created: {token_request.id}")
    
    # Check if user can request
    can_request, message = token_request.can_request_refill()
    print(f"   Can request refill: {can_request}")
    print(f"   Message: {message}")
    
    if can_request:
        print("   ✅ Token refill request validation working!")
    else:
        print("   ❌ Token refill request validation failed")
    
    # Test 5: Admin Approval
    print("\n5️⃣ Testing Admin Approval")
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    
    if created:
        admin_user.set_password('adminpass123')
        admin_user.save()
    
    # Approve the request
    try:
        token_request.approve(
            admin_user=admin_user,
            tokens_to_grant=50,
            admin_notes="Test approval"
        )
        
        wallet.refresh_from_db()
        print(f"   Request approved: {token_request.status}")
        print(f"   Tokens granted: {token_request.tokens_granted}")
        print(f"   New wallet balance: {wallet.balance}")
        
        if token_request.status == 'approved' and wallet.balance == 50:
            print("   ✅ Admin approval working correctly!")
        else:
            print("   ❌ Admin approval failed")
            
    except Exception as e:
        print(f"   ❌ Admin approval error: {str(e)}")
    
    # Test 6: Complete Token Status
    print("\n6️⃣ Testing Complete Token Status")
    
    status = get_user_token_status(user.id)
    print(f"   Token Status: {status}")
    
    if 'balance' in status and 'can_play_games' in status:
        print("   ✅ Token status API working correctly!")
    else:
        print("   ❌ Token status API failed")
    
    # Test 7: Game Win (for completeness)
    print("\n7️⃣ Testing Game Win Reward")
    
    initial_balance = wallet.balance
    wallet.add_tokens(5, 'game_win', 'Test game win')
    wallet.refresh_from_db()
    
    if wallet.balance == initial_balance + 5:
        print("   ✅ Game win reward working correctly!")
    else:
        print("   ❌ Game win reward failed")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Token Economy System Summary:")
    print(f"   👤 User: {user.username}")
    print(f"   💰 Final Balance: {wallet.balance} tokens")
    print(f"   📈 Total Earned: {wallet.total_earned} tokens")
    print(f"   📉 Total Spent: {wallet.total_spent} tokens")
    print(f"   🎮 Can Play Games: {wallet.can_play_games()}")
    print(f"   🛒 Can Use Discounts: {wallet.can_use_token_discounts()}")
    print(f"   📝 Token Requests: {TokenRequest.objects.filter(user=user).count()}")
    
    print("\n🎉 Token Economy System Test Complete!")
    print("\n🔗 API Endpoints Available:")
    print("   GET  /api/wallet/ - Get wallet details")
    print("   GET  /api/wallet/token-requests/ - List user's requests")
    print("   POST /api/wallet/token-requests/ - Create new request")
    print("   POST /api/wallet/check-refill-eligibility/ - Check eligibility")
    print("   GET  /api/wallet/admin/token-requests/ - Admin: List all requests")
    print("   POST /api/wallet/admin/token-requests/{id}/approve/ - Admin: Approve")
    print("   POST /api/wallet/admin/token-requests/{id}/reject/ - Admin: Reject")
    
    print("\n🎯 Features Implemented:")
    print("   ✅ 100 token signup bonus")
    print("   ✅ 1 token deduction on game loss")
    print("   ✅ Zero balance lock (games + discounts)")
    print("   ✅ Token refill request system")
    print("   ✅ Admin approval/rejection")
    print("   ✅ Complete API endpoints")
    print("   ✅ Django admin integration")
    print("   ✅ Frontend React component")


if __name__ == '__main__':
    test_token_economy_system()
