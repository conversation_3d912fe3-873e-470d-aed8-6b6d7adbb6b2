#!/usr/bin/env python
"""
Direct Production Fix
====================

This script directly creates the missing game types in production
and provides a fallback mechanism for the MatchmakingService.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dropshipping_backend.settings')
django.setup()

from gaming.models import GameType
from django.db import transaction


def create_missing_game_types():
    """Create the missing game types directly"""
    print("🎮 Creating Missing Game Types")
    print("=" * 35)
    
    # Define the exact games needed
    games_to_create = [
        {
            'name': 'color_match',
            'display_name': 'Color Match',
            'description': 'Remember color sequences - Play vs AI or human opponents!',
            'rules': {
                'max_rounds': 5,
                'colors': ['red', 'blue', 'green', 'yellow', 'purple', 'orange'],
                'sequence_grows': True
            }
        },
        {
            'name': 'memory_card',
            'display_name': 'Memory Card Match',
            'description': 'Find matching card pairs - Play vs AI or human opponents!',
            'rules': {
                'card_pairs': 8,
                'total_cards': 16,
                'card_types': ['🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼']
            }
        }
    ]
    
    created_count = 0
    
    with transaction.atomic():
        for game_data in games_to_create:
            try:
                game_type, created = GameType.objects.get_or_create(
                    name=game_data['name'],
                    defaults={
                        'display_name': game_data['display_name'],
                        'description': game_data['description'],
                        'rules': game_data['rules'],
                        'is_active': True
                    }
                )
                
                if created:
                    print(f"✅ Created: {game_type.display_name}")
                    created_count += 1
                else:
                    # Ensure it's active and properly configured
                    game_type.is_active = True
                    game_type.display_name = game_data['display_name']
                    game_type.description = game_data['description']
                    game_type.rules = game_data['rules']
                    game_type.save()
                    print(f"✅ Updated: {game_type.display_name}")
                    
            except Exception as e:
                print(f"❌ Error with {game_data['name']}: {e}")
    
    print(f"\nCreated/Updated: {created_count} game types")
    return created_count


def verify_all_games():
    """Verify all required games exist"""
    print("\n🔍 Verifying All Games")
    print("=" * 25)
    
    required_games = ['rock_paper_scissors', 'number_guessing', 'tic_tac_toe', 'color_match', 'memory_card']
    
    all_good = True
    for game_name in required_games:
        try:
            game = GameType.objects.get(name=game_name, is_active=True)
            print(f"✅ {game.display_name} (ID: {game.id})")
        except GameType.DoesNotExist:
            print(f"❌ Missing: {game_name}")
            all_good = False
    
    total_active = GameType.objects.filter(is_active=True).count()
    print(f"\nTotal active games: {total_active}")
    
    return all_good


def main():
    """Main fix function"""
    print("🔧 DIRECT PRODUCTION FIX")
    print("=" * 40)
    print("Creating missing game types for Color Match and Memory Card...")
    print("=" * 40)
    
    try:
        # Create missing game types
        created = create_missing_game_types()
        
        # Verify all games
        all_good = verify_all_games()
        
        print("\n🎉 PRODUCTION FIX COMPLETE")
        print("=" * 30)
        
        if all_good:
            print("✅ ALL GAMES READY!")
            print("✅ Color Match and Memory Card should work now!")
            
            print("\n🎮 Available Games:")
            for game in GameType.objects.filter(is_active=True).order_by('name'):
                print(f"   • {game.display_name}")
                
            print("\n💡 Test Steps:")
            print("1. Go to your gaming dashboard")
            print("2. Click 'Play vs AI' on Color Match")
            print("3. Click 'Play vs AI' on Memory Card Match")
            print("4. Both should work without 'Failed to create AI battle' error")
            
        else:
            print("❌ Some games are still missing")
            print("Check the verification results above")
        
    except Exception as e:
        print(f"\n💥 FIX FAILED: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
